<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Services\MidtransService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $midtransService;

    public function __construct(MidtransService $midtransService)
    {
        $this->midtransService = $midtransService;
        $this->middleware('auth');
    }

    /**
     * Create payment via Midtrans
     */
    public function createPayment(Request $request, Transaction $transaction)
    {
        try {
            Log::info('Creating payment for transaction', [
                'transaction_id' => $transaction->id,
                'user_id' => auth()->id(),
                'total_amount' => $transaction->total_amount,
                'method' => $request->method()
            ]);

            // Validate transaction
            if ($transaction->status !== 'pending') {
                $message = 'Transaksi sudah diproses sebelumnya';

                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => $message], 400);
                }

                return redirect()->route('transactions.show', $transaction)->with('error', $message);
            }

            if ($transaction->items->count() === 0) {
                $message = 'Transaksi tidak memiliki item';

                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => $message], 400);
                }

                return redirect()->route('transactions.show', $transaction)->with('error', $message);
            }

            // Create Snap token
            $result = $this->midtransService->createSnapToken($transaction);

            if ($result['success']) {
                Log::info('Payment gateway created successfully', [
                    'transaction_id' => $transaction->id,
                    'redirect_url' => $result['redirect_url']
                ]);

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Token pembayaran berhasil dibuat',
                        'snap_token' => $result['snap_token'],
                        'redirect_url' => $result['redirect_url']
                    ]);
                }

                // For GET request, redirect directly to Midtrans
                return redirect($result['redirect_url']);

            } else {
                $message = $result['message'];

                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => $message], 500);
                }

                return redirect()->route('transactions.show', $transaction)->with('error', $message);
            }

        } catch (\Exception $e) {
            Log::error('Payment creation failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'error_class' => get_class($e),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString()
            ]);

            // Handle specific error cases
            $errorMessage = $e->getMessage();

            if (strpos($errorMessage, 'Undefined array key') !== false) {
                $message = 'Terjadi kesalahan dalam data transaksi. Silakan coba lagi dengan produk yang berbeda.';
            } elseif (strpos($errorMessage, 'ServerKey') !== false || strpos($errorMessage, 'ClientKey') !== false) {
                $message = 'Konfigurasi payment gateway bermasalah. Silakan hubungi administrator.';
            } elseif (strpos($errorMessage, 'network') !== false || strpos($errorMessage, 'timeout') !== false) {
                $message = 'Koneksi ke payment gateway bermasalah. Silakan coba lagi.';
            } else {
                $message = 'Gagal membuat pembayaran. Silakan coba lagi.';
            }

            if ($request->expectsJson()) {
                return response()->json(['success' => false, 'message' => $message], 500);
            }

            return redirect()->route('transactions.show', $transaction)->with('error', $message);
        }
    }

    /**
     * Handle Midtrans notification
     */
    public function handleNotification(Request $request)
    {
        try {
            $notification = $request->all();

            Log::info('Received Midtrans notification', $notification);

            // Extract order ID and find transaction
            $orderId = $notification['order_id'] ?? null;
            if (!$orderId) {
                Log::error('No order_id in notification');
                return response('No order_id', 400);
            }

            // Extract transaction ID from order ID (format: UBI-{id}-{timestamp})
            if (preg_match('/UBI-(\d+)-\d+/', $orderId, $matches)) {
                $transactionId = $matches[1];
                $transaction = \App\Models\Transaction::find($transactionId);

                if (!$transaction) {
                    Log::error('Transaction not found', ['transaction_id' => $transactionId]);
                    return response('Transaction not found', 404);
                }

                // Update transaction status based on Midtrans status
                $transactionStatus = $notification['transaction_status'] ?? '';
                $fraudStatus = $notification['fraud_status'] ?? '';

                switch ($transactionStatus) {
                    case 'capture':
                        if ($fraudStatus == 'challenge') {
                            $transaction->update(['status' => 'pending', 'payment_status' => 'challenge']);
                        } else if ($fraudStatus == 'accept') {
                            $this->completeTransaction($transaction);
                        }
                        break;

                    case 'settlement':
                        $this->completeTransaction($transaction);
                        break;

                    case 'pending':
                        $transaction->update(['payment_status' => 'pending']);
                        break;

                    case 'deny':
                    case 'expire':
                    case 'cancel':
                        $transaction->update([
                            'status' => 'failed',
                            'payment_status' => 'failed'
                        ]);
                        break;
                }

                Log::info('Transaction status updated', [
                    'transaction_id' => $transaction->id,
                    'status' => $transaction->status,
                    'payment_status' => $transaction->payment_status
                ]);

                return response('OK', 200);
            }

            Log::error('Invalid order_id format', ['order_id' => $orderId]);
            return response('Invalid order_id', 400);

        } catch (\Exception $e) {
            Log::error('Notification handling failed', [
                'error' => $e->getMessage(),
                'notification' => $request->all()
            ]);

            return response('Error', 500);
        }
    }

    private function completeTransaction($transaction)
    {
        $transaction->update([
            'status' => 'completed',
            'payment_status' => 'paid',
            'amount_paid' => $transaction->total_amount, // Set amount paid for gateway payments
            'change_amount' => 0, // No change for gateway payments
            'payment_gateway_paid_at' => now()
        ]);

        // Update inventory
        foreach ($transaction->items as $item) {
            if ($item->processed_inventory_id) {
                $product = \App\Models\ProcessedInventory::find($item->processed_inventory_id);
                if ($product && $product->current_stock >= $item->quantity) {
                    $product->decrement('current_stock', $item->quantity);
                }
            } elseif ($item->product_id) {
                $product = \App\Models\OtherProduct::find($item->product_id);
                if ($product && $product->current_stock >= $item->quantity) {
                    $product->decrement('current_stock', $item->quantity);
                }
            }
        }
    }

    /**
     * Payment finish page
     */
    public function paymentFinish(Request $request)
    {
        $orderId = $request->get('order_id');
        $statusCode = $request->get('status_code');
        $transactionStatus = $request->get('transaction_status');

        Log::info('Payment finish callback received', [
            'order_id' => $orderId,
            'status_code' => $statusCode,
            'transaction_status' => $transactionStatus,
            'all_params' => $request->all()
        ]);

        $transaction = null;
        $isSuccess = false;

        // Try to find and update the transaction
        if ($orderId) {
            try {
                // Try multiple ways to find the transaction
                $transaction = Transaction::where('payment_gateway_order_id', $orderId)->first();

                // If not found by order_id, try to extract transaction ID from order_id
                if (!$transaction && preg_match('/ORDER-(\d+)-/', $orderId, $matches)) {
                    $transactionId = $matches[1];
                    $transaction = Transaction::find($transactionId);

                    // Update the order_id if found
                    if ($transaction) {
                        $transaction->update(['payment_gateway_order_id' => $orderId]);
                    }
                }

                if ($transaction) {
                    // Update transaction status based on payment result
                    if (in_array($transactionStatus, ['capture', 'settlement'])) {
                        $transaction->update([
                            'status' => 'completed',
                            'payment_status' => 'paid',
                            'payment_gateway_status' => $transactionStatus,
                            'payment_gateway_paid_at' => now()
                        ]);
                        $isSuccess = true;
                    } else {
                        $transaction->update([
                            'payment_gateway_status' => $transactionStatus
                        ]);
                    }

                    Log::info('Transaction updated from payment finish', [
                        'transaction_id' => $transaction->id,
                        'order_id' => $orderId,
                        'new_status' => $transaction->status
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Error updating transaction from payment finish', [
                    'order_id' => $orderId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        // Store success data in session for redirect
        if ($isSuccess && $transaction) {
            session([
                'payment_success' => true,
                'payment_data' => [
                    'order_id' => $orderId,
                    'status' => $transactionStatus,
                    'transaction' => $transaction->toArray(),
                    'invoice_number' => $transaction->invoice_number,
                    'total_amount' => $transaction->total_amount
                ]
            ]);

            // Redirect to success page to avoid loading screen
            return redirect()->route('payment.success');
        }

        // For other cases, show appropriate message
        return view('payment.finish', [
            'order_id' => $orderId,
            'status' => $transactionStatus,
            'success' => $isSuccess
        ]);
    }

    /**
     * Payment success page (redirect target)
     */
    public function paymentSuccess()
    {
        $paymentData = session('payment_data');

        if (!$paymentData) {
            // If no payment data, redirect to POS
            return redirect()->route('pos')->with('error', 'Data pembayaran tidak ditemukan');
        }

        // Clear session data
        session()->forget(['payment_success', 'payment_data']);

        return view('payment.success', [
            'success' => true,
            'order_id' => $paymentData['order_id'],
            'status' => $paymentData['status'],
            'transaction' => $paymentData['transaction'],
            'invoice_number' => $paymentData['invoice_number'],
            'total_amount' => $paymentData['total_amount']
        ]);
    }

    /**
     * Payment unfinish page
     */
    public function paymentUnfinish(Request $request)
    {
        $orderId = $request->get('order_id');
        
        Log::info('Payment unfinish', ['order_id' => $orderId]);
        
        return view('payment.unfinish', ['order_id' => $orderId]);
    }

    /**
     * Payment error page
     */
    public function paymentError(Request $request)
    {
        $orderId = $request->get('order_id');
        
        Log::info('Payment error', ['order_id' => $orderId]);
        
        return view('payment.error', ['order_id' => $orderId]);
    }

    /**
     * Check payment status from Midtrans
     */
    public function checkStatus(Transaction $transaction)
    {
        try {
            // Check status from Midtrans if order_id exists
            if ($transaction->order_id) {
                $orderId = $transaction->order_id;

                // Call Midtrans Status API
                \Midtrans\Config::$serverKey = config('midtrans.server_key');
                \Midtrans\Config::$isProduction = config('midtrans.is_production');

                try {
                    $status = \Midtrans\Transaction::status($orderId);

                    Log::info('Midtrans status check', [
                        'order_id' => $orderId,
                        'status' => $status
                    ]);

                    // Update transaction based on Midtrans status
                    $transactionStatus = is_object($status) ? $status->transaction_status : $status['transaction_status'];
                    $fraudStatus = is_object($status) ? ($status->fraud_status ?? '') : ($status['fraud_status'] ?? '');

                    switch ($transactionStatus) {
                        case 'capture':
                            if ($fraudStatus == 'accept') {
                                $this->completeTransaction($transaction);
                            }
                            break;
                        case 'settlement':
                            $this->completeTransaction($transaction);
                            break;
                        case 'pending':
                            $transaction->update(['payment_status' => 'pending']);
                            break;
                        case 'deny':
                        case 'expire':
                        case 'cancel':
                            $transaction->update([
                                'status' => 'failed',
                                'payment_status' => 'failed'
                            ]);
                            break;
                    }

                    // Refresh transaction data
                    $transaction->refresh();

                } catch (\Exception $midtransError) {
                    Log::error('Midtrans status check failed', [
                        'order_id' => $orderId,
                        'error' => $midtransError->getMessage()
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'transaction' => [
                    'id' => $transaction->id,
                    'status' => $transaction->status,
                    'payment_status' => $transaction->payment_status ?? 'pending',
                    'total_amount' => $transaction->total_amount
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengecek status pembayaran'
            ], 500);
        }
    }

    /**
     * Manual update payment status (for testing)
     */
    public function updateStatus(Request $request, Transaction $transaction)
    {
        try {
            $validated = $request->validate([
                'status' => 'required|in:pending,completed,failed',
                'payment_status' => 'required|in:pending,paid,failed'
            ]);

            if ($validated['status'] === 'completed' && $validated['payment_status'] === 'paid') {
                $this->completeTransaction($transaction);
            } else {
                $transaction->update($validated);
            }

            return redirect()->route('transactions.show', $transaction)
                ->with('success', 'Status pembayaran berhasil diperbarui');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Gagal memperbarui status: ' . $e->getMessage()]);
        }
    }
}
