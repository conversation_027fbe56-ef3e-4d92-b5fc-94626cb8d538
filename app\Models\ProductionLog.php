<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Auditable;

class ProductionLog extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'raw_inventory_id',
        'processed_inventory_id',
        'user_id',
        'raw_amount_used',
        'produced_amount',
        'raw_cost',
        'additional_cost',
        'total_cost',
        'cost_per_item',
        'raw_name',
        'processed_name',
    ];

    /**
     * Get the raw inventory item associated with this production log.
     */
    public function rawInventory(): BelongsTo
    {
        return $this->belongsTo(RawInventory::class);
    }

    /**
     * Get the processed inventory item associated with this production log.
     */
    public function processedInventory(): BelongsTo
    {
        return $this->belongsTo(ProcessedInventory::class);
    }

    /**
     * Get the user who executed this production process.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
