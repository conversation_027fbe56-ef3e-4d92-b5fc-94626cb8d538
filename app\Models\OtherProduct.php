<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Auditable;

class OtherProduct extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'name',
        'sku',
        'description',
        'purchase_price',
        'selling_price',
        'current_stock',
        'min_stock_threshold',
        'category',
        'unit',
        'supplier',
        'is_active',
        'notes',
        'image'
    ];

    protected $casts = [
        'purchase_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'current_stock' => 'integer',
        'min_stock_threshold' => 'integer',
        'is_active' => 'boolean'
    ];

    public function transactionItems()
    {
        return $this->hasMany(TransactionItem::class, 'product_id');
    }

    public function isLowStock()
    {
        return $this->current_stock <= $this->min_stock_threshold;
    }

    public function getProfit()
    {
        return $this->selling_price - $this->purchase_price;
    }

    public function getProfitMargin()
    {
        if ($this->selling_price > 0) {
            return ($this->getProfit() / $this->selling_price) * 100;
        }
        return 0;
    }

    public function getTotalValue()
    {
        return $this->current_stock * $this->purchase_price;
    }
}
