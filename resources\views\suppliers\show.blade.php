@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-truck"></i>
        <span>Detail Supplier</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Informasi Supplier</span>
                    <div>
                        <a href="{{ route('suppliers.edit', $supplier->id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('suppliers.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <i class="fas fa-info-circle me-2"></i> Data Supplier
                                </div>
                                <div class="card-body">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td style="width: 150px;"><strong>Nama</strong></td>
                                            <td>: {{ $supplier->name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Kontak Person</strong></td>
                                            <td>: {{ $supplier->contact_person ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>No. Telepon</strong></td>
                                            <td>: {{ $supplier->phone_number ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Email</strong></td>
                                            <td>: {{ $supplier->email ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Alamat</strong></td>
                                            <td>: {{ $supplier->address ?? '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status</strong></td>
                                            <td>: 
                                                @if($supplier->is_active)
                                                    <span class="badge bg-success">Aktif</span>
                                                @else
                                                    <span class="badge bg-danger">Tidak Aktif</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Catatan</strong></td>
                                            <td>: {{ $supplier->notes ?? '-' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header">
                                    <i class="fas fa-history me-2"></i> Histori Transaksi
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Tanggal</th>
                                                    <th>Nama Ubi</th>
                                                    <th>Jumlah (kg)</th>
                                                    <th>Total Biaya</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($supplier->rawInventory()->orderBy('purchase_date', 'desc')->limit(5)->get() as $item)
                                                <tr>
                                                    <td>{{ $item->purchase_date->format('d/m/Y') }}</td>
                                                    <td>{{ $item->name }}</td>
                                                    <td>{{ number_format($item->quantity_kg, 1, ',', '.') }} kg</td>
                                                    <td>Rp {{ number_format($item->total_cost, 0, ',', '.') }}</td>
                                                </tr>
                                                @empty
                                                <tr>
                                                    <td colspan="4" class="text-center">Belum ada transaksi</td>
                                                </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                        
                                        @if($supplier->rawInventory()->count() > 5)
                                            <div class="text-center mt-3">
                                                <a href="#" class="btn btn-sm btn-outline-primary">Lihat Semua Transaksi</a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <i class="fas fa-chart-pie me-2"></i> Statistik Pembelian
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 text-center mb-3">
                                            <div class="border rounded p-3">
                                                <h5>Total Transaksi</h5>
                                                <h3 class="text-primary">{{ $supplier->rawInventory()->count() }}</h3>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center mb-3">
                                            <div class="border rounded p-3">
                                                <h5>Total Pembelian</h5>
                                                <h3 class="text-success">{{ number_format($supplier->rawInventory()->sum('quantity_kg'), 1, ',', '.') }} kg</h3>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center mb-3">
                                            <div class="border rounded p-3">
                                                <h5>Total Pengeluaran</h5>
                                                <h3 class="text-danger">Rp {{ number_format($supplier->rawInventory()->sum('total_cost'), 0, ',', '.') }}</h3>
                                            </div>
                                        </div>
                                        <div class="col-md-3 text-center mb-3">
                                            <div class="border rounded p-3">
                                                <h5>Transaksi Terakhir</h5>
                                                <h3 class="text-info">
                                                    @php
                                                        $lastTransaction = $supplier->rawInventory()->orderBy('purchase_date', 'desc')->first();
                                                    @endphp
                                                    {{ $lastTransaction ? $lastTransaction->purchase_date->format('d/m/Y') : '-' }}
                                                </h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
