# 🗄️ ENTITY RELATIONSHIP DIAGRAM (ERD) - SISTEM UBI BAKAR CILEMBU

## ERD - Database Structure dan Relationships

```mermaid
erDiagram
    %% User Management
    USERS {
        bigint id PK
        string name
        string email UK
        timestamp email_verified_at
        string password
        enum role
        datetime last_activity
        string remember_token
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    %% Transaction Tables
    TRANSACTIONS {
        bigint id PK
        string invoice_number UK
        bigint user_id FK
        string customer_name
        string customer_phone
        decimal subtotal
        decimal tax
        decimal discount
        decimal total_amount
        decimal amount_paid
        decimal change_amount
        enum payment_method
        string payment_gateway
        string payment_gateway_transaction_id
        string payment_gateway_order_id
        string payment_gateway_status
        json payment_gateway_response
        string snap_token
        string snap_redirect_url
        datetime payment_gateway_paid_at
        datetime payment_gateway_expired_at
        enum status
        text notes
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    TRANSACTION_ITEMS {
        bigint id PK
        bigint transaction_id FK
        bigint product_id FK
        bigint processed_inventory_id FK
        string product_name
        decimal price
        int quantity
        decimal discount
        decimal subtotal
        timestamp created_at
        timestamp updated_at
    }
    
    %% Inventory Tables
    RAW_INVENTORY {
        bigint id PK
        string batch_number UK
        bigint supplier_id FK
        string supplier_name
        decimal quantity_kg
        decimal cost_per_kg
        decimal total_cost
        date purchase_date
        date expiry_date
        enum quality
        text notes
        decimal current_stock
        boolean is_active
        decimal min_stock_threshold
        string name
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    PROCESSED_INVENTORY {
        bigint id PK
        string batch_number UK
        bigint raw_inventory_id FK
        decimal quantity_processed_kg
        int quantity_produced
        decimal cost_per_unit
        decimal selling_price
        date production_date
        date expiry_date
        string product_type
        int current_stock
        boolean is_active
        text notes
        int min_stock_threshold
        string name
        decimal cost_per_item
        decimal raw_material_per_item
        int priority_level
        boolean needs_immediate_sale
        int days_until_expiry
        string recommended_market
        boolean notification_sent
        datetime last_notification_date
        string image
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    OTHER_PRODUCTS {
        bigint id PK
        string name
        string sku UK
        text description
        decimal purchase_price
        decimal selling_price
        int current_stock
        int min_stock_threshold
        string category
        string unit
        string supplier
        boolean is_active
        text notes
        string image
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    %% Supplier Table
    SUPPLIERS {
        bigint id PK
        string name
        string contact_person
        string phone_number
        string email
        text address
        text notes
        boolean is_active
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    %% Production Tables
    PRODUCTION_PROCESSES {
        bigint id PK
        string process_number UK
        bigint user_id FK
        date planned_date
        date actual_date
        enum status
        decimal total_cost
        text notes
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    PRODUCTION_LOGS {
        bigint id PK
        string batch_number
        bigint raw_inventory_id FK
        decimal raw_quantity_used
        int produced_quantity
        decimal production_cost
        date production_date
        enum quality_grade
        text notes
        timestamp created_at
        timestamp updated_at
    }
    
    %% Distribution Tables
    DISTRIBUTIONS {
        bigint id PK
        string distribution_number UK
        bigint user_id FK
        string market_name
        date distribution_date
        text notes
        enum status
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    DISTRIBUTION_ITEMS {
        bigint id PK
        bigint distribution_id FK
        bigint processed_inventory_id FK
        bigint other_product_id FK
        int quantity
        decimal price_per_item
        decimal total_price
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    %% Expense Table
    EXPENSES {
        bigint id PK
        string expense_number UK
        bigint user_id FK
        string category
        string description
        decimal amount
        date expense_date
        text notes
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }
    
    %% Audit Log Table
    AUDIT_LOGS {
        bigint id PK
        bigint user_id FK
        string action
        string model_type
        bigint model_id
        json old_values
        json new_values
        string ip_address
        string user_agent
        timestamp created_at
    }
    
    %% Cache Tables (Laravel)
    CACHE {
        string key PK
        text value
        int expiration
    }
    
    CACHE_LOCKS {
        string key PK
        string owner
        int expiration
    }
    
    %% Job Tables (Laravel)
    JOBS {
        bigint id PK
        string queue
        longtext payload
        tinyint attempts
        int reserved_at
        int available_at
        int created_at
    }
    
    JOB_BATCHES {
        string id PK
        string name
        int total_jobs
        int pending_jobs
        int failed_jobs
        text failed_job_ids
        text options
        int cancelled_at
        int created_at
        int finished_at
    }
    
    FAILED_JOBS {
        bigint id PK
        string uuid UK
        text connection
        text queue
        longtext payload
        longtext exception
        timestamp failed_at
    }
    
    %% Relationships
    USERS ||--o{ TRANSACTIONS : "user_id"
    USERS ||--o{ PRODUCTION_PROCESSES : "user_id"
    USERS ||--o{ DISTRIBUTIONS : "user_id"
    USERS ||--o{ EXPENSES : "user_id"
    USERS ||--o{ AUDIT_LOGS : "user_id"
    
    TRANSACTIONS ||--o{ TRANSACTION_ITEMS : "transaction_id"
    
    PROCESSED_INVENTORY ||--o{ TRANSACTION_ITEMS : "processed_inventory_id"
    OTHER_PRODUCTS ||--o{ TRANSACTION_ITEMS : "product_id"
    
    SUPPLIERS ||--o{ RAW_INVENTORY : "supplier_id"
    RAW_INVENTORY ||--o{ PROCESSED_INVENTORY : "raw_inventory_id"
    RAW_INVENTORY ||--o{ PRODUCTION_LOGS : "raw_inventory_id"
    
    DISTRIBUTIONS ||--o{ DISTRIBUTION_ITEMS : "distribution_id"
    PROCESSED_INVENTORY ||--o{ DISTRIBUTION_ITEMS : "processed_inventory_id"
    OTHER_PRODUCTS ||--o{ DISTRIBUTION_ITEMS : "other_product_id"
    
    PRODUCTION_PROCESSES ||--o{ PRODUCTION_LOGS : "process_id"
```

## 📋 **Deskripsi ERD**

### **🔑 Primary Keys & Foreign Keys**
- Semua tabel menggunakan `bigint id` sebagai Primary Key
- Foreign Key relationships menggunakan konvensi Laravel (`table_id`)
- Unique constraints pada field penting (email, batch_number, invoice_number)

### **📊 Tabel Utama**

#### **👥 USERS**
- Menyimpan data user dengan multi-role system
- Soft deletes untuk data integrity
- Last activity tracking

#### **💰 TRANSACTIONS & TRANSACTION_ITEMS**
- Master-detail relationship untuk transaksi
- Support multiple payment methods termasuk payment gateway
- Comprehensive payment gateway integration fields

#### **📦 Inventory Tables**
- **RAW_INVENTORY**: Bahan mentah dengan supplier tracking
- **PROCESSED_INVENTORY**: Produk jadi dengan expiry management
- **OTHER_PRODUCTS**: Produk lainnya dengan category management

#### **🏭 Production Tables**
- **PRODUCTION_PROCESSES**: Master production planning
- **PRODUCTION_LOGS**: Detail production per batch

#### **🚚 Distribution Tables**
- **DISTRIBUTIONS**: Master distribusi ke pasar
- **DISTRIBUTION_ITEMS**: Detail item yang didistribusikan

#### **🏢 Supporting Tables**
- **SUPPLIERS**: Master supplier data
- **EXPENSES**: Operational expense tracking
- **AUDIT_LOGS**: Complete activity logging

### **🔗 Key Relationships**

1. **User → Transactions**: One-to-Many (user dapat membuat banyak transaksi)
2. **Transaction → Transaction Items**: One-to-Many (satu transaksi banyak item)
3. **Raw Inventory → Processed Inventory**: One-to-Many (satu batch raw bisa jadi banyak processed)
4. **Supplier → Raw Inventory**: One-to-Many (satu supplier banyak raw material)
5. **Distribution → Distribution Items**: One-to-Many (satu distribusi banyak item)

### **📈 Database Features**

- **Soft Deletes**: Pada semua tabel penting untuk data recovery
- **Timestamps**: Created_at dan updated_at pada semua tabel
- **JSON Fields**: Untuk payment gateway response dan audit log values
- **Enum Fields**: Untuk status, role, quality grade
- **Decimal Fields**: Untuk monetary values dengan precision
- **Index Optimization**: Pada foreign keys dan frequently queried fields
