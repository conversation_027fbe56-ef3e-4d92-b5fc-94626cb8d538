<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt #{{ $transaction->invoice_number }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 0;
            padding: 10px;
            width: 80mm;
        }
        .receipt {
            width: 100%;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .header {
            margin-bottom: 10px;
            text-align: center;
        }
        .header h2 {
            margin: 0;
            padding: 0;
            font-size: 16px;
        }
        .header p {
            margin: 0;
            padding: 0;
        }
        .divider {
            border-top: 1px dashed #000;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table th, table td {
            padding: 3px 0;
        }
        .total {
            font-weight: bold;
        }
        .footer {
            margin-top: 10px;
            text-align: center;
        }
        
        @media print {
            body {
                width: 80mm;
                margin: 0;
                padding: 5px;
            }
            .no-print {
                display: none;
            }
            .receipt {
                page-break-inside: avoid;
            }
            @page {
                size: 80mm auto;
                margin: 0;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="header">
            <h2>UBI BAKAR CILEMBU</h2>
            <p>Jl. Contoh No. 123, Kota, Indonesia</p>
            <p>Telp: 081234567890</p>
        </div>
        
        <div class="divider"></div>
        
        <table>
            <tr>
                <td>No. Invoice</td>
                <td>:</td>
                <td>{{ $transaction->invoice_number }}</td>
            </tr>
            <tr>
                <td>Tanggal</td>
                <td>:</td>
                <td>{{ $transaction->created_at->format('d/m/Y H:i') }}</td>
            </tr>
            <tr>
                <td>Kasir</td>
                <td>:</td>
                <td>{{ $transaction->user->name ?? 'Admin' }}</td>
            </tr>
            @if($transaction->customer_name)
            <tr>
                <td>Customer</td>
                <td>:</td>
                <td>{{ $transaction->customer_name }}</td>
            </tr>
            @endif
        </table>
        
        <div class="divider"></div>
        
        <table>
            <tr>
                <th class="text-left">Item</th>
                <th class="text-right">Qty</th>
                <th class="text-right">Harga</th>
                <th class="text-right">Subtotal</th>
            </tr>
            
            @foreach($transaction->items as $item)
            <tr>
                <td>{{ $item->product_name }}</td>
                <td class="text-right">{{ $item->quantity }}</td>
                <td class="text-right">{{ number_format($item->price, 0, ',', '.') }}</td>
                <td class="text-right">{{ number_format($item->subtotal, 0, ',', '.') }}</td>
            </tr>
            @endforeach
        </table>
        
        <div class="divider"></div>
        
        <table>
            <tr>
                <td>Total</td>
                <td class="text-right total">Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</td>
            </tr>

            @if($transaction->payment_method === 'gateway')
                <!-- Payment Gateway Information -->
                <tr>
                    <td>Metode Bayar</td>
                    <td class="text-right">Payment Gateway</td>
                </tr>
                <tr>
                    <td>Provider</td>
                    <td class="text-right">Midtrans</td>
                </tr>
                @php
                    $paymentStatus = $transaction->payment_status ?? 'pending';
                @endphp
                <tr>
                    <td>Status</td>
                    <td class="text-right">
                        @if($paymentStatus === 'paid')
                            SELESAI
                        @elseif($paymentStatus === 'pending')
                            MENUNGGU
                        @elseif($paymentStatus === 'failed')
                            GAGAL
                        @elseif($paymentStatus === 'cancelled')
                            BATAL
                        @elseif($paymentStatus === 'expired')
                            KEDALUWARSA
                        @else
                            {{ strtoupper($paymentStatus) }}
                        @endif
                    </td>
                </tr>
                @if($paymentStatus === 'paid' && $transaction->payment_gateway_paid_at)
                <tr>
                    <td>Dibayar</td>
                    <td class="text-right">{{ $transaction->payment_gateway_paid_at->format('d/m/Y H:i') }}</td>
                </tr>
                @endif
                @if($transaction->payment_gateway_transaction_id)
                <tr>
                    <td>Ref ID</td>
                    <td class="text-right">{{ substr($transaction->payment_gateway_transaction_id, -8) }}</td>
                </tr>
                @endif
            @else
                <!-- Manual Payment Information -->
                <tr>
                    <td>Metode Bayar</td>
                    <td class="text-right">
                        @switch($transaction->payment_method)
                            @case('cash')
                                Tunai
                                @break
                            @case('transfer')
                                Transfer Bank
                                @break
                            @case('debit')
                                Kartu Debit
                                @break
                            @case('credit')
                                Kartu Kredit
                                @break
                            @default
                                {{ ucfirst($transaction->payment_method) }}
                        @endswitch
                    </td>
                </tr>
                @if($transaction->amount_paid)
                <tr>
                    <td>Bayar</td>
                    <td class="text-right">Rp {{ number_format($transaction->amount_paid, 0, ',', '.') }}</td>
                </tr>
                @if($transaction->change_amount > 0)
                <tr>
                    <td>Kembali</td>
                    <td class="text-right">Rp {{ number_format($transaction->change_amount, 0, ',', '.') }}</td>
                </tr>
                @endif
                @endif
                <tr>
                    <td>Status</td>
                    <td class="text-right">LUNAS</td>
                </tr>
            @endif
        </table>
        
        <div class="divider"></div>
        
        <div class="footer">
            <p>Terima Kasih Atas Kunjungan Anda</p>
            <p>Selamat Menikmati!</p>
            @if($transaction->note)
            <div class="divider"></div>
            <p><strong>Catatan:</strong></p>
            <p>{{ $transaction->note }}</p>
            @endif
            <div class="divider"></div>
            <p style="font-size: 10px;">Dicetak: {{ now()->format('d/m/Y H:i:s') }}</p>
        </div>
        
        <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="printReceipt()" style="padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">🖨️ Print Receipt</button>
            <button onclick="saveToPdf()" style="padding: 10px 20px; margin: 5px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">📄 Save as PDF</button>
            <a href="{{ route('transactions.export-pdf', $transaction) }}" style="display: inline-block; padding: 10px 20px; margin: 5px; background: #17a2b8; color: white; text-decoration: none; border-radius: 4px;">⬇️ Download PDF</a>
            <button onclick="window.close()" style="padding: 10px 20px; margin: 5px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">❌ Close</button>
        </div>
    </div>
    
    <script>
        // Optional: Auto-print can be enabled by adding ?autoprint=1 to URL
        window.onload = function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('autoprint') === '1') {
                setTimeout(function() {
                    window.print();
                }, 500);
            }
        };

        // Function to manually trigger print
        function printReceipt() {
            window.print();
        }

        // Function to save as PDF using browser print dialog
        function saveToPdf() {
            // Hide the buttons temporarily
            const buttons = document.querySelector('.no-print');
            buttons.style.display = 'none';

            // Trigger print dialog (user can choose "Save as PDF")
            window.print();

            // Show buttons again after a delay
            setTimeout(() => {
                buttons.style.display = 'block';
            }, 1000);
        }
    </script>
</body>
</html> 