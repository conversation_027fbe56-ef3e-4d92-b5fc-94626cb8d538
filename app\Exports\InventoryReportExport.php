<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

class InventoryReportExport implements WithMultipleSheets
{
    protected $rawInventory;
    protected $processedInventory;
    protected $otherProducts;
    
    public function __construct($rawInventory, $processedInventory, $otherProducts)
    {
        $this->rawInventory = $rawInventory;
        $this->processedInventory = $processedInventory;
        $this->otherProducts = $otherProducts;
    }
    
    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [
            new RawInventorySheet($this->rawInventory),
            new ProcessedInventorySheet($this->processedInventory),
            new OtherProductsSheet($this->otherProducts)
        ];
        
        return $sheets;
    }
}

class RawInventorySheet implements FromCollection, WithHeadings, WithTitle, WithStyles
{
    protected $rawInventory;
    
    public function __construct($rawInventory)
    {
        $this->rawInventory = $rawInventory;
    }
    
    public function collection()
    {
        $data = new Collection();
        
        foreach ($this->rawInventory as $item) {
            $data->push([
                $item->name,
                $item->batch_number,
                $item->current_stock,
                number_format($item->cost_per_kg, 0, ',', '.'),
                number_format($item->total_value, 0, ',', '.')
            ]);
        }
        
        return $data;
    }
    
    public function headings(): array
    {
        return [
            'Nama Produk',
            'Nomor Batch',
            'Stok (kg)',
            'Harga per kg',
            'Nilai Total'
        ];
    }
    
    public function title(): string
    {
        return 'Inventaris Ubi Mentah';
    }
    
    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            'A' => ['autosize' => true],
            'B' => ['autosize' => true],
            'C' => ['autosize' => true],
            'D' => ['autosize' => true],
            'E' => ['autosize' => true],
        ];
    }
}

class ProcessedInventorySheet implements FromCollection, WithHeadings, WithTitle, WithStyles
{
    protected $processedInventory;
    
    public function __construct($processedInventory)
    {
        $this->processedInventory = $processedInventory;
    }
    
    public function collection()
    {
        $data = new Collection();
        
        foreach ($this->processedInventory as $item) {
            $data->push([
                $item->name,
                $item->batch_number,
                $item->current_stock,
                number_format($item->cost_per_unit, 0, ',', '.'),
                number_format($item->total_value, 0, ',', '.')
            ]);
        }
        
        return $data;
    }
    
    public function headings(): array
    {
        return [
            'Nama Produk',
            'Nomor Batch',
            'Stok (pcs)',
            'Harga Modal per unit',
            'Nilai Total'
        ];
    }
    
    public function title(): string
    {
        return 'Inventaris Ubi Bakar';
    }
    
    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            'A' => ['autosize' => true],
            'B' => ['autosize' => true],
            'C' => ['autosize' => true],
            'D' => ['autosize' => true],
            'E' => ['autosize' => true],
        ];
    }
}

class OtherProductsSheet implements FromCollection, WithHeadings, WithTitle, WithStyles
{
    protected $otherProducts;
    
    public function __construct($otherProducts)
    {
        $this->otherProducts = $otherProducts;
    }
    
    public function collection()
    {
        $data = new Collection();
        
        foreach ($this->otherProducts as $item) {
            $data->push([
                $item->name,
                $item->current_stock,
                number_format($item->cost_price, 0, ',', '.'),
                number_format($item->total_value, 0, ',', '.')
            ]);
        }
        
        return $data;
    }
    
    public function headings(): array
    {
        return [
            'Nama Produk',
            'Stok',
            'Harga Modal',
            'Nilai Total'
        ];
    }
    
    public function title(): string
    {
        return 'Produk Lainnya';
    }
    
    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
            'A' => ['autosize' => true],
            'B' => ['autosize' => true],
            'C' => ['autosize' => true],
            'D' => ['autosize' => true],
        ];
    }
}
