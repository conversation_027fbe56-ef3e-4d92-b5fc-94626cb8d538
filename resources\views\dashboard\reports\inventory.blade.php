@extends('layouts.app')

@section('content')
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-boxes"></i> Laporan Inventori</h1>
        <div>
            <a href="{{ route('reports.export', ['type' => 'inventory']) }}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Export Excel
            </a>
            <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card text-center mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5>Total Nilai Inventori Ubi Mentah</h5>
                </div>
                <div class="card-body">
                    <h2 class="mb-0">Rp {{ number_format($totalRawValue, 0, ',', '.') }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card text-center mb-4">
                <div class="card-header bg-primary text-white">
                    <h5>Total Nilai Inventori Ubi Bakar</h5>
                </div>
                <div class="card-body">
                    <h2 class="mb-0">Rp {{ number_format($totalProcessedValue, 0, ',', '.') }}</h2>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-chart-pie"></i> Distribusi Nilai Inventori</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="inventoryDistributionChart" width="400" height="300"></canvas>
                        </div>
                        <div class="col-md-6 d-flex align-items-center">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Jenis Inventori</th>
                                        <th class="text-end">Nilai (Rp)</th>
                                        <th class="text-end">Persentase</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                        $totalValue = $totalRawValue + $totalProcessedValue;
                                        $rawPercentage = $totalValue > 0 ? round(($totalRawValue / $totalValue) * 100) : 0;
                                        $processedPercentage = $totalValue > 0 ? round(($totalProcessedValue / $totalValue) * 100) : 0;
                                    @endphp
                                    <tr>
                                        <td>Ubi Mentah</td>
                                        <td class="text-end">{{ number_format($totalRawValue, 0, ',', '.') }}</td>
                                        <td class="text-end">{{ $rawPercentage }}%</td>
                                    </tr>
                                    <tr>
                                        <td>Ubi Bakar</td>
                                        <td class="text-end">{{ number_format($totalProcessedValue, 0, ',', '.') }}</td>
                                        <td class="text-end">{{ $processedPercentage }}%</td>
                                    </tr>
                                    <tr class="table-secondary">
                                        <th>Total</th>
                                        <th class="text-end">{{ number_format($totalValue, 0, ',', '.') }}</th>
                                        <th class="text-end">100%</th>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-seedling"></i> Inventori Ubi Mentah</h5>
            <a href="{{ route('raw-inventory.create') }}" class="btn btn-sm btn-success">
                <i class="fas fa-plus-circle"></i> Tambah Stok Baru
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>No Batch</th>
                            <th>Supplier</th>
                            <th>Kualitas</th>
                            <th>Stok (kg)</th>
                            <th>Harga Per kg</th>
                            <th>Nilai Total</th>
                            <th>Tgl Pembelian</th>
                            <th>Tgl Kadaluarsa</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($rawInventory as $raw)
                            <tr>
                                <td>{{ $raw->batch_number }}</td>
                                <td>{{ $raw->supplier_name ?: 'N/A' }}</td>
                                <td>
                                    @if($raw->quality == 'A')
                                        <span class="badge bg-success">A (Premium)</span>
                                    @elseif($raw->quality == 'B')
                                        <span class="badge bg-info">B (Standard)</span>
                                    @elseif($raw->quality == 'C')
                                        <span class="badge bg-warning">C (Economy)</span>
                                    @endif
                                </td>
                                <td>
                                    {{ number_format($raw->current_stock, 2, ',', '.') }} kg
                                    @if($raw->current_stock < 5)
                                        <span class="badge bg-danger">Stok Rendah</span>
                                    @endif
                                </td>
                                <td>Rp {{ number_format($raw->cost_per_kg, 0, ',', '.') }}</td>
                                <td>Rp {{ number_format($raw->current_stock * $raw->cost_per_kg, 0, ',', '.') }}</td>
                                <td>{{ $raw->purchase_date->format('d/m/Y') }}</td>
                                <td>
                                    @if($raw->expiry_date)
                                        {{ $raw->expiry_date->format('d/m/Y') }}
                                        @if($raw->expiry_date->isPast())
                                            <span class="badge bg-danger">Kadaluarsa</span>
                                        @elseif($raw->expiry_date->diffInDays(now()) < 7)
                                            <span class="badge bg-warning">Segera Kadaluarsa</span>
                                        @endif
                                    @else
                                        N/A
                                    @endif
                                </td>
                                <td>
                                    @if($raw->is_active)
                                        <span class="badge bg-success">Aktif</span>
                                    @else
                                        <span class="badge bg-secondary">Tidak Aktif</span>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">Tidak ada data inventori ubi mentah</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-fire"></i> Inventori Ubi Bakar</h5>
            <a href="{{ route('processed-inventory.create') }}" class="btn btn-sm btn-success">
                <i class="fas fa-plus-circle"></i> Tambah Produksi
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>No Batch</th>
                            <th>Jenis Produk</th>
                            <th>Stok (buah)</th>
                            <th>Biaya Produksi</th>
                            <th>Harga Jual</th>
                            <th>Nilai Total</th>
                            <th>Tgl Produksi</th>
                            <th>Tgl Kadaluarsa</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($processedInventory as $product)
                            <tr>
                                <td>{{ $product->batch_number }}</td>
                                <td>{{ $product->product_type }}</td>
                                <td>
                                    {{ number_format($product->current_stock, 0, ',', '.') }} buah
                                    @if($product->current_stock < 10)
                                        <span class="badge bg-danger">Stok Rendah</span>
                                    @endif
                                </td>
                                <td>Rp {{ number_format($product->cost_per_unit, 0, ',', '.') }}</td>
                                <td>Rp {{ number_format($product->selling_price, 0, ',', '.') }}</td>
                                <td>Rp {{ number_format($product->current_stock * $product->cost_per_unit, 0, ',', '.') }}</td>
                                <td>{{ $product->production_date->format('d/m/Y') }}</td>
                                <td>
                                    {{ $product->expiry_date->format('d/m/Y') }}
                                    @if($product->expiry_date->isPast())
                                        <span class="badge bg-danger">Kadaluarsa</span>
                                    @elseif($product->expiry_date->diffInDays(now()) < 3)
                                        <span class="badge bg-warning">Segera Kadaluarsa</span>
                                    @endif
                                </td>
                                <td>
                                    @if($product->is_active)
                                        <span class="badge bg-success">Aktif</span>
                                    @else
                                        <span class="badge bg-secondary">Tidak Aktif</span>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">Tidak ada data inventori ubi bakar</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('inventoryDistributionChart').getContext('2d');
        
        const rawValue = {{ $totalRawValue }};
        const processedValue = {{ $totalProcessedValue }};
        
        const inventoryChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Ubi Mentah', 'Ubi Bakar'],
                datasets: [{
                    data: [rawValue, processedValue],
                    backgroundColor: [
                        '#8B4513',  // Coklat tua untuk ubi mentah
                        '#FF8C00'   // Oranye untuk ubi bakar
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: Rp ${new Intl.NumberFormat('id-ID').format(value)} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
@endsection 