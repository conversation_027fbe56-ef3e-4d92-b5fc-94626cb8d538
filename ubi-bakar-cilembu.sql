-- Database Export: sim_ubi_cilembu
-- Generated: 2025-05-17 03:43:10

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Table structure for table `audit_logs`
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE `audit_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `action` varchar(255) NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  `old_values` text DEFAULT NULL,
  `new_values` text DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `audit_logs_user_id_foreign` (`user_id`),
  KEY `audit_logs_model_type_model_id_index` (`model_type`,`model_id`),
  <PERSON><PERSON><PERSON> `audit_logs_action_index` (`action`),
  KEY `audit_logs_created_at_index` (`created_at`),
  CONSTRAINT `audit_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `cache`
DROP TABLE IF EXISTS `cache`;
CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `cache_locks`
DROP TABLE IF EXISTS `cache_locks`;
CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `failed_jobs`
DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `job_batches`
DROP TABLE IF EXISTS `job_batches`;
CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `jobs`
DROP TABLE IF EXISTS `jobs`;
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) unsigned NOT NULL,
  `reserved_at` int(10) unsigned DEFAULT NULL,
  `available_at` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `migrations`
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `migrations`
INSERT INTO `migrations` VALUES
('1', '0001_01_01_000000_create_users_table', '1'),
('2', '0001_01_01_000001_create_cache_table', '1'),
('3', '0001_01_01_000002_create_jobs_table', '1'),
('4', '2024_03_13_000000_update_user_passwords', '1'),
('5', '2025_05_13_071104_create_raw_inventory_table', '1'),
('6', '2025_05_13_071118_create_processed_inventory_table', '1'),
('7', '2025_05_13_071136_create_transactions_table', '1'),
('8', '2025_05_13_071225_create_transaction_items_table', '1'),
('9', '2025_05_13_071358_add_role_to_users_table', '1'),
('10', '2025_05_13_173320_add_min_stock_threshold_to_raw_inventory', '1'),
('11', '2025_05_13_173607_add_min_stock_threshold_to_processed_inventory', '1'),
('12', '2025_05_13_173850_fix_transaction_items_column_names', '1'),
('13', '2025_05_14_012500_add_customer_info_to_transactions', '1'),
('14', '2025_05_13_193721_create_other_products_table', '2'),
('15', '2025_05_13_173721_add_name_to_raw_inventory', '3'),
('16', '2025_05_14_154257_create_production_logs_table', '4'),
('17', '2025_05_17_012205_add_indexes_to_improve_performance', '5'),
('18', '2025_05_17_012215_add_constraints_for_data_integrity', '5'),
('19', '2025_05_17_012225_add_soft_deletes_to_important_tables', '5'),
('20', '2025_05_17_012234_create_audit_logs_table', '5'),
('21', '2025_05_17_012929_add_last_activity_to_users_table', '6'),
('22', '2025_05_17_013228_add_soft_delete_to_users_table', '7');

-- Table structure for table `other_products`
DROP TABLE IF EXISTS `other_products`;
CREATE TABLE `other_products` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `sku` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `purchase_price` decimal(10,2) NOT NULL,
  `selling_price` decimal(10,2) NOT NULL,
  `current_stock` int(11) NOT NULL,
  `min_stock_threshold` int(11) NOT NULL DEFAULT 5,
  `category` varchar(255) DEFAULT NULL,
  `unit` varchar(255) NOT NULL DEFAULT 'pcs',
  `supplier` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `other_products_sku_unique` (`sku`),
  KEY `idx_other_prod_sku` (`sku`),
  KEY `idx_other_prod_category` (`category`),
  KEY `idx_other_prod_stock` (`current_stock`),
  KEY `idx_other_prod_active_stock` (`is_active`,`current_stock`),
  CONSTRAINT `chk_other_prod_purchase_price_positive` CHECK (`purchase_price` >= 0),
  CONSTRAINT `chk_other_prod_selling_price_positive` CHECK (`selling_price` >= 0),
  CONSTRAINT `chk_other_prod_current_stock_positive` CHECK (`current_stock` >= 0)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `other_products`
INSERT INTO `other_products` VALUES
('1', 'TEH BOTOL', 'OTH-D1X8QCNU', 'teh botol segar', '2000.00', '3000.00', '10', '5', 'minuman', 'pcs', 'grosir rudi', '0', NULL, '2025-05-13 19:44:03', '2025-05-13 19:45:09', NULL),
('2', 'ROKOK SAMSU', 'OTH-SRFVMJTM', 'rokok untuk orang dewasa', '10000.00', '200000.00', '1', '5', 'ROKOK', 'pcs', 'toko bagus', '0', NULL, '2025-05-14 04:31:21', '2025-05-14 23:06:30', NULL);

-- Table structure for table `password_reset_tokens`
DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `processed_inventory`
DROP TABLE IF EXISTS `processed_inventory`;
CREATE TABLE `processed_inventory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `batch_number` varchar(255) NOT NULL,
  `raw_inventory_id` bigint(20) unsigned DEFAULT NULL,
  `quantity_processed_kg` decimal(10,2) NOT NULL,
  `quantity_produced` decimal(10,2) NOT NULL,
  `cost_per_unit` decimal(10,2) NOT NULL,
  `cost_per_item` decimal(10,2) DEFAULT NULL,
  `selling_price` decimal(10,2) NOT NULL,
  `production_date` date NOT NULL,
  `expiry_date` date NOT NULL,
  `product_type` enum('Original','Premium','Special') NOT NULL,
  `current_stock` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `min_stock_threshold` int(11) DEFAULT 5,
  `raw_material_per_item` decimal(10,2) DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `processed_inventory_batch_number_unique` (`batch_number`),
  KEY `processed_inventory_raw_inventory_id_foreign` (`raw_inventory_id`),
  KEY `idx_proc_batch_number` (`batch_number`),
  KEY `idx_proc_production_date` (`production_date`),
  KEY `idx_proc_expiry_date` (`expiry_date`),
  KEY `idx_proc_current_stock` (`current_stock`),
  KEY `idx_proc_active_stock` (`is_active`,`current_stock`),
  KEY `idx_proc_product_type` (`product_type`),
  CONSTRAINT `processed_inventory_raw_inventory_id_foreign` FOREIGN KEY (`raw_inventory_id`) REFERENCES `raw_inventory` (`id`) ON DELETE SET NULL,
  CONSTRAINT `chk_proc_quantity_processed_positive` CHECK (`quantity_processed_kg` >= 0),
  CONSTRAINT `chk_proc_quantity_produced_positive` CHECK (`quantity_produced` >= 0),
  CONSTRAINT `chk_proc_cost_per_unit_positive` CHECK (`cost_per_unit` >= 0),
  CONSTRAINT `chk_proc_selling_price_positive` CHECK (`selling_price` >= 0),
  CONSTRAINT `chk_proc_current_stock_positive` CHECK (`current_stock` >= 0),
  CONSTRAINT `chk_proc_dates_valid` CHECK (`expiry_date` > `production_date`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `processed_inventory`
INSERT INTO `processed_inventory` VALUES
('1', 'Ubi Bakar Original', 'UBO-001', '4', '25.00', '100.00', '5000.00', '3417.00', '8000.00', '2025-05-13', '2025-05-20', 'Original', '186.00', '1', 'Ubi bakar original dengan tekstur lembut', '2025-05-13 18:40:51', '2025-05-15 04:29:16', '20', '0.25', NULL),
('2', 'Ubi Bakar Coklat', 'UBC-001', NULL, '25.00', '100.00', '6000.00', '6000.00', '10000.00', '2025-05-13', '2025-05-20', 'Premium', '91.00', '1', 'Ubi bakar dengan topping coklat premium', '2025-05-13 18:40:51', '2025-05-14 22:52:32', '20', '0.25', NULL),
('3', 'Ubi Bakar Keju', 'UBK-001', '3', '25.00', '100.00', '6000.00', '2667.00', '10000.00', '2025-05-13', '2025-05-20', 'Premium', '42.00', '1', 'Ubi bakar dengan topping keju melimpah', '2025-05-13 18:40:51', '2025-05-14 22:52:32', '33', '0.25', NULL),
('4', 'Ubi Bakar Spesial', 'UBS-001', NULL, '25.00', '100.00', '7000.00', '7000.00', '12000.00', '2025-05-13', '2025-05-20', 'Special', '97.00', '1', 'Ubi bakar dengan topping coklat dan keju', '2025-05-13 18:40:51', '2025-05-14 04:46:29', '20', '0.25', NULL);

-- Table structure for table `production_logs`
DROP TABLE IF EXISTS `production_logs`;
CREATE TABLE `production_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `raw_inventory_id` bigint(20) unsigned NOT NULL,
  `processed_inventory_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `raw_amount_used` decimal(10,2) NOT NULL COMMENT 'dalam kg',
  `produced_amount` int(11) NOT NULL COMMENT 'jumlah item yang dihasilkan',
  `raw_cost` decimal(12,2) NOT NULL COMMENT 'biaya bahan mentah',
  `additional_cost` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT 'biaya tambahan',
  `total_cost` decimal(12,2) NOT NULL COMMENT 'total biaya produksi',
  `cost_per_item` decimal(12,2) NOT NULL COMMENT 'biaya per item',
  `raw_name` varchar(255) NOT NULL COMMENT 'nama bahan mentah saat produksi',
  `processed_name` varchar(255) NOT NULL COMMENT 'nama produk saat produksi',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `production_logs_processed_inventory_id_foreign` (`processed_inventory_id`),
  KEY `production_logs_user_id_foreign` (`user_id`),
  KEY `idx_prod_logs_created` (`created_at`),
  KEY `idx_prod_logs_raw_proc` (`raw_inventory_id`,`processed_inventory_id`),
  CONSTRAINT `production_logs_processed_inventory_id_foreign` FOREIGN KEY (`processed_inventory_id`) REFERENCES `processed_inventory` (`id`),
  CONSTRAINT `production_logs_raw_inventory_id_foreign` FOREIGN KEY (`raw_inventory_id`) REFERENCES `raw_inventory` (`id`),
  CONSTRAINT `production_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `chk_prod_logs_raw_amount_positive` CHECK (`raw_amount_used` >= 0),
  CONSTRAINT `chk_prod_logs_produced_amount_positive` CHECK (`produced_amount` >= 0),
  CONSTRAINT `chk_prod_logs_raw_cost_positive` CHECK (`raw_cost` >= 0),
  CONSTRAINT `chk_prod_logs_additional_cost_positive` CHECK (`additional_cost` >= 0),
  CONSTRAINT `chk_prod_logs_total_cost_positive` CHECK (`total_cost` >= 0),
  CONSTRAINT `chk_prod_logs_cost_per_item_positive` CHECK (`cost_per_item` >= 0)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `production_logs`
INSERT INTO `production_logs` VALUES
('1', '3', '3', '2', '3.00', '12', '30000.00', '2000.00', '32000.00', '2667.00', 'ubi ubu', 'Ubi Bakar Keju', '2025-05-14 15:59:47', '2025-05-14 15:59:47', NULL),
('2', '4', '1', '2', '30.00', '120', '360000.00', '50000.00', '410000.00', '3417.00', 'UBI SUPER', 'Ubi Bakar Original', '2025-05-14 22:55:59', '2025-05-14 22:55:59', NULL);

-- Table structure for table `raw_inventory`
DROP TABLE IF EXISTS `raw_inventory`;
CREATE TABLE `raw_inventory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `batch_number` varchar(255) NOT NULL,
  `supplier_name` varchar(255) DEFAULT NULL,
  `quantity_kg` decimal(10,2) NOT NULL,
  `cost_per_kg` decimal(10,2) NOT NULL,
  `total_cost` decimal(12,2) NOT NULL,
  `purchase_date` date NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `quality` enum('A','B','C') NOT NULL,
  `notes` text DEFAULT NULL,
  `current_stock` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `min_stock_threshold` decimal(8,2) DEFAULT 10.00,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `raw_inventory_batch_number_unique` (`batch_number`),
  KEY `idx_raw_batch_number` (`batch_number`),
  KEY `idx_raw_purchase_date` (`purchase_date`),
  KEY `idx_raw_expiry_date` (`expiry_date`),
  KEY `idx_raw_current_stock` (`current_stock`),
  KEY `idx_raw_active_stock` (`is_active`,`current_stock`),
  CONSTRAINT `chk_raw_quantity_positive` CHECK (`quantity_kg` >= 0),
  CONSTRAINT `chk_raw_cost_per_kg_positive` CHECK (`cost_per_kg` >= 0),
  CONSTRAINT `chk_raw_total_cost_positive` CHECK (`total_cost` >= 0),
  CONSTRAINT `chk_raw_current_stock_positive` CHECK (`current_stock` >= 0),
  CONSTRAINT `chk_raw_dates_valid` CHECK (`expiry_date` is null or `expiry_date` > `purchase_date`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `raw_inventory`
INSERT INTO `raw_inventory` VALUES
('1', 'ubi super', 'BATCH-99YDUTMP', 'rydu', '12.00', '12000.00', '169200.00', '2025-05-13', '2025-05-31', 'B', NULL, '14.10', '0', '2025-05-13 19:56:24', '2025-05-14 15:23:22', '10.00', NULL),
('2', 'UBI SUPER', 'BATCH-P8PC0CH0', NULL, '10.00', '12000.00', '120000.00', '2025-05-14', '2025-05-24', 'A', NULL, '10.00', '0', '2025-05-14 15:32:16', '2025-05-14 15:36:56', '10.00', NULL),
('3', 'ubi ubu', 'BATCH-U6OBATGC', NULL, '10.00', '10000.00', '100000.00', '2025-05-14', NULL, 'A', NULL, '7.00', '1', '2025-05-14 15:36:49', '2025-05-14 15:59:47', '10.00', NULL),
('4', 'UBI SUPER', 'BATCH-MXUEHVPS', 'sdasda', '39.00', '12000.00', '468000.00', '2025-05-14', NULL, 'A', NULL, '9.00', '1', '2025-05-14 22:52:58', '2025-05-14 22:55:59', '10.00', NULL);

-- Table structure for table `sessions`
DROP TABLE IF EXISTS `sessions`;
CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `transaction_items`
DROP TABLE IF EXISTS `transaction_items`;
CREATE TABLE `transaction_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `transaction_id` bigint(20) unsigned NOT NULL,
  `product_id` bigint(20) unsigned NOT NULL,
  `processed_inventory_id` bigint(20) unsigned DEFAULT NULL,
  `product_name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `discount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `subtotal` decimal(12,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_items_product_id_foreign` (`product_id`),
  KEY `transaction_items_processed_inventory_id_foreign` (`processed_inventory_id`),
  KEY `idx_trans_items_trans_prod` (`transaction_id`,`product_id`),
  KEY `idx_trans_items_trans_proc` (`transaction_id`,`processed_inventory_id`),
  CONSTRAINT `transaction_items_processed_inventory_id_foreign` FOREIGN KEY (`processed_inventory_id`) REFERENCES `processed_inventory` (`id`) ON DELETE CASCADE,
  CONSTRAINT `transaction_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `processed_inventory` (`id`) ON DELETE CASCADE,
  CONSTRAINT `transaction_items_transaction_id_foreign` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `chk_trans_items_price_positive` CHECK (`price` >= 0),
  CONSTRAINT `chk_trans_items_quantity_positive` CHECK (`quantity` >= 0),
  CONSTRAINT `chk_trans_items_discount_positive` CHECK (`discount` >= 0),
  CONSTRAINT `chk_trans_items_subtotal_positive` CHECK (`subtotal` >= 0)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `transaction_items`
INSERT INTO `transaction_items` VALUES
('1', '1', '4', '4', 'Ubi Bakar Spesial', '12000.00', '1', '0.00', '12000.00', '2025-05-13 18:48:59', '2025-05-13 18:48:59'),
('2', '2', '1', '1', 'Ubi Bakar Original', '8000.00', '1', '0.00', '8000.00', '2025-05-13 18:53:01', '2025-05-13 18:53:01'),
('3', '2', '3', '3', 'Ubi Bakar Keju', '10000.00', '1', '0.00', '10000.00', '2025-05-13 18:53:01', '2025-05-13 18:53:01'),
('4', '2', '2', '2', 'Ubi Bakar Coklat', '10000.00', '1', '0.00', '10000.00', '2025-05-13 18:53:01', '2025-05-13 18:53:01'),
('5', '2', '4', '4', 'Ubi Bakar Spesial', '12000.00', '1', '0.00', '12000.00', '2025-05-13 18:53:01', '2025-05-13 18:53:01'),
('6', '3', '4', '4', 'Ubi Bakar Spesial', '12000.00', '1', '0.00', '12000.00', '2025-05-14 04:46:29', '2025-05-14 04:46:29'),
('7', '3', '1', '1', 'Ubi Bakar Original', '8000.00', '1', '0.00', '8000.00', '2025-05-14 04:46:29', '2025-05-14 04:46:29'),
('8', '3', '3', '3', 'Ubi Bakar Keju', '10000.00', '6', '0.00', '60000.00', '2025-05-14 04:46:29', '2025-05-14 04:46:29'),
('9', '4', '2', '2', 'Ubi Bakar Coklat', '10000.00', '1', '0.00', '10000.00', '2025-05-14 16:56:31', '2025-05-14 16:56:31'),
('10', '4', '3', '3', 'Ubi Bakar Keju', '10000.00', '1', '0.00', '10000.00', '2025-05-14 16:56:31', '2025-05-14 16:56:31'),
('11', '4', '1', '1', 'Ubi Bakar Original', '8000.00', '1', '0.00', '8000.00', '2025-05-14 16:56:31', '2025-05-14 16:56:31'),
('12', '5', '3', '3', 'Ubi Bakar Keju', '10000.00', '9', '0.00', '90000.00', '2025-05-14 22:52:32', '2025-05-14 22:52:32'),
('13', '5', '2', '2', 'Ubi Bakar Coklat', '10000.00', '7', '0.00', '70000.00', '2025-05-14 22:52:32', '2025-05-14 22:52:32'),
('14', '6', '2', NULL, 'ROKOK SAMSU', '200000.00', '11', '0.00', '2200000.00', '2025-05-14 22:57:26', '2025-05-14 22:57:26'),
('15', '7', '1', '1', 'Ubi Bakar Original', '8000.00', '31', '0.00', '248000.00', '2025-05-15 04:29:16', '2025-05-15 04:29:16');

-- Table structure for table `transactions`
DROP TABLE IF EXISTS `transactions`;
CREATE TABLE `transactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `customer_name` varchar(255) DEFAULT NULL,
  `customer_phone` varchar(255) DEFAULT NULL,
  `subtotal` decimal(12,2) NOT NULL,
  `tax` decimal(12,2) NOT NULL DEFAULT 0.00,
  `discount` decimal(12,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(12,2) NOT NULL,
  `amount_paid` decimal(12,2) NOT NULL,
  `change_amount` decimal(12,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('cash','transfer','qris','debit','credit') NOT NULL,
  `status` enum('completed','cancelled','refunded') NOT NULL DEFAULT 'completed',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transactions_invoice_number_unique` (`invoice_number`),
  KEY `idx_trans_invoice` (`invoice_number`),
  KEY `idx_trans_created_at` (`created_at`),
  KEY `idx_trans_status` (`status`),
  KEY `idx_trans_payment` (`payment_method`),
  KEY `idx_trans_user_date` (`user_id`,`created_at`),
  CONSTRAINT `transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `chk_trans_subtotal_positive` CHECK (`subtotal` >= 0),
  CONSTRAINT `chk_trans_tax_positive` CHECK (`tax` >= 0),
  CONSTRAINT `chk_trans_discount_positive` CHECK (`discount` >= 0),
  CONSTRAINT `chk_trans_total_amount_positive` CHECK (`total_amount` >= 0),
  CONSTRAINT `chk_trans_amount_paid_positive` CHECK (`amount_paid` >= 0),
  CONSTRAINT `chk_trans_change_amount_positive` CHECK (`change_amount` >= 0)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `transactions`
INSERT INTO `transactions` VALUES
('1', 'INV/20250513/0001', '2', NULL, NULL, '12000.00', '0.00', '0.00', '12000.00', '1000000.00', '988000.00', 'cash', 'completed', NULL, '2025-05-13 18:48:59', '2025-05-13 18:48:59', NULL),
('2', 'INV/20250513/0002', '2', 'hrnti', '324', '40000.00', '0.00', '0.00', '40000.00', '50000.00', '10000.00', 'cash', 'completed', 'manrep', '2025-05-13 18:53:01', '2025-05-13 18:53:01', NULL),
('3', 'INV/20250514/0001', '2', NULL, NULL, '80000.00', '0.00', '0.00', '80000.00', '81000.00', '1000.00', 'cash', 'completed', NULL, '2025-05-14 04:46:29', '2025-05-14 04:46:29', NULL),
('4', 'INV/20250514/0002', '2', NULL, NULL, '28000.00', '0.00', '0.00', '28000.00', '30000.00', '2000.00', 'qris', 'completed', NULL, '2025-05-14 16:56:31', '2025-05-14 16:56:31', NULL),
('5', 'INV/20250514/0003', '3', NULL, NULL, '160000.00', '0.00', '0.00', '160000.00', '2000000.00', '1840000.00', 'cash', 'completed', NULL, '2025-05-14 22:52:32', '2025-05-14 22:52:32', NULL),
('6', 'INV/20250514/0004', '2', NULL, NULL, '2200000.00', '0.00', '0.00', '2200000.00', '2500000.00', '300000.00', 'cash', 'completed', NULL, '2025-05-14 22:57:26', '2025-05-14 22:57:26', NULL),
('7', 'INV/20250515/0001', '2', NULL, NULL, '248000.00', '0.00', '0.00', '248000.00', '250000.00', '2000.00', 'cash', 'completed', NULL, '2025-05-15 04:29:16', '2025-05-15 04:29:16', NULL);

-- Table structure for table `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `role` enum('admin','employee') NOT NULL DEFAULT 'employee',
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_activity` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `users`
INSERT INTO `users` VALUES
('1', 'Test User', '<EMAIL>', 'employee', '2025-05-13 18:40:50', '$2y$12$29tJBXIiAJZdjmh30B66pe9YOCoIpQ3fuvfGNUEkbMJSfvpeXW4SG', '3SxSIYsyn5', '2025-05-13 18:40:50', '2025-05-13 18:40:50', NULL, NULL),
('2', 'Admin', '<EMAIL>', 'admin', NULL, '$2y$12$HG8rofay9Y431JfTmYWCwutUEFdiGt2oeVHIuboceRkS4qlaUCwTi', NULL, '2025-05-13 18:40:51', '2025-05-13 18:40:51', NULL, NULL),
('3', 'Karyawan Toko', '<EMAIL>', 'employee', NULL, '$2y$12$MGkUVuCuTE51sM.9JQi7Hes7HUezt4FAu5Q7CoBAg0ZXFMzmvkucW', 'fNdYPBqJ1q67NaiXW5H2I3C4xYSZIB1Wsq3IAmCcxAeviGMIJJvcVoBAgJLc', '2025-05-14 05:27:27', '2025-05-14 05:27:27', NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;
