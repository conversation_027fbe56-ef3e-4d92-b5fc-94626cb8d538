<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('processed_inventory', function (Blueprint $table) {
            // Field untuk prioritas penjualan
            $table->string('priority_level')->nullable()->comment('Tinggi, Sedang, Rendah');
            $table->boolean('needs_immediate_sale')->default(false);
            $table->integer('days_until_expiry')->nullable();
            $table->string('recommended_market')->nullable();
            $table->boolean('notification_sent')->default(false);
            $table->timestamp('last_notification_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('processed_inventory', function (Blueprint $table) {
            $table->dropColumn([
                'priority_level',
                'needs_immediate_sale',
                'days_until_expiry',
                'recommended_market',
                'notification_sent',
                'last_notification_date'
            ]);
        });
    }
};
