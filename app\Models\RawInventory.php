<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Auditable;

class RawInventory extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $table = 'raw_inventory';

    protected $fillable = [
        'batch_number',
        'supplier_name',
        'quantity_kg',
        'cost_per_kg',
        'total_cost',
        'purchase_date',
        'expiry_date',
        'quality',
        'notes',
        'current_stock',
        'is_active',
        'min_stock_threshold',
        'name'
    ];

    protected $casts = [
        'purchase_date' => 'date',
        'expiry_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the processed inventory items associated with this raw inventory.
     */
    public function processedInventory(): HasMany
    {
        return $this->hasMany(ProcessedInventory::class);
    }
}
