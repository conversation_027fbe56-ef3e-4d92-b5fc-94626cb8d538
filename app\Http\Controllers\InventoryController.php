<?php

namespace App\Http\Controllers;

use App\Models\RawInventory;
use App\Models\ProcessedInventory;
use Illuminate\Http\Request;

class InventoryController extends Controller
{
    public function index()
    {
        $rawInventory = RawInventory::all();
        $processedInventory = ProcessedInventory::all();
        
        // Check for low stock
        $lowStockRaw = RawInventory::where('current_stock', '<=', 10)->get();
        $lowStockProcessed = ProcessedInventory::where('current_stock', '<=', 5)->get();
        
        return view('inventory.index', compact('rawInventory', 'processedInventory', 'lowStockRaw', 'lowStockProcessed'));
    }

    public function addRawStock(Request $request)
    {
        $validated = $request->validate([
            'quantity_kg' => 'required|numeric|min:1',
            'cost_per_kg' => 'required|numeric|min:0',
            'supplier_name' => 'required|string',
            'notes' => 'nullable|string'
        ]);

        // Hitung total cost
        $validated['total_cost'] = $validated['quantity_kg'] * $validated['cost_per_kg'];
        $validated['current_stock'] = $validated['quantity_kg'];
        $validated['purchase_date'] = now();
        $validated['batch_number'] = 'BATCH-' . time();
            
        RawInventory::create($validated);

        return redirect()->back()->with('success', 'Stok ubi mentah berhasil ditambahkan');
    }

    public function processStock(Request $request)
    {
        $validated = $request->validate([
            'raw_inventory_id' => 'required|exists:raw_inventory,id',
            'quantity_processed_kg' => 'required|numeric|min:1',
            'selling_price' => 'required|numeric|min:0'
        ]);

        $rawInventory = RawInventory::findOrFail($validated['raw_inventory_id']);
        
        if ($rawInventory->current_stock < $validated['quantity_processed_kg']) {
            return redirect()->back()->with('error', 'Stok ubi mentah tidak mencukupi');
        }

        // Kurangi stok mentah
        $rawInventory->decrement('current_stock', $validated['quantity_processed_kg']);

        // Hitung jumlah ubi bakar yang dihasilkan (asumsi 1 kg = 4 buah ubi bakar)
        $quantity_produced = $validated['quantity_processed_kg'] * 4;

        // Tambah stok matang
        ProcessedInventory::create([
            'batch_number' => 'PROC-' . time(),
            'raw_inventory_id' => $validated['raw_inventory_id'],
            'quantity_processed_kg' => $validated['quantity_processed_kg'],
            'quantity_produced' => $quantity_produced,
            'current_stock' => $quantity_produced,
            'cost_per_unit' => ($rawInventory->cost_per_kg * $validated['quantity_processed_kg']) / $quantity_produced,
            'selling_price' => $validated['selling_price'],
            'production_date' => now(),
            'expiry_date' => now()->addDays(3),
            'product_type' => 'Original',
            'is_active' => true
        ]);

        return redirect()->back()->with('success', 'Ubi berhasil diproses');
    }

    public function getLowStockAlert()
    {
        $lowStockRaw = RawInventory::where('current_stock', '<=', 10)->get();
        $lowStockProcessed = ProcessedInventory::where('current_stock', '<=', 5)->get();
        $lowStockOther = \App\Models\OtherProduct::whereRaw('current_stock <= min_stock_threshold')->get();

        return view('inventory.low-stock-alert', compact('lowStockRaw', 'lowStockProcessed', 'lowStockOther'));
    }
} 