<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transaction_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('transaction_id'); // Referensi ke transaksi
            $table->unsignedBigInteger('product_id'); // Referensi ke produk
            $table->string('product_name'); // Nama produk
            $table->decimal('price', 10, 2); // Harga satuan
            $table->integer('quantity'); // Jumlah unit
            $table->decimal('discount', 10, 2)->default(0); // Diskon per item
            $table->decimal('subtotal', 12, 2); // Subtotal per item
            $table->timestamps();
            
            $table->foreign('transaction_id')->references('id')->on('transactions')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('processed_inventory')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transaction_items');
    }
};
