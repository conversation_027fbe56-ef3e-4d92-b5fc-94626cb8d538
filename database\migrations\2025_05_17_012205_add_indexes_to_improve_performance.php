<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Menambahkan indeks pada tabel raw_inventory
        Schema::table('raw_inventory', function (Blueprint $table) {
            $table->index('batch_number', 'idx_raw_batch_number');
            $table->index('purchase_date', 'idx_raw_purchase_date');
            $table->index('expiry_date', 'idx_raw_expiry_date');
            $table->index('current_stock', 'idx_raw_current_stock');
            $table->index(['is_active', 'current_stock'], 'idx_raw_active_stock');
        });

        // Menambahkan indeks pada tabel processed_inventory
        Schema::table('processed_inventory', function (Blueprint $table) {
            $table->index('batch_number', 'idx_proc_batch_number');
            $table->index('production_date', 'idx_proc_production_date');
            $table->index('expiry_date', 'idx_proc_expiry_date');
            $table->index('current_stock', 'idx_proc_current_stock');
            $table->index(['is_active', 'current_stock'], 'idx_proc_active_stock');
            $table->index('product_type', 'idx_proc_product_type');
        });

        // Menambahkan indeks pada tabel transactions
        Schema::table('transactions', function (Blueprint $table) {
            $table->index('invoice_number', 'idx_trans_invoice');
            $table->index('created_at', 'idx_trans_created_at');
            $table->index('status', 'idx_trans_status');
            $table->index('payment_method', 'idx_trans_payment');
            $table->index(['user_id', 'created_at'], 'idx_trans_user_date');
        });

        // Menambahkan indeks pada tabel transaction_items
        Schema::table('transaction_items', function (Blueprint $table) {
            $table->index(['transaction_id', 'product_id'], 'idx_trans_items_trans_prod');
            $table->index(['transaction_id', 'processed_inventory_id'], 'idx_trans_items_trans_proc');
        });

        // Menambahkan indeks pada tabel other_products
        Schema::table('other_products', function (Blueprint $table) {
            $table->index('sku', 'idx_other_prod_sku');
            $table->index('category', 'idx_other_prod_category');
            $table->index('current_stock', 'idx_other_prod_stock');
            $table->index(['is_active', 'current_stock'], 'idx_other_prod_active_stock');
        });

        // Menambahkan indeks pada tabel production_logs
        Schema::table('production_logs', function (Blueprint $table) {
            $table->index('created_at', 'idx_prod_logs_created');
            $table->index(['raw_inventory_id', 'processed_inventory_id'], 'idx_prod_logs_raw_proc');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Menghapus indeks dari tabel raw_inventory
        Schema::table('raw_inventory', function (Blueprint $table) {
            $table->dropIndex('idx_raw_batch_number');
            $table->dropIndex('idx_raw_purchase_date');
            $table->dropIndex('idx_raw_expiry_date');
            $table->dropIndex('idx_raw_current_stock');
            $table->dropIndex('idx_raw_active_stock');
        });

        // Menghapus indeks dari tabel processed_inventory
        Schema::table('processed_inventory', function (Blueprint $table) {
            $table->dropIndex('idx_proc_batch_number');
            $table->dropIndex('idx_proc_production_date');
            $table->dropIndex('idx_proc_expiry_date');
            $table->dropIndex('idx_proc_current_stock');
            $table->dropIndex('idx_proc_active_stock');
            $table->dropIndex('idx_proc_product_type');
        });

        // Menghapus indeks dari tabel transactions
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropIndex('idx_trans_invoice');
            $table->dropIndex('idx_trans_created_at');
            $table->dropIndex('idx_trans_status');
            $table->dropIndex('idx_trans_payment');
            $table->dropIndex('idx_trans_user_date');
        });

        // Menghapus indeks dari tabel transaction_items
        Schema::table('transaction_items', function (Blueprint $table) {
            $table->dropIndex('idx_trans_items_trans_prod');
            $table->dropIndex('idx_trans_items_trans_proc');
        });

        // Menghapus indeks dari tabel other_products
        Schema::table('other_products', function (Blueprint $table) {
            $table->dropIndex('idx_other_prod_sku');
            $table->dropIndex('idx_other_prod_category');
            $table->dropIndex('idx_other_prod_stock');
            $table->dropIndex('idx_other_prod_active_stock');
        });

        // Menghapus indeks dari tabel production_logs
        Schema::table('production_logs', function (Blueprint $table) {
            $table->dropIndex('idx_prod_logs_created');
            $table->dropIndex('idx_prod_logs_raw_proc');
        });
    }
};
