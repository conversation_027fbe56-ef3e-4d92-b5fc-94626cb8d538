@extends('layouts.app')

@section('content')
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-calculator"></i> <PERSON><PERSON><PERSON></h1>
        <div>
            <a href="{{ route('reports.export', ['type' => 'financial', 'start_date' => $startDate, 'end_date' => $endDate]) }}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Export Excel
            </a>
            <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Filter Data</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('reports.financial') }}" method="GET" class="row">
                        <div class="col-md-4 mb-3">
                            <label for="start_date" class="form-label">Tanggal Awal</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate }}">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <h5>Laporan Laba Rugi</h5>
            <small class="text-muted">Periode: {{ \Carbon\Carbon::parse($startDate)->format('d F Y') }} - {{ \Carbon\Carbon::parse($endDate)->format('d F Y') }}</small>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <tbody>
                        <tr class="table-primary">
                            <th colspan="2">Pendapatan</th>
                        </tr>
                        <tr>
                            <td style="padding-left: 20px;">Penjualan</td>
                            <td class="text-end">Rp {{ number_format($revenue, 0, ',', '.') }}</td>
                        </tr>
                        <tr class="fw-bold">
                            <td>Total Pendapatan</td>
                            <td class="text-end">Rp {{ number_format($revenue, 0, ',', '.') }}</td>
                        </tr>
                        
                        <tr class="table-danger">
                            <th colspan="2">Harga Pokok Penjualan</th>
                        </tr>
                        <tr>
                            <td style="padding-left: 20px;">Biaya Produksi Ubi Bakar</td>
                            <td class="text-end">Rp {{ number_format($cogs, 0, ',', '.') }}</td>
                        </tr>
                        <tr class="fw-bold">
                            <td>Total Harga Pokok Penjualan</td>
                            <td class="text-end">Rp {{ number_format($cogs, 0, ',', '.') }}</td>
                        </tr>
                        
                        <tr class="table-success fw-bold">
                            <td>Laba Kotor</td>
                            <td class="text-end">Rp {{ number_format($grossProfit, 0, ',', '.') }}</td>
                        </tr>
                        
                        <tr class="table-warning">
                            <th colspan="2">Biaya Operasional</th>
                        </tr>
                        <tr>
                            <td style="padding-left: 20px;">Pembelian Bahan Baku</td>
                            <td class="text-end">Rp {{ number_format($rawPurchases, 0, ',', '.') }}</td>
                        </tr>
                        <tr class="fw-bold">
                            <td>Total Biaya Operasional</td>
                            <td class="text-end">Rp {{ number_format($rawPurchases, 0, ',', '.') }}</td>
                        </tr>
                        
                        <tr class="table-info fw-bold">
                            <td>Laba Bersih</td>
                            <td class="text-end">Rp {{ number_format($netProfit, 0, ',', '.') }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Ringkasan Keuangan</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="p-3 bg-primary text-white rounded">
                                <h6>Pendapatan</h6>
                                <h4>Rp {{ number_format($revenue, 0, ',', '.') }}</h4>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="p-3 bg-success text-white rounded">
                                <h6>Laba Kotor</h6>
                                <h4>Rp {{ number_format($grossProfit, 0, ',', '.') }}</h4>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="p-3 bg-danger text-white rounded">
                                <h6>Biaya Produksi</h6>
                                <h4>Rp {{ number_format($cogs, 0, ',', '.') }}</h4>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="p-3 bg-info text-white rounded">
                                <h6>Laba Bersih</h6>
                                <h4>Rp {{ number_format($netProfit, 0, ',', '.') }}</h4>
                                @if($revenue > 0)
                                    <small>Margin: {{ round(($netProfit / $revenue) * 100, 2) }}%</small>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Grafik Keuangan</h5>
                </div>
                <div class="card-body">
                    <canvas id="financialChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('financialChart').getContext('2d');
        
        const revenue = {{ $revenue }};
        const cogs = {{ $cogs }};
        const grossProfit = {{ $grossProfit }};
        const expenses = {{ $rawPurchases }};
        const netProfit = {{ $netProfit }};
        
        const financialChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Pendapatan', 'HPP', 'Laba Kotor', 'Biaya Operasional', 'Laba Bersih'],
                datasets: [{
                    label: 'Laporan Keuangan (Rp)',
                    data: [revenue, cogs, grossProfit, expenses, netProfit],
                    backgroundColor: [
                        '#4361EE',  // Pendapatan (Biru)
                        '#E63946',  // HPP (Merah)
                        '#2EC4B6',  // Laba kotor (Hijau)
                        '#FF9F1C',  // Biaya operasional (Kuning)
                        '#7209B7'   // Laba bersih (Ungu)
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + new Intl.NumberFormat('id-ID').format(value);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Rp ' + new Intl.NumberFormat('id-ID').format(context.raw);
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
@endsection 