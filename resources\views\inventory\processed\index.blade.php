@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-fire-alt me-2"></i>
            <span class="fw-bold">Produk Ubi Matang</span>
        </div>
        <div class="button-group d-flex">
            <a href="{{ route('processed-inventory.create') }}" class="btn btn-primary me-2">
                <i class="fas fa-plus me-1"></i> Tambah Produk
            </a>
            <a href="{{ route('processed-inventory.show-process-form') }}" class="btn btn-success me-2">
                <i class="fas fa-industry me-1"></i> Proses Produksi
            </a>
            <a href="{{ route('production.reports') }}" class="btn btn-info">
                <i class="fas fa-chart-line me-1"></i> <PERSON><PERSON><PERSON>du<PERSON>
            </a>
        </div>
    </div>

    <!-- Filter dan Statistik Kadaluarsa -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filter & Statistik Kadaluarsa</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="btn-group" role="group" aria-label="Filter Status">
                                <button type="button" class="btn btn-outline-primary active" onclick="filterTable('all')">
                                    <i class="fas fa-list"></i> Semua
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="filterTable('expired')">
                                    <i class="fas fa-times-circle"></i> Kadaluarsa
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="filterTable('near-expiry')">
                                    <i class="fas fa-exclamation-triangle"></i> Segera Kadaluarsa
                                </button>
                                <button type="button" class="btn btn-outline-success" onclick="filterTable('fresh')">
                                    <i class="fas fa-check-circle"></i> Masih Segar
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="filterTable('low-stock')">
                                    <i class="fas fa-arrow-down"></i> Stok Menipis
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            @php
                                $expiredCount = $processedInventory->filter(function($item) {
                                    $item->updateExpiryTracking();
                                    return $item->days_until_expiry < 0;
                                })->count();

                                $nearExpiryCount = $processedInventory->filter(function($item) {
                                    return $item->days_until_expiry >= 0 && $item->days_until_expiry <= 3;
                                })->count();

                                $lowStockCount = $processedInventory->filter(function($item) {
                                    return $item->current_stock <= ($item->min_stock_threshold ?? 5);
                                })->count();
                            @endphp
                            <div class="d-flex justify-content-end">
                                <div class="me-3">
                                    <small class="text-muted">Kadaluarsa:</small>
                                    <span class="badge bg-danger">{{ $expiredCount }}</span>
                                </div>
                                <div class="me-3">
                                    <small class="text-muted">Segera Kadaluarsa:</small>
                                    <span class="badge bg-warning">{{ $nearExpiryCount }}</span>
                                </div>
                                <div>
                                    <small class="text-muted">Stok Menipis:</small>
                                    <span class="badge bg-info">{{ $lowStockCount }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span class="fw-bold">Daftar Stok Ubi Matang</span>
                </div>
                <div class="card-body">
                    @if($lowStock->count() > 0)
                    <div class="alert alert-warning mb-4">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i> Peringatan Stok Menipis</h5>
                        <ul class="mb-0">
                            @foreach($lowStock as $item)
                            <li>{{ $item->name }} - tersisa {{ $item->current_stock }} pcs (minimum: {{ $item->min_stock_threshold ?? 0 }} pcs)</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif

                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Nama Produk</th>
                                    <th>Jenis</th>
                                    <th>Stok Saat Ini</th>
                                    <th>Harga Jual</th>
                                    <th>Margin</th>
                                    <th>Tanggal Kadaluarsa</th>
                                    <th>Status</th>
                                    <th>Prioritas</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($processedInventory as $index => $item)
                                @php
                                    // Update expiry tracking for current item
                                    $item->updateExpiryTracking();
                                    $daysUntilExpiry = $item->days_until_expiry;
                                    $isExpired = $daysUntilExpiry < 0;
                                    $isNearExpiry = $daysUntilExpiry <= 3 && $daysUntilExpiry >= 0;
                                    $isLowStock = $item->current_stock <= ($item->min_stock_threshold ?? 5);
                                @endphp
                                <tr class="{{ $isExpired ? 'table-danger' : ($isNearExpiry ? 'table-warning' : '') }}">
                                    <td>{{ $index + 1 }}</td>
                                    <td>
                                        <div class="fw-bold">{{ $item->name }}</div>
                                        <small class="text-muted">{{ $item->batch_number }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $item->product_type == 'Original' ? 'primary' : ($item->product_type == 'Premium' ? 'warning' : 'success') }}">
                                            {{ $item->product_type }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="fw-bold">{{ $item->current_stock }}</span> pcs
                                        @if($isLowStock)
                                            <br><small class="text-warning"><i class="fas fa-exclamation-triangle"></i> Stok Menipis</small>
                                        @endif
                                    </td>
                                    <td>Rp {{ number_format($item->selling_price, 0, ',', '.') }}</td>
                                    <td>
                                        @if($item->cost_per_item && $item->selling_price > 0)
                                            @php $margin = (($item->selling_price - $item->cost_per_item) / $item->selling_price) * 100; @endphp
                                            <span class="badge bg-{{ $margin < 20 ? 'danger' : ($margin < 40 ? 'warning' : 'success') }}">
                                                {{ number_format($margin, 1) }}%
                                            </span>
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        <div class="fw-bold">{{ \Carbon\Carbon::parse($item->expiry_date)->format('d/m/Y') }}</div>
                                        <small class="text-muted">
                                            @if($daysUntilExpiry < 0)
                                                <span class="text-danger">{{ abs($daysUntilExpiry) }} hari lalu</span>
                                            @elseif($daysUntilExpiry == 0)
                                                <span class="text-warning">Hari ini</span>
                                            @else
                                                <span class="text-{{ $daysUntilExpiry <= 3 ? 'warning' : 'success' }}">{{ $daysUntilExpiry }} hari lagi</span>
                                            @endif
                                        </small>
                                    </td>
                                    <td>
                                        @if($item->current_stock == 0)
                                            <span class="status-badge danger">Habis</span>
                                        @elseif($isExpired)
                                            <span class="status-badge danger">Kadaluarsa</span>
                                        @elseif($isNearExpiry)
                                            <span class="status-badge warning">Segera Kadaluarsa</span>
                                        @elseif($isLowStock)
                                            <span class="status-badge warning">Stok Menipis</span>
                                        @else
                                            <span class="status-badge success">Tersedia</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($item->priority_level)
                                            <span class="badge bg-{{ $item->priority_level == 'Tinggi' ? 'danger' : ($item->priority_level == 'Sedang' ? 'warning' : 'success') }}">
                                                {{ $item->priority_level }}
                                            </span>
                                            @if($item->needs_immediate_sale)
                                                <br><small class="text-danger"><i class="fas fa-exclamation-circle"></i> Jual Segera</small>
                                            @endif
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('processed-inventory.edit', $item) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('processed-inventory.show', $item) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form action="{{ route('processed-inventory.destroy', $item) }}" method="POST" class="d-inline" onsubmit="return confirm('Yakin ingin menghapus data ini?');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center">Tidak ada data produk ubi matang</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .page-title {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .page-title .fw-bold {
        font-size: 1.25rem;
    }
    
    .button-group .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 15px;
        border-radius: 5px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .button-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .btn-primary {
        background-color: #8B4513;
        border-color: #8B4513;
    }
    
    .btn-success {
        background-color: #4CAF50;
        border-color: #4CAF50;
    }
    
    .btn-info {
        background-color: #2196F3;
        border-color: #2196F3;
        color: white;
    }
    
    .btn-info:hover {
        color: white;
    }
    
    @media (max-width: 768px) {
        .page-title {
            flex-direction: column;
            align-items: flex-start !important;
        }
        
        .button-group {
            margin-top: 15px;
            width: 100%;
        }
        
        .button-group .btn {
            flex: 1;
            margin-bottom: 5px;
        }
    }
</style>

<script>
function filterTable(filterType) {
    const table = document.querySelector('.custom-table tbody');
    const rows = table.querySelectorAll('tr');
    const buttons = document.querySelectorAll('.btn-group .btn');

    // Remove active class from all buttons
    buttons.forEach(btn => btn.classList.remove('active'));

    // Add active class to clicked button
    event.target.classList.add('active');

    let visibleCount = 0;

    rows.forEach(row => {
        if (row.querySelector('td[colspan]')) return; // Skip empty row

        let shouldShow = false;

        switch(filterType) {
            case 'all':
                shouldShow = true;
                break;
            case 'expired':
                shouldShow = row.classList.contains('table-danger');
                break;
            case 'near-expiry':
                shouldShow = row.classList.contains('table-warning');
                break;
            case 'fresh':
                shouldShow = !row.classList.contains('table-danger') && !row.classList.contains('table-warning');
                break;
            case 'low-stock':
                shouldShow = row.querySelector('.text-warning') && row.querySelector('.text-warning').textContent.includes('Stok Menipis');
                break;
        }

        if (shouldShow) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Update empty message
    const emptyRow = table.querySelector('tr td[colspan]');
    if (emptyRow) {
        if (visibleCount === 0) {
            emptyRow.parentElement.style.display = '';
            emptyRow.textContent = `Tidak ada produk dengan filter "${getFilterName(filterType)}"`;
        } else {
            emptyRow.parentElement.style.display = 'none';
        }
    }
}

function getFilterName(filterType) {
    const names = {
        'all': 'Semua',
        'expired': 'Kadaluarsa',
        'near-expiry': 'Segera Kadaluarsa',
        'fresh': 'Masih Segar',
        'low-stock': 'Stok Menipis'
    };
    return names[filterType] || filterType;
}

// Auto-refresh expiry status every minute
setInterval(function() {
    location.reload();
}, 60000);

// Add tooltips for better UX
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
});
</script>
@endpush
@endsection