<?php

namespace App\Http\Controllers;

use App\Models\Distribution;
use App\Models\DistributionItem;
use App\Models\ProcessedInventory;
use App\Models\OtherProduct;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DistributionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Distribution::with(['user', 'items']);

        // Filter berdasarkan pencarian
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('distribution_number', 'like', "%{$search}%")
                  ->orWhere('destination', 'like', "%{$search}%")
                  ->orWhere('notes', 'like', "%{$search}%")
                  ->orWhereHas('user', function($u) use ($search) {
                      $u->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Filter berdasarkan status
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        // Filter berdasarkan tanggal
        if ($request->has('start_date') && $request->start_date) {
            $query->whereDate('distribution_date', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->whereDate('distribution_date', '<=', $request->end_date);
        }

        $distributions = $query->orderBy('created_at', 'desc')->paginate(10);

        return view('distributions.index', compact('distributions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $processedInventory = ProcessedInventory::where('current_stock', '>', 0)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('distributions.create', compact('processedInventory'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'destination' => 'required|string|max:255',
            'distribution_date' => 'required|date',
            'vehicle_info' => 'nullable|string|max:255',
            'driver_name' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.processed_inventory_id' => 'required|exists:processed_inventory,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);
        
        DB::beginTransaction();
        
        try {
            // Create distribution record
            $distribution = Distribution::create([
                'distribution_number' => 'DIST-' . date('YmdHis'),
                'user_id' => Auth::id(),
                'destination' => $validated['destination'],
                'distribution_date' => $validated['distribution_date'],
                'vehicle_info' => $validated['vehicle_info'],
                'driver_name' => $validated['driver_name'],
                'notes' => $validated['notes'],
                'status' => 'planned'
            ]);
            
            // Process items
            foreach ($validated['items'] as $item) {
                $product = ProcessedInventory::findOrFail($item['processed_inventory_id']);

                // Check if enough stock
                if ($product->current_stock < $item['quantity']) {
                    throw new \Exception("Stok tidak mencukupi untuk produk: {$product->name}");
                }

                // Create distribution item
                DistributionItem::create([
                    'distribution_id' => $distribution->id,
                    'processed_inventory_id' => $product->id,
                    'quantity' => $item['quantity'],
                    'price_per_item' => $product->selling_price,
                    'total_price' => $item['quantity'] * $product->selling_price
                ]);

                // Update stock
                $product->current_stock -= $item['quantity'];
                $product->save();
            }
            
            DB::commit();
            
            return redirect()->route('distributions.index')
                ->with('success', 'Distribusi berhasil dicatat');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Terjadi kesalahan: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Distribution $distribution)
    {
        $distribution->load(['user', 'items.processedInventory', 'items.otherProduct']);

        return view('distributions.show', compact('distribution'));
    }

    /**
     * Update the status of a distribution.
     */
    public function updateStatus(Request $request, Distribution $distribution)
    {
        $validated = $request->validate([
            'status' => 'required|in:planned,in_transit,delivered,returned'
        ]);
        
        $distribution->status = $validated['status'];
        
        // If returned, return items to inventory
        if ($validated['status'] === 'returned') {
            DB::beginTransaction();
            
            try {
                foreach ($distribution->items as $item) {
                    if ($item->processed_inventory_id) {
                        $product = $item->processedInventory;
                        $product->current_stock += $item->quantity;
                        $product->save();
                    } elseif ($item->other_product_id) {
                        $product = $item->otherProduct;
                        $product->current_stock += $item->quantity;
                        $product->save();
                    }
                }
                
                $distribution->save();
                DB::commit();
                
                return redirect()->route('distribution.show', $distribution)
                    ->with('success', 'Status distribusi berhasil diperbarui dan stok telah dikembalikan');
            } catch (\Exception $e) {
                DB::rollBack();
                return back()->withErrors(['error' => 'Terjadi kesalahan: ' . $e->getMessage()]);
            }
        } else {
            $distribution->save();
            
            return redirect()->route('distribution.show', $distribution)
                ->with('success', 'Status distribusi berhasil diperbarui');
        }
    }

    /**
     * Generate distribution report
     */
    public function report(Request $request)
    {
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));
        
        $distributions = Distribution::whereBetween('distribution_date', [$startDate, $endDate])
            ->with(['user', 'items.processedInventory', 'items.otherProduct'])
            ->orderBy('distribution_date', 'desc')
            ->get();
            
        $totalDistributed = $distributions->count();
        $totalItems = DistributionItem::whereHas('distribution', function($query) use ($startDate, $endDate) {
                $query->whereBetween('distribution_date', [$startDate, $endDate]);
            })->sum('quantity');
            
        $totalValue = DistributionItem::whereHas('distribution', function($query) use ($startDate, $endDate) {
                $query->whereBetween('distribution_date', [$startDate, $endDate]);
            })->sum('total_price');
        
        // Get daily distribution data for chart
        $dailyData = Distribution::whereBetween('distribution_date', [$startDate, $endDate])
            ->select(DB::raw('DATE(distribution_date) as date'), DB::raw('COUNT(*) as total'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $labels = $dailyData->pluck('date')->toArray();
        $data = $dailyData->pluck('total')->toArray();
        
        return view('distributions.report', compact(
            'distributions', 
            'startDate', 
            'endDate', 
            'totalDistributed',
            'totalItems',
            'totalValue',
            'labels',
            'data'
        ));
    }
}
