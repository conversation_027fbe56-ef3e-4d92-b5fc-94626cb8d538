<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Cek apakah user login dan rolenya admin
        if ($request->user() && $request->user()->isAdmin()) {
            return $next($request);
        }

        // Jika bukan admin, redirect ke home
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json(['message' => 'Tidak memiliki izin.'], 403);
        }

        return redirect()->route('home')->with('error', 'Anda tidak memiliki izin untuk mengakses halaman tersebut.');
    }
}