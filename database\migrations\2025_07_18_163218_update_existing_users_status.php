<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing users to have 'approved' status
        // This ensures existing users can still login after the approval system is implemented
        DB::table('users')
            ->whereNull('status')
            ->orWhere('status', '')
            ->update([
                'status' => 'approved',
                'approved_at' => now(),
                'updated_at' => now()
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert status back to null for users that were updated
        DB::table('users')
            ->where('status', 'approved')
            ->whereNotNull('approved_at')
            ->update([
                'status' => null,
                'approved_at' => null,
                'updated_at' => now()
            ]);
    }
};
