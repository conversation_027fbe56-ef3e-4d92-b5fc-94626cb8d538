<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    /**
     * Validate the user login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateLogin(Request $request)
    {
        // Basic validation
        $rules = [
            $this->username() => 'required|string',
            'password' => 'required|string',
            'role' => 'required|string',
        ];

        // Log the role being validated
        Log::debug('Login attempt with role: ' . $request->input('role'));
        
        $request->validate($rules);
    }

    /**
     * Attempt to log the user into the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {
        // Ambil semua kredensial yang dikirim tanpa role
        $credentials = $request->only($this->username(), 'password');
        $selectedRole = $request->input('role');
        
        // Log debug info
        Log::debug('Login attempt with credentials: ' . json_encode([
            'email' => $credentials['email'],
            'selected_role' => $selectedRole
        ]));
        
        // Coba login tanpa memeriksa role dulu
        $basicAuth = $this->guard()->attempt(
            $credentials, $request->filled('remember')
        );
        
        // Jika berhasil login, sekarang periksa role
        if ($basicAuth) {
            $user = $this->guard()->user();
            $userRole = $user->role;
            
            // Log debug info
            Log::debug("User authenticated: {$user->email} with role {$userRole}, requested role: {$selectedRole}");
            
            // Cek untuk admin
            if ($selectedRole === 'admin' && $userRole === 'admin') {
                return true;
            }
            
            // Cek untuk karyawan - terima baik 'employee' atau 'karyawan'
            if (($selectedRole === 'employee' || $selectedRole === 'karyawan') && 
                ($userRole === 'employee' || $userRole === 'karyawan')) {
                return true;
            }
            
            // Jika role tidak cocok, logout dan kembalikan false
            $this->guard()->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();
            
            Log::debug("Role mismatch: user role is {$userRole}, requested role is {$selectedRole}");
        } else {
            Log::debug("Authentication failed for: {$credentials['email']}");
        }
        
        return false;
    }

    /**
     * Get the failed login response instance.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function sendFailedLoginResponse(Request $request)
    {
        // Log the failure
        Log::debug('Login failed - generating response');
        
        // Check if user exists
        $user = \App\Models\User::where('email', $request->input('email'))->first();
        
        if (!$user) {
            Log::debug('User not found: ' . $request->input('email'));
            throw ValidationException::withMessages([
                $this->username() => ['Email tidak terdaftar dalam sistem.'],
            ]);
        }
        
        // Check password
        if (!\Illuminate\Support\Facades\Hash::check($request->input('password'), $user->password)) {
            Log::debug('Password incorrect for: ' . $request->input('email'));
            throw ValidationException::withMessages([
                'password' => ['Password yang Anda masukkan salah.'],
            ]);
        }
        
        // If we get here, the issue is with the role
        $userRole = $user->role;
        $selectedRole = $request->input('role');
        
        Log::debug("Role mismatch: User role is {$userRole}, selected role is {$selectedRole}");
        
        // Admin trying to log in as karyawan
        if (($selectedRole === 'employee' || $selectedRole === 'karyawan') && $userRole === 'admin') {
            throw ValidationException::withMessages([
                'role' => ["Akun admin tidak bisa login sebagai karyawan."],
            ]);
        }
        
        // Karyawan trying to log in as admin
        if ($selectedRole === 'admin' && ($userRole === 'employee' || $userRole === 'karyawan')) {
            throw ValidationException::withMessages([
                'role' => ["Akun karyawan tidak bisa login sebagai admin."],
            ]);
        }
        
        // Default error message
        throw ValidationException::withMessages([
            'email' => ['Kredensial yang Anda masukkan tidak cocok dengan catatan kami.'],
        ]);
    }

    /**
     * The user has been authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  mixed  $user
     * @return mixed
     */
    protected function authenticated(Request $request, $user)
    {
        // Log successful login
        Log::debug("Login successful: {$user->email} as {$user->role}");
        
        // Tambahkan pesan sukses
        session()->flash('success', "Selamat datang, {$user->name}! Anda berhasil login.");
        
        // Redirect berdasarkan role
        if ($user->role === 'admin') {
            return redirect()->route('dashboard');
        } elseif ($user->role === 'karyawan' || $user->role === 'employee') {
            return redirect()->route('transactions.index');
        }
        
        // Default fallback ke home
        return redirect()->route('home');
    }
}
