<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            // Add payment gateway fields
            $table->string('payment_gateway')->nullable()->after('payment_method');
            $table->string('payment_gateway_transaction_id')->nullable()->after('payment_gateway');
            $table->string('payment_gateway_order_id')->nullable()->after('payment_gateway_transaction_id');
            $table->string('payment_gateway_status')->nullable()->after('payment_gateway_order_id');
            $table->json('payment_gateway_response')->nullable()->after('payment_gateway_status');
            $table->string('snap_token')->nullable()->after('payment_gateway_response');
            $table->string('snap_redirect_url')->nullable()->after('snap_token');
            $table->timestamp('payment_gateway_paid_at')->nullable()->after('snap_redirect_url');
            $table->timestamp('payment_gateway_expired_at')->nullable()->after('payment_gateway_paid_at');

            // Add payment status field
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'cancelled', 'expired'])->default('pending')->after('status');
        });

        // Update payment_method enum to include 'gateway'
        DB::statement("ALTER TABLE transactions MODIFY COLUMN payment_method ENUM('cash', 'transfer', 'qris', 'debit', 'credit', 'gateway') NOT NULL");

        // Update status enum to include 'pending' and 'failed'
        DB::statement("ALTER TABLE transactions MODIFY COLUMN status ENUM('pending', 'completed', 'cancelled', 'refunded', 'failed') DEFAULT 'completed'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            // Remove payment gateway fields
            $table->dropColumn([
                'payment_gateway',
                'payment_gateway_transaction_id',
                'payment_gateway_order_id',
                'payment_gateway_status',
                'payment_gateway_response',
                'snap_token',
                'snap_redirect_url',
                'payment_gateway_paid_at',
                'payment_gateway_expired_at',
                'payment_status'
            ]);
        });

        // Revert back to original enums
        DB::statement("ALTER TABLE transactions MODIFY COLUMN payment_method ENUM('cash', 'transfer', 'qris', 'debit', 'credit') NOT NULL");
        DB::statement("ALTER TABLE transactions MODIFY COLUMN status ENUM('completed', 'cancelled', 'refunded') DEFAULT 'completed'");
    }
};
