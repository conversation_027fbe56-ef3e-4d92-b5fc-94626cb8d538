@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-fire-alt"></i>
        <span>Tambah Produk Ubi Matang</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Form Tambah Produk Ubi Matang</span>
                    <a href="{{ route('processed-inventory.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <form action="{{ route('processed-inventory.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Nama Produk <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="raw_inventory_id" class="form-label">Bahan Baku</label>
                                <select class="form-select @error('raw_inventory_id') is-invalid @enderror" id="raw_inventory_id" name="raw_inventory_id">
                                    <option value="">-- Pilih Bahan Baku --</option>
                                    @foreach($rawItems as $raw)
                                    <option value="{{ $raw->id }}" {{ old('raw_inventory_id') == $raw->id ? 'selected' : '' }}>
                                        {{ $raw->name }} (Stok: {{ $raw->current_stock }} kg)
                                    </option>
                                    @endforeach
                                </select>
                                @error('raw_inventory_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Opsional. Pilih jika produk ini dibuat dari ubi mentah tertentu.</small>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="current_stock" class="form-label">Stok Awal <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('current_stock') is-invalid @enderror" id="current_stock" name="current_stock" value="{{ old('current_stock', 0) }}" min="0" required>
                                @error('current_stock')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="cost_per_item" class="form-label">Biaya per Item (Rp)</label>
                                <input type="number" class="form-control @error('cost_per_item') is-invalid @enderror" id="cost_per_item" name="cost_per_item" value="{{ old('cost_per_item', 0) }}" min="0">
                                @error('cost_per_item')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="selling_price" class="form-label">Harga Jual (Rp) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('selling_price') is-invalid @enderror" id="selling_price" name="selling_price" value="{{ old('selling_price', 0) }}" min="0" required>
                                @error('selling_price')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="min_stock_threshold" class="form-label">Batas Minimum Stok</label>
                                <input type="number" class="form-control @error('min_stock_threshold') is-invalid @enderror" id="min_stock_threshold" name="min_stock_threshold" value="{{ old('min_stock_threshold', 5) }}" min="0">
                                <small class="form-text text-muted">Sistem akan memberikan peringatan saat stok di bawah nilai ini.</small>
                                @error('min_stock_threshold')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="raw_material_per_item" class="form-label">Berat Bahan Baku per Item (kg)</label>
                                <input type="number" class="form-control @error('raw_material_per_item') is-invalid @enderror" id="raw_material_per_item" name="raw_material_per_item" value="{{ old('raw_material_per_item', 0.2) }}" step="0.01" min="0">
                                <small class="form-text text-muted">Berapa kg ubi mentah yang digunakan untuk membuat 1 item produk ini.</small>
                                @error('raw_material_per_item')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Informasi Produksi dan Kadaluarsa -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Informasi Produksi & Kadaluarsa</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="production_date" class="form-label">Tanggal Produksi <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control @error('production_date') is-invalid @enderror" id="production_date" name="production_date" value="{{ old('production_date', date('Y-m-d')) }}" required>
                                        @error('production_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="expiry_date" class="form-label">Tanggal Kadaluarsa <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control @error('expiry_date') is-invalid @enderror" id="expiry_date" name="expiry_date" value="{{ old('expiry_date', date('Y-m-d', strtotime('+7 days'))) }}" required>
                                        @error('expiry_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Ubi bakar umumnya tahan 7 hari.</small>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="product_type" class="form-label">Jenis Produk <span class="text-danger">*</span></label>
                                        <select class="form-select @error('product_type') is-invalid @enderror" id="product_type" name="product_type" required>
                                            <option value="">-- Pilih Jenis Produk --</option>
                                            <option value="Original" {{ old('product_type') == 'Original' ? 'selected' : '' }}>Original</option>
                                            <option value="Premium" {{ old('product_type') == 'Premium' ? 'selected' : '' }}>Premium</option>
                                            <option value="Special" {{ old('product_type') == 'Special' ? 'selected' : '' }}>Special</option>
                                        </select>
                                        @error('product_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="notes" class="form-label">Catatan</label>
                                        <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3" placeholder="Catatan tambahan tentang produk ini...">{{ old('notes') }}</textarea>
                                        @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="is_active" class="form-label">Status Produk</label>
                                        <div class="form-check form-switch mt-2">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', '1') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_active">
                                                Produk Aktif
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">Produk aktif akan muncul dalam daftar penjualan.</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const productionDateInput = document.getElementById('production_date');
    const expiryDateInput = document.getElementById('expiry_date');
    const productTypeSelect = document.getElementById('product_type');

    // Auto-calculate expiry date based on production date and product type
    function calculateExpiryDate() {
        const productionDate = new Date(productionDateInput.value);
        const productType = productTypeSelect.value;

        if (productionDate && productType) {
            let daysToAdd = 7; // Default 7 days

            // Adjust days based on product type
            switch(productType) {
                case 'Original':
                    daysToAdd = 7;
                    break;
                case 'Premium':
                    daysToAdd = 5; // Premium products might have shorter shelf life due to toppings
                    break;
                case 'Special':
                    daysToAdd = 3; // Special products with dairy/cream have shorter shelf life
                    break;
            }

            const expiryDate = new Date(productionDate);
            expiryDate.setDate(expiryDate.getDate() + daysToAdd);

            // Format date to YYYY-MM-DD
            const formattedDate = expiryDate.toISOString().split('T')[0];
            expiryDateInput.value = formattedDate;
        }
    }

    // Event listeners
    productionDateInput.addEventListener('change', calculateExpiryDate);
    productTypeSelect.addEventListener('change', calculateExpiryDate);

    // Calculate margin when cost or selling price changes
    const costPerItemInput = document.getElementById('cost_per_item');
    const sellingPriceInput = document.getElementById('selling_price');

    function calculateMargin() {
        const cost = parseFloat(costPerItemInput.value) || 0;
        const selling = parseFloat(sellingPriceInput.value) || 0;

        if (cost > 0 && selling > 0) {
            const margin = ((selling - cost) / selling * 100).toFixed(1);

            // Create or update margin display
            let marginDisplay = document.getElementById('margin-display');
            if (!marginDisplay) {
                marginDisplay = document.createElement('small');
                marginDisplay.id = 'margin-display';
                marginDisplay.className = 'form-text text-info';
                sellingPriceInput.parentNode.appendChild(marginDisplay);
            }

            marginDisplay.textContent = `Margin: ${margin}%`;

            // Color coding for margin
            if (margin < 20) {
                marginDisplay.className = 'form-text text-danger';
            } else if (margin < 40) {
                marginDisplay.className = 'form-text text-warning';
            } else {
                marginDisplay.className = 'form-text text-success';
            }
        }
    }

    costPerItemInput.addEventListener('input', calculateMargin);
    sellingPriceInput.addEventListener('input', calculateMargin);

    // Initial calculation
    calculateExpiryDate();
    calculateMargin();
});
</script>
@endpush

@endsection