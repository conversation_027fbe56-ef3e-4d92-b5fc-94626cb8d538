@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-fire-alt"></i>
        <span>Tambah Produk Ubi Matang</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Form Tambah Produk Ubi Matang</span>
                    <a href="{{ route('processed-inventory.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <form action="{{ route('processed-inventory.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Nama Produk <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="raw_inventory_id" class="form-label">Bahan Baku</label>
                                <select class="form-select @error('raw_inventory_id') is-invalid @enderror" id="raw_inventory_id" name="raw_inventory_id">
                                    <option value="">-- Pilih Bahan Baku --</option>
                                    @foreach($rawItems as $raw)
                                    <option value="{{ $raw->id }}" {{ old('raw_inventory_id') == $raw->id ? 'selected' : '' }}>
                                        {{ $raw->name }} (Stok: {{ $raw->current_stock }} kg)
                                    </option>
                                    @endforeach
                                </select>
                                @error('raw_inventory_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Opsional. Pilih jika produk ini dibuat dari ubi mentah tertentu.</small>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="current_stock" class="form-label">Stok Awal <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('current_stock') is-invalid @enderror" id="current_stock" name="current_stock" value="{{ old('current_stock', 0) }}" min="0" required>
                                @error('current_stock')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="cost_per_item" class="form-label">Biaya per Item (Rp)</label>
                                <input type="number" class="form-control @error('cost_per_item') is-invalid @enderror" id="cost_per_item" name="cost_per_item" value="{{ old('cost_per_item', 0) }}" min="0">
                                @error('cost_per_item')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="selling_price" class="form-label">Harga Jual (Rp) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('selling_price') is-invalid @enderror" id="selling_price" name="selling_price" value="{{ old('selling_price', 0) }}" min="0" required>
                                @error('selling_price')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="min_stock_threshold" class="form-label">Batas Minimum Stok</label>
                                <input type="number" class="form-control @error('min_stock_threshold') is-invalid @enderror" id="min_stock_threshold" name="min_stock_threshold" value="{{ old('min_stock_threshold', 5) }}" min="0">
                                <small class="form-text text-muted">Sistem akan memberikan peringatan saat stok di bawah nilai ini.</small>
                                @error('min_stock_threshold')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="raw_material_per_item" class="form-label">Berat Bahan Baku per Item (kg)</label>
                                <input type="number" class="form-control @error('raw_material_per_item') is-invalid @enderror" id="raw_material_per_item" name="raw_material_per_item" value="{{ old('raw_material_per_item', 0.2) }}" step="0.01" min="0">
                                <small class="form-text text-muted">Berapa kg ubi mentah yang digunakan untuk membuat 1 item produk ini.</small>
                                @error('raw_material_per_item')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 