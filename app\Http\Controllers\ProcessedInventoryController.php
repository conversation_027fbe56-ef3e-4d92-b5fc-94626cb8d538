<?php

namespace App\Http\Controllers;

use App\Models\ProcessedInventory;
use App\Models\ProductionLog;
use App\Models\RawInventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ProcessedInventoryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $processedInventory = ProcessedInventory::with('rawMaterial')->orderBy('name')->get();

        // Update expiry tracking for all items
        foreach($processedInventory as $item) {
            $item->updateExpiryTracking();
        }

        $lowStock = ProcessedInventory::whereRaw('current_stock <= min_stock_threshold')->get();

        return view('inventory.processed.index', compact('processedInventory', 'lowStock'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $rawItems = RawInventory::orderBy('name')->get();
        return view('inventory.processed.create', compact('rawItems'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'current_stock' => 'required|integer|min:0',
            'selling_price' => 'required|numeric|min:0',
            'cost_per_item' => 'nullable|numeric|min:0',
            'min_stock_threshold' => 'nullable|integer|min:0',
            'raw_inventory_id' => 'nullable|exists:raw_inventory,id',
            'conversion_rate' => 'nullable|numeric|min:0',
            'production_date' => 'required|date|before_or_equal:today',
            'expiry_date' => 'required|date|after:production_date',
            'product_type' => 'required|in:Original,Premium,Special',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'nullable|boolean',
            'raw_material_per_item' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Prepare data with auto-generated batch number and required fields
        $data = $request->all();
        $data['batch_number'] = ProcessedInventory::generateBatchNumber();
        $data['quantity_processed_kg'] = $data['quantity_processed_kg'] ?? 0;
        $data['quantity_produced'] = $data['quantity_produced'] ?? $data['current_stock'];
        $data['cost_per_unit'] = $data['cost_per_unit'] ?? ($data['cost_per_item'] ?? 0);
        $data['is_active'] = $request->has('is_active') ? true : false;

        $processedInventory = ProcessedInventory::create($data);

        // Update expiry tracking after creation
        $processedInventory->updateExpiryTracking();

        return redirect()->route('processed-inventory.index')
            ->with('success', 'Produk ubi matang berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ProcessedInventory $processedInventory)
    {
        return view('inventory.processed.show', compact('processedInventory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProcessedInventory $processedInventory)
    {
        $rawItems = RawInventory::orderBy('name')->get();
        return view('inventory.processed.edit', compact('processedInventory', 'rawItems'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProcessedInventory $processedInventory)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'current_stock' => 'required|integer|min:0',
            'selling_price' => 'required|numeric|min:0',
            'cost_per_item' => 'nullable|numeric|min:0',
            'min_stock_threshold' => 'nullable|integer|min:0',
            'raw_inventory_id' => 'nullable|exists:raw_inventory,id',
            'conversion_rate' => 'nullable|numeric|min:0',
            'production_date' => 'required|date|before_or_equal:today',
            'expiry_date' => 'required|date|after:production_date',
            'product_type' => 'required|in:Original,Premium,Special',
            'notes' => 'nullable|string|max:1000',
            'is_active' => 'nullable|boolean',
            'raw_material_per_item' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Prepare data for update
        $data = $request->all();
        $data['is_active'] = $request->has('is_active') ? true : false;
        $data['cost_per_unit'] = $data['cost_per_unit'] ?? ($data['cost_per_item'] ?? 0);

        $processedInventory->update($data);

        // Update expiry tracking after update
        $processedInventory->updateExpiryTracking();

        return redirect()->route('processed-inventory.index')
            ->with('success', 'Produk ubi matang berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProcessedInventory $processedInventory)
    {
        try {
            // Check if this processed item is used in transactions
            if ($processedInventory->transactionItems()->count() > 0) {
                return redirect()->back()
                    ->with('error', 'Tidak dapat menghapus! Produk ini sudah digunakan dalam transaksi.');
            }

            $processedInventory->delete();

            return redirect()->route('processed-inventory.index')
                ->with('success', 'Produk ubi matang berhasil dihapus.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for processing raw inventory into processed inventory
     */
    public function showProcessForm()
    {
        $rawItems = RawInventory::where('current_stock', '>', 0)
                               ->where('is_active', true)
                               ->orderBy('name')
                               ->get();
        $processedItems = ProcessedInventory::orderBy('name')->get();

        return view('inventory.processed.process', compact('rawItems', 'processedItems'));
    }

    /**
     * Process raw inventory into processed inventory
     */
    public function process(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'raw_inventory_id' => 'required|exists:raw_inventory,id',
            'processed_inventory_id' => 'required|exists:processed_inventory,id',
            'raw_amount_used' => 'required|numeric|min:0.1',
            'produced_amount' => 'required|integer|min:1',
            'additional_cost' => 'nullable|numeric|min:0',
            'cost_per_item' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Get the raw and processed inventory items
        $rawItem = RawInventory::findOrFail($request->raw_inventory_id);
        $processedItem = ProcessedInventory::findOrFail($request->processed_inventory_id);

        // Check if there's enough raw inventory
        if ($rawItem->current_stock < $request->raw_amount_used) {
            return redirect()->back()
                ->with('error', 'Stok ubi mentah tidak mencukupi.')
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Calculate costs
            $rawCost = $rawItem->cost_per_kg * $request->raw_amount_used;
            $additionalCost = $request->additional_cost ?? 0;
            $totalCost = $rawCost + $additionalCost;
            $costPerItem = $request->cost_per_item ?? ($totalCost / $request->produced_amount);

            // Reduce raw inventory
            $rawItem->update([
                'current_stock' => $rawItem->current_stock - $request->raw_amount_used
            ]);

            // Increase processed inventory
            $processedItem->update([
                'current_stock' => $processedItem->current_stock + $request->produced_amount,
                'cost_per_item' => $costPerItem,
                'raw_inventory_id' => $rawItem->id,
                'conversion_rate' => $request->produced_amount / $request->raw_amount_used
            ]);

            // Create production log
            ProductionLog::create([
                'raw_inventory_id' => $rawItem->id,
                'processed_inventory_id' => $processedItem->id,
                'user_id' => Auth::id(),
                'raw_amount_used' => $request->raw_amount_used,
                'produced_amount' => $request->produced_amount,
                'raw_cost' => $rawCost,
                'additional_cost' => $additionalCost,
                'total_cost' => $totalCost,
                'cost_per_item' => $costPerItem,
                'raw_name' => $rawItem->name,
                'processed_name' => $processedItem->name,
            ]);

            DB::commit();

            return redirect()->route('processed-inventory.index')
                ->with('success', 'Proses produksi berhasil. ' . $request->produced_amount . ' ' . $processedItem->name . ' telah ditambahkan ke stok.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display production reports/history
     */
    public function productionReports(Request $request)
    {
        $query = ProductionLog::with(['rawInventory', 'processedInventory', 'user']);

        // Filter by date range if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('created_at', [
                $request->start_date . ' 00:00:00',
                $request->end_date . ' 23:59:59'
            ]);
        }

        // Filter by processed inventory if provided
        if ($request->has('processed_inventory_id') && $request->processed_inventory_id) {
            $query->where('processed_inventory_id', $request->processed_inventory_id);
        }

        // Filter by raw inventory if provided
        if ($request->has('raw_inventory_id') && $request->raw_inventory_id) {
            $query->where('raw_inventory_id', $request->raw_inventory_id);
        }

        $productionLogs = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get all processed and raw inventories for filter options
        $processedItems = ProcessedInventory::orderBy('name')->get();
        $rawItems = RawInventory::orderBy('name')->get();

        // Calculate totals
        $totalRawAmount = $productionLogs->sum('raw_amount_used');
        $totalProducedAmount = $productionLogs->sum('produced_amount');
        $totalCost = $productionLogs->sum('total_cost');

        return view('inventory.processed.reports', compact(
            'productionLogs',
            'processedItems',
            'rawItems',
            'totalRawAmount',
            'totalProducedAmount',
            'totalCost'
        ));
    }

    /**
     * Show expiry dashboard with detailed analytics
     */
    public function expiryDashboard()
    {
        // Get all active inventory with expiry tracking
        $items = ProcessedInventory::getExpiryTrackingList();

        // Group by priority levels
        $highPriority = $items->where('priority_level', 'Tinggi');
        $mediumPriority = $items->where('priority_level', 'Sedang');
        $lowPriority = $items->where('priority_level', 'Rendah');

        // Calculate statistics
        $stats = [
            'total_items' => $items->count(),
            'expired_items' => $items->where('days_until_expiry', '<', 0)->count(),
            'expiring_today' => $items->where('days_until_expiry', 0)->count(),
            'expiring_3_days' => $items->where('days_until_expiry', '<=', 3)->where('days_until_expiry', '>=', 0)->count(),
            'needs_immediate_sale' => $items->where('needs_immediate_sale', true)->count(),
            'total_value_at_risk' => $items->where('days_until_expiry', '<=', 3)->sum(function($item) {
                return $item->current_stock * $item->selling_price;
            })
        ];

        return view('inventory.processed.expiry-dashboard', compact(
            'items', 'highPriority', 'mediumPriority', 'lowPriority', 'stats'
        ));
    }

    /**
     * Get expiry data for AJAX requests
     */
    public function getExpiryData()
    {
        $items = ProcessedInventory::getExpiryTrackingList();

        return response()->json([
            'items' => $items->map(function($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'batch_number' => $item->batch_number,
                    'current_stock' => $item->current_stock,
                    'expiry_date' => $item->expiry_date->format('Y-m-d'),
                    'days_until_expiry' => $item->days_until_expiry,
                    'priority_level' => $item->priority_level,
                    'needs_immediate_sale' => $item->needs_immediate_sale,
                    'recommended_market' => $item->recommended_market,
                    'selling_price' => $item->selling_price,
                    'total_value' => $item->current_stock * $item->selling_price
                ];
            })
        ]);
    }
}
