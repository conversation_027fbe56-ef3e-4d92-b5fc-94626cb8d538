<?php

namespace App\Http\Controllers;

use App\Models\ProcessedInventory;
use App\Models\ProductionLog;
use App\Models\RawInventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ProcessedInventoryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $processedInventory = ProcessedInventory::with('rawMaterial')->orderBy('name')->get();
        $lowStock = ProcessedInventory::whereRaw('current_stock <= min_stock_threshold')->get();

        return view('inventory.processed.index', compact('processedInventory', 'lowStock'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $rawItems = RawInventory::orderBy('name')->get();
        return view('inventory.processed.create', compact('rawItems'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'current_stock' => 'required|integer|min:0',
            'selling_price' => 'required|numeric|min:0',
            'cost_per_item' => 'nullable|numeric|min:0',
            'min_stock_threshold' => 'nullable|integer|min:0',
            'raw_inventory_id' => 'nullable|exists:raw_inventory,id',
            'conversion_rate' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        ProcessedInventory::create($request->all());

        return redirect()->route('processed-inventory.index')
            ->with('success', 'Produk ubi matang berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(ProcessedInventory $processedInventory)
    {
        return view('inventory.processed.show', compact('processedInventory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProcessedInventory $processedInventory)
    {
        $rawItems = RawInventory::orderBy('name')->get();
        return view('inventory.processed.edit', compact('processedInventory', 'rawItems'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProcessedInventory $processedInventory)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'current_stock' => 'required|integer|min:0',
            'selling_price' => 'required|numeric|min:0',
            'cost_per_item' => 'nullable|numeric|min:0',
            'min_stock_threshold' => 'nullable|integer|min:0',
            'raw_inventory_id' => 'nullable|exists:raw_inventory,id',
            'conversion_rate' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $processedInventory->update($request->all());

        return redirect()->route('processed-inventory.index')
            ->with('success', 'Produk ubi matang berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProcessedInventory $processedInventory)
    {
        try {
            // Check if this processed item is used in transactions
            if ($processedInventory->transactionItems()->count() > 0) {
                return redirect()->back()
                    ->with('error', 'Tidak dapat menghapus! Produk ini sudah digunakan dalam transaksi.');
            }

            $processedInventory->delete();

            return redirect()->route('processed-inventory.index')
                ->with('success', 'Produk ubi matang berhasil dihapus.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for processing raw inventory into processed inventory
     */
    public function showProcessForm()
    {
        $rawItems = RawInventory::where('current_stock', '>', 0)
                               ->where('is_active', true)
                               ->orderBy('name')
                               ->get();
        $processedItems = ProcessedInventory::orderBy('name')->get();

        return view('inventory.processed.process', compact('rawItems', 'processedItems'));
    }

    /**
     * Process raw inventory into processed inventory
     */
    public function process(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'raw_inventory_id' => 'required|exists:raw_inventory,id',
            'processed_inventory_id' => 'required|exists:processed_inventory,id',
            'raw_amount_used' => 'required|numeric|min:0.1',
            'produced_amount' => 'required|integer|min:1',
            'additional_cost' => 'nullable|numeric|min:0',
            'cost_per_item' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Get the raw and processed inventory items
        $rawItem = RawInventory::findOrFail($request->raw_inventory_id);
        $processedItem = ProcessedInventory::findOrFail($request->processed_inventory_id);

        // Check if there's enough raw inventory
        if ($rawItem->current_stock < $request->raw_amount_used) {
            return redirect()->back()
                ->with('error', 'Stok ubi mentah tidak mencukupi.')
                ->withInput();
        }

        try {
            DB::beginTransaction();

            // Calculate costs
            $rawCost = $rawItem->cost_per_kg * $request->raw_amount_used;
            $additionalCost = $request->additional_cost ?? 0;
            $totalCost = $rawCost + $additionalCost;
            $costPerItem = $request->cost_per_item ?? ($totalCost / $request->produced_amount);

            // Reduce raw inventory
            $rawItem->update([
                'current_stock' => $rawItem->current_stock - $request->raw_amount_used
            ]);

            // Increase processed inventory
            $processedItem->update([
                'current_stock' => $processedItem->current_stock + $request->produced_amount,
                'cost_per_item' => $costPerItem,
                'raw_inventory_id' => $rawItem->id,
                'conversion_rate' => $request->produced_amount / $request->raw_amount_used
            ]);

            // Create production log
            ProductionLog::create([
                'raw_inventory_id' => $rawItem->id,
                'processed_inventory_id' => $processedItem->id,
                'user_id' => Auth::id(),
                'raw_amount_used' => $request->raw_amount_used,
                'produced_amount' => $request->produced_amount,
                'raw_cost' => $rawCost,
                'additional_cost' => $additionalCost,
                'total_cost' => $totalCost,
                'cost_per_item' => $costPerItem,
                'raw_name' => $rawItem->name,
                'processed_name' => $processedItem->name,
            ]);

            DB::commit();

            return redirect()->route('processed-inventory.index')
                ->with('success', 'Proses produksi berhasil. ' . $request->produced_amount . ' ' . $processedItem->name . ' telah ditambahkan ke stok.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Terjadi kesalahan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display production reports/history
     */
    public function productionReports(Request $request)
    {
        $query = ProductionLog::with(['rawInventory', 'processedInventory', 'user']);

        // Filter by date range if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('created_at', [
                $request->start_date . ' 00:00:00',
                $request->end_date . ' 23:59:59'
            ]);
        }

        // Filter by processed inventory if provided
        if ($request->has('processed_inventory_id') && $request->processed_inventory_id) {
            $query->where('processed_inventory_id', $request->processed_inventory_id);
        }

        // Filter by raw inventory if provided
        if ($request->has('raw_inventory_id') && $request->raw_inventory_id) {
            $query->where('raw_inventory_id', $request->raw_inventory_id);
        }

        $productionLogs = $query->orderBy('created_at', 'desc')->paginate(15);

        // Get all processed and raw inventories for filter options
        $processedItems = ProcessedInventory::orderBy('name')->get();
        $rawItems = RawInventory::orderBy('name')->get();

        // Calculate totals
        $totalRawAmount = $productionLogs->sum('raw_amount_used');
        $totalProducedAmount = $productionLogs->sum('produced_amount');
        $totalCost = $productionLogs->sum('total_cost');

        return view('inventory.processed.reports', compact(
            'productionLogs',
            'processedItems',
            'rawItems',
            'totalRawAmount',
            'totalProducedAmount',
            'totalCost'
        ));
    }
}
