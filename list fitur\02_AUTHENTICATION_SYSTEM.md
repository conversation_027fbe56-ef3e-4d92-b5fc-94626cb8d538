# 🔐 SISTEM AUTHENTICATION & AUTHORIZATION

## 🎯 **OVERVIEW**

Sistem authentication mengguna<PERSON> Laravel Auth dengan custom middleware untuk multi-role access control. Sistem ini memastikan keamanan data dan akses yang tepat sesuai dengan peran pengguna.

---

## 👥 **USER ROLES & PERMISSIONS**

### **1. 🔑 ADMIN ROLE**

**Credentials Default:**
- **Email:** <EMAIL>
- **Password:** password
- **Role:** admin

**Full Access Permissions:**
```php
// Admin dapat mengakses semua route
Route::middleware(['auth', AdminMiddleware::class])->group(function () {
    // Semua fitur admin
});
```

**Fitur yang Dapat Diakses:**
- ✅ **Dashboard Analytics** - Semua metrics dan reports
- ✅ **User Management** - CRUD users, role assignment
- ✅ **Raw Inventory** - <PERSON>aj<PERSON><PERSON> ubi mentah
- ✅ **Processed Inventory** - <PERSON><PERSON><PERSON><PERSON> ubi bakar
- ✅ **Production Management** - Proses produksi
- ✅ **Distribution Management** - Distribusi ke pasar
- ✅ **Financial Reports** - Laporan keuangan lengkap
- ✅ **Expense Management** - Manajemen pengeluaran
- ✅ **Audit Logs** - Log aktivitas sistem
- ✅ **System Settings** - Konfigurasi sistem

### **2. 👨‍💼 EMPLOYEE ROLE**

**Credentials Default:**
- **Email:** <EMAIL>
- **Password:** password
- **Role:** employee

**Limited Access Permissions:**
```php
// Employee hanya akses fitur operasional
Route::middleware(['auth'])->group(function () {
    // Fitur yang dibatasi untuk employee
});
```

**Fitur yang Dapat Diakses:**
- ✅ **Employee Dashboard** - Dashboard khusus karyawan
- ✅ **POS System** - Point of Sale untuk transaksi
- ✅ **Transaction Management** - Buat dan lihat transaksi
- ✅ **Inventory View** - Lihat stok (read-only)
- ✅ **Daily Sales Report** - Laporan penjualan harian
- ❌ **Production Management** - Tidak dapat akses
- ❌ **Financial Reports** - Tidak dapat akses detail
- ❌ **User Management** - Tidak dapat akses
- ❌ **System Settings** - Tidak dapat akses

### **3. 💰 CASHIER ROLE**

**Fokus pada Transaksi:**
- ✅ **POS Interface** - Interface kasir yang disederhanakan
- ✅ **Transaction Processing** - Proses pembayaran
- ✅ **Receipt Printing** - Cetak struk
- ✅ **Product Lookup** - Cari produk
- ❌ **Inventory Management** - Tidak dapat akses
- ❌ **Reports** - Tidak dapat akses

### **4. 📦 WAREHOUSE ROLE**

**Fokus pada Gudang:**
- ✅ **Inventory Management** - Kelola stok
- ✅ **Stock Movements** - Pencatatan pergerakan stok
- ✅ **Production Logs** - Log produksi
- ✅ **Expiry Alerts** - Peringatan kadaluarsa
- ❌ **Transactions** - Tidak dapat akses
- ❌ **Financial Reports** - Tidak dapat akses

---

## 🔒 **AUTHENTICATION FLOW**

### **1. Login Process**

**Login Controller:**
```php
class LoginController extends Controller
{
    public function login(Request $request)
    {
        // Validasi credentials
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'selected_role' => 'required|in:admin,employee'
        ]);

        // Attempt login
        if (Auth::attempt($credentials)) {
            // Redirect berdasarkan role
            return $this->redirectBasedOnRole();
        }

        return back()->withErrors(['email' => 'Invalid credentials']);
    }
}
```

**Role-Based Redirect:**
```php
private function redirectBasedOnRole()
{
    $user = Auth::user();
    
    if ($user->isAdmin()) {
        return redirect()->route('dashboard');
    } elseif ($user->isEmployee()) {
        return redirect()->route('employee.dashboard');
    }
    
    return redirect()->route('home');
}
```

### **2. Middleware Protection**

**AdminMiddleware:**
```php
class AdminMiddleware
{
    public function handle($request, Closure $next)
    {
        if (!Auth::check() || !Auth::user()->isAdmin()) {
            abort(403, 'Unauthorized access');
        }
        
        return $next($request);
    }
}
```

**Route Protection:**
```php
// Routes hanya untuk admin
Route::middleware(['auth', AdminMiddleware::class])->group(function () {
    Route::resource('raw-inventory', RawInventoryController::class);
    Route::resource('production', ProductionController::class);
    Route::get('/financial-reports', [FinancialReportController::class, 'index']);
});

// Routes untuk semua authenticated users
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index']);
    Route::resource('transactions', TransactionController::class);
    Route::get('/pos', [TransactionController::class, 'pos']);
});
```

---

## 🛡️ **SECURITY FEATURES**

### **1. Password Security**
- ✅ **Bcrypt Hashing** - Password di-hash dengan bcrypt
- ✅ **Minimum Length** - Password minimal 8 karakter
- ✅ **Password Reset** - Fitur reset password via email
- ✅ **Session Management** - Secure session handling

### **2. CSRF Protection**
```php
// Semua form dilindungi CSRF token
@csrf
<input type="hidden" name="_token" value="{{ csrf_token() }}">
```

### **3. Input Validation**
```php
// Validasi input di setiap request
$request->validate([
    'email' => 'required|email|unique:users',
    'password' => 'required|min:8|confirmed',
    'role' => 'required|in:admin,employee,cashier,warehouse'
]);
```

### **4. SQL Injection Prevention**
- ✅ **Eloquent ORM** - Menggunakan prepared statements
- ✅ **Parameter Binding** - Semua query menggunakan parameter binding
- ✅ **Input Sanitization** - Input di-sanitize sebelum disimpan

---

## 📊 **USER MANAGEMENT**

### **1. User Model**
```php
class User extends Authenticatable
{
    const ROLE_ADMIN = 'admin';
    const ROLE_EMPLOYEE = 'employee';
    const ROLE_CASHIER = 'cashier';
    const ROLE_WAREHOUSE = 'warehouse';

    protected $fillable = [
        'name', 'email', 'role', 'password', 'last_activity'
    ];

    public function isAdmin()
    {
        return $this->role === self::ROLE_ADMIN;
    }

    public function isEmployee()
    {
        return $this->role === self::ROLE_EMPLOYEE;
    }
}
```

### **2. Activity Tracking**
```php
// Track user activity
$user->update(['last_activity' => now()]);

// Audit logging
AuditLog::create([
    'user_id' => Auth::id(),
    'action' => 'login',
    'description' => 'User logged in',
    'ip_address' => request()->ip()
]);
```

---

## 🔄 **SESSION MANAGEMENT**

### **1. Session Configuration**
```php
// config/session.php
'lifetime' => 120, // 2 hours
'expire_on_close' => false,
'encrypt' => true,
'files' => storage_path('framework/sessions'),
```

### **2. Auto Logout**
```javascript
// Auto logout setelah inaktif
let inactivityTimer;
function resetTimer() {
    clearTimeout(inactivityTimer);
    inactivityTimer = setTimeout(logout, 7200000); // 2 hours
}

function logout() {
    window.location.href = '/logout';
}
```

---

## 🚪 **LOGIN INTERFACES**

### **1. Main Login Page**
- **Route:** `/login`
- **View:** `auth.login`
- **Features:**
  - Email/password authentication
  - Role selection
  - Remember me option
  - Password reset link

### **2. Employee Login Page**
- **Route:** `/karyawan-login`
- **View:** `auth.karyawan-login`
- **Features:**
  - Simplified interface for employees
  - Quick role selection
  - Mobile-friendly design

---

## 📱 **RESPONSIVE AUTHENTICATION**

### **Mobile-Friendly Features:**
- ✅ Touch-friendly login forms
- ✅ Auto-focus on input fields
- ✅ Keyboard optimization
- ✅ Biometric support (future)

### **Security on Mobile:**
- ✅ Secure session cookies
- ✅ HTTPS enforcement
- ✅ Auto-lock after inactivity
- ✅ Device fingerprinting

---

## 🔧 **TROUBLESHOOTING**

### **Common Issues:**
1. **403 Unauthorized** - Check user role and middleware
2. **Session Expired** - Increase session lifetime
3. **CSRF Token Mismatch** - Ensure @csrf in forms
4. **Login Loop** - Check redirect logic

### **Debug Commands:**
```bash
# Clear auth cache
php artisan auth:clear-resets

# Check user roles
php artisan tinker
>>> User::all()->pluck('email', 'role')

# Reset user password
>>> User::where('email', '<EMAIL>')->first()->update(['password' => bcrypt('newpassword')])
```

---

**🔐 Security Level:** High  
**🔄 Last Security Audit:** June 2025  
**📋 Compliance:** GDPR Ready
