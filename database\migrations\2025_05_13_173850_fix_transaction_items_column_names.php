<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Langkah 1: Menambahkan kolom baru
        Schema::table('transaction_items', function (Blueprint $table) {
            $table->unsignedBigInteger('processed_inventory_id')->after('product_id')->nullable();
        });

        // Langkah 2: Mengisi data dari product_id ke processed_inventory_id
        DB::statement('UPDATE transaction_items SET processed_inventory_id = product_id');

        // Langkah 3: Tambahkan foreign key baru
        Schema::table('transaction_items', function (Blueprint $table) {
            $table->foreign('processed_inventory_id')
                  ->references('id')
                  ->on('processed_inventory')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transaction_items', function (Blueprint $table) {
            $table->dropForeign(['processed_inventory_id']);
            $table->dropColumn('processed_inventory_id');
        });
    }
};
