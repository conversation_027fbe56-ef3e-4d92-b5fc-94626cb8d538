<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UpdatePasswordSeeder extends Seeder
{
    public function run()
    {
        // Update Admin password
        DB::table('users')
            ->where('email', '<EMAIL>')
            ->update([
                'password' => Hash::make('admin123')
            ]);

        // Update Karyawan password
        DB::table('users')
            ->where('email', '<EMAIL>')
            ->update([
                'password' => Hash::make('karyawan123')
            ]);
    }
} 