<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ubah enum role pada tabel users
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'employee', 'cashier', 'warehouse') DEFAULT 'employee'");

        // Update existing employee roles untuk kesesuaian dengan peran baru
        DB::table('users')
            ->where('role', 'employee')
            ->update(['role' => 'cashier']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Kembalikan nilai cashier dan warehouse ke employee
        DB::table('users')
            ->whereIn('role', ['cashier', 'warehouse'])
            ->update(['role' => 'employee']);

        // Kembalikan enum ke nilai semula
        DB::statement("ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'employee') DEFAULT 'employee'");
    }
};
