<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Auditable;

class Distribution extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'distribution_number',
        'user_id',
        'market_name',
        'distribution_date',
        'notes',
        'status'
    ];

    protected $casts = [
        'distribution_date' => 'date',
    ];

    /**
     * Generate a unique distribution number
     * Format: DIST-YYYYMMDD-XXXX
     */
    public static function generateDistributionNumber()
    {
        $date = now()->format('Ymd');
        $latestDistribution = static::whereDate('created_at', now()->format('Y-m-d'))
            ->latest()
            ->first();

        $sequence = $latestDistribution ? (int)substr($latestDistribution->distribution_number, -4) + 1 : 1;
        return 'DIST-' . $date . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the user who created this distribution.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the items in this distribution.
     */
    public function items(): HasMany
    {
        return $this->hasMany(DistributionItem::class);
    }

    /**
     * Calculate the total value of this distribution.
     */
    public function getTotalValueAttribute()
    {
        return $this->items->sum('total_price');
    }

    /**
     * Get the status badge HTML.
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'planned' => 'bg-info',
            'in_transit' => 'bg-warning',
            'delivered' => 'bg-success',
            'returned' => 'bg-danger'
        ];

        $class = $badges[$this->status] ?? 'bg-secondary';
        return '<span class="badge ' . $class . '">' . ucfirst($this->status) . '</span>';
    }
}
