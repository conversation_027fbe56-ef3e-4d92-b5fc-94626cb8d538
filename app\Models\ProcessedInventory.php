<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use App\Models\Traits\Auditable;
use App\Models\Distribution;

class ProcessedInventory extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $table = 'processed_inventory';

    protected $fillable = [
        'batch_number',
        'raw_inventory_id',
        'quantity_processed_kg',
        'quantity_produced',
        'cost_per_unit',
        'selling_price',
        'production_date',
        'expiry_date',
        'product_type',
        'current_stock',
        'is_active',
        'notes',
        'min_stock_threshold',
        'name',
        'cost_per_item',
        'raw_material_per_item',
        'priority_level',
        'needs_immediate_sale',
        'days_until_expiry',
        'recommended_market',
        'notification_sent',
        'last_notification_date',
        'image'
    ];

    protected $casts = [
        'production_date' => 'date',
        'expiry_date' => 'date',
        'is_active' => 'boolean',
        'needs_immediate_sale' => 'boolean',
        'notification_sent' => 'boolean',
        'last_notification_date' => 'datetime',
    ];

    /**
     * Get the raw inventory associated with this processed inventory.
     */
    public function rawInventory(): BelongsTo
    {
        return $this->belongsTo(RawInventory::class);
    }
    
    /**
     * Generate a unique batch number for processed inventory
     * Format: PROC-YYYYMMDDHHMMSS-XXX
     */
    public static function generateBatchNumber()
    {
        $timestamp = date('YmdHis');
        $random = rand(100, 999);
        $batchNumber = "PROC-{$timestamp}-{$random}";

        // Ensure uniqueness
        while (static::where('batch_number', $batchNumber)->exists()) {
            $random = rand(100, 999);
            $batchNumber = "PROC-{$timestamp}-{$random}";
        }

        return $batchNumber;
    }

    /**
     * Calculate days until expiry and update the field
     */
    public function calculateDaysUntilExpiry()
    {
        if ($this->expiry_date) {
            $today = now()->startOfDay();
            $expiry = $this->expiry_date->startOfDay();
            $this->days_until_expiry = $today->diffInDays($expiry, false);
            return $this->days_until_expiry;
        }
        return null;
    }
    
    /**
     * Update priority level based on days until expiry
     */
    public function updatePriorityLevel()
    {
        $days = $this->calculateDaysUntilExpiry();
        
        if ($days === null) {
            $this->priority_level = null;
            $this->needs_immediate_sale = false;
            return;
        }
        
        if ($days <= 3) {
            $this->priority_level = 'Tinggi';
            $this->needs_immediate_sale = true;
        } elseif ($days <= 7) {
            $this->priority_level = 'Sedang';
            $this->needs_immediate_sale = false;
        } else {
            $this->priority_level = 'Rendah';
            $this->needs_immediate_sale = false;
        }
        
        return $this->priority_level;
    }
    
    /**
     * Recommend market for distribution based on available markets
     */
    public function recommendMarket()
    {
        // Get frequently used markets from distributions
        $frequentMarkets = Distribution::select('market_name', DB::raw('COUNT(*) as count'))
            ->groupBy('market_name')
            ->orderByDesc('count')
            ->limit(3)
            ->get();
            
        if ($frequentMarkets->isNotEmpty()) {
            // Jika prioritas tinggi, rekomendasikan market teratas
            if ($this->priority_level === 'Tinggi') {
                $this->recommended_market = $frequentMarkets->first()->market_name;
            } 
            // Jika prioritas sedang, rekomendasikan market kedua jika ada
            elseif ($this->priority_level === 'Sedang' && $frequentMarkets->count() > 1) {
                $this->recommended_market = $frequentMarkets[1]->market_name;
            }
            // Jika prioritas rendah atau default, rekomendasikan market ketiga jika ada
            else {
                $this->recommended_market = $frequentMarkets->last()->market_name;
            }
        } else {
            $this->recommended_market = null;
        }
        
        return $this->recommended_market;
    }
    
    /**
     * Update expiry tracking data
     */
    public function updateExpiryTracking()
    {
        $this->updatePriorityLevel();
        $this->recommendMarket();
        $this->save();
        
        return $this;
    }
    
    /**
     * Scope a query to only include products that need immediate sale
     */
    public function scopeNeedsImmediateSale($query)
    {
        return $query->where('needs_immediate_sale', true)
                     ->where('current_stock', '>', 0);
    }
    
    /**
     * Scope a query to filter by priority level
     */
    public function scopeByPriority($query, $level)
    {
        return $query->where('priority_level', $level)
                     ->where('current_stock', '>', 0);
    }
    
    /**
     * Get all items with expiry tracking data updated
     */
    public static function getExpiryTrackingList()
    {
        // Get all active inventory with stock
        $items = self::where('is_active', true)
                    ->where('current_stock', '>', 0)
                    ->where('expiry_date', '!=', null)
                    ->get();
                    
        // Update expiry tracking for each item
        foreach($items as $item) {
            $item->updateExpiryTracking();
        }
        
        // Return fresh items sorted by priority
        return self::where('is_active', true)
                  ->where('current_stock', '>', 0)
                  ->where('expiry_date', '!=', null)
                  ->orderByRaw("FIELD(priority_level, 'Tinggi', 'Sedang', 'Rendah')")
                  ->orderBy('days_until_expiry')
                  ->get();
    }

    /**
     * Get the transaction items associated with this processed inventory.
     */
    public function transactionItems(): HasMany
    {
        return $this->hasMany(TransactionItem::class, 'processed_inventory_id');
    }

    /**
     * Get the raw material used for this processed inventory.
     */
    public function rawMaterial(): BelongsTo
    {
        return $this->belongsTo(RawInventory::class, 'raw_inventory_id');
    }
}
