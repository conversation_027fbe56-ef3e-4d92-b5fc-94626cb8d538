# 🔄 ACTIVITY DIAGRAM - SISTEM UBI BAKAR CILEMBU

## Activity Diagram - Business Process Flow (BPMN Format)

### 📋 **Deskripsi Activity Diagram**

Activity diagram ini menggambarkan alur proses bisnis dalam sistem berdasarkan use case yang telah didefinisikan menggunakan format BPMN.io yang dapat diimport langsung.

---

## 🎯 **BPMN XML untuk BPMN.io**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.0.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:collaboration id="Collaboration_1">
    <bpmn:participant id="Participant_Admin" name="ADMIN" processRef="Process_Admin" />
    <bpmn:participant id="Participant_Karyawan" name="KARYAWAN" processRef="Process_Karyawan" />
    <bpmn:participant id="Participant_System" name="SISTEM UBI BAKAR CILEMBU" processRef="Process_System" />
  </bpmn:collaboration>

  <!-- ADMIN PROCESS -->
  <bpmn:process id="Process_Admin" isExecutable="true">
    <bpmn:startEvent id="StartEvent_Admin" name="Admin Login">
      <bpmn:outgoing>Flow_AdminLogin</bpmn:outgoing>
    </bpmn:startEvent>

    <bpmn:task id="Task_AdminLogin" name="Akses Login">
      <bpmn:incoming>Flow_AdminLogin</bpmn:incoming>
      <bpmn:outgoing>Flow_AdminAuth</bpmn:outgoing>
    </bpmn:task>

    <bpmn:exclusiveGateway id="Gateway_AdminAuth" name="Authentication Success?">
      <bpmn:incoming>Flow_AdminAuth</bpmn:incoming>
      <bpmn:outgoing>Flow_AdminSuccess</bpmn:outgoing>
      <bpmn:outgoing>Flow_AdminFail</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:task id="Task_AdminDashboard" name="Akses Dashboard">
      <bpmn:incoming>Flow_AdminSuccess</bpmn:incoming>
      <bpmn:outgoing>Flow_AdminMenu</bpmn:outgoing>
    </bpmn:task>

    <bpmn:exclusiveGateway id="Gateway_AdminMenu" name="Pilih Menu">
      <bpmn:incoming>Flow_AdminMenu</bpmn:incoming>
      <bpmn:outgoing>Flow_AdminTransaksi</bpmn:outgoing>
      <bpmn:outgoing>Flow_AdminInventory</bpmn:outgoing>
      <bpmn:outgoing>Flow_AdminLaporan</bpmn:outgoing>
      <bpmn:outgoing>Flow_AdminLogout</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:task id="Task_AdminTransaksi" name="Akses Transaksi">
      <bpmn:incoming>Flow_AdminTransaksi</bpmn:incoming>
      <bpmn:outgoing>Flow_AdminTransaksiBack</bpmn:outgoing>
    </bpmn:task>

    <bpmn:task id="Task_AdminInventory" name="Akses Inventory">
      <bpmn:incoming>Flow_AdminInventory</bpmn:incoming>
      <bpmn:outgoing>Flow_AdminInventoryBack</bpmn:outgoing>
    </bpmn:task>

    <bpmn:task id="Task_AdminLaporan" name="Akses Laporan">
      <bpmn:incoming>Flow_AdminLaporan</bpmn:incoming>
      <bpmn:outgoing>Flow_AdminLaporanBack</bpmn:outgoing>
    </bpmn:task>

    <bpmn:task id="Task_AdminLogout" name="Logout">
      <bpmn:incoming>Flow_AdminLogout</bpmn:incoming>
      <bpmn:outgoing>Flow_AdminEnd</bpmn:outgoing>
    </bpmn:task>

    <bpmn:endEvent id="EndEvent_Admin" name="Admin End">
      <bpmn:incoming>Flow_AdminEnd</bpmn:incoming>
      <bpmn:incoming>Flow_AdminFail</bpmn:incoming>
    </bpmn:endEvent>

    <!-- Sequence Flows -->
    <bpmn:sequenceFlow id="Flow_AdminLogin" sourceRef="StartEvent_Admin" targetRef="Task_AdminLogin" />
    <bpmn:sequenceFlow id="Flow_AdminAuth" sourceRef="Task_AdminLogin" targetRef="Gateway_AdminAuth" />
    <bpmn:sequenceFlow id="Flow_AdminSuccess" name="Success" sourceRef="Gateway_AdminAuth" targetRef="Task_AdminDashboard" />
    <bpmn:sequenceFlow id="Flow_AdminFail" name="Failed" sourceRef="Gateway_AdminAuth" targetRef="EndEvent_Admin" />
    <bpmn:sequenceFlow id="Flow_AdminMenu" sourceRef="Task_AdminDashboard" targetRef="Gateway_AdminMenu" />
    <bpmn:sequenceFlow id="Flow_AdminTransaksi" name="Transaksi" sourceRef="Gateway_AdminMenu" targetRef="Task_AdminTransaksi" />
    <bpmn:sequenceFlow id="Flow_AdminInventory" name="Inventory" sourceRef="Gateway_AdminMenu" targetRef="Task_AdminInventory" />
    <bpmn:sequenceFlow id="Flow_AdminLaporan" name="Laporan" sourceRef="Gateway_AdminMenu" targetRef="Task_AdminLaporan" />
    <bpmn:sequenceFlow id="Flow_AdminLogout" name="Logout" sourceRef="Gateway_AdminMenu" targetRef="Task_AdminLogout" />
    <bpmn:sequenceFlow id="Flow_AdminTransaksiBack" sourceRef="Task_AdminTransaksi" targetRef="Gateway_AdminMenu" />
    <bpmn:sequenceFlow id="Flow_AdminInventoryBack" sourceRef="Task_AdminInventory" targetRef="Gateway_AdminMenu" />
    <bpmn:sequenceFlow id="Flow_AdminLaporanBack" sourceRef="Task_AdminLaporan" targetRef="Gateway_AdminMenu" />
    <bpmn:sequenceFlow id="Flow_AdminEnd" sourceRef="Task_AdminLogout" targetRef="EndEvent_Admin" />
  </bpmn:process>

  <!-- KARYAWAN PROCESS -->
  <bpmn:process id="Process_Karyawan" isExecutable="true">
    <bpmn:startEvent id="StartEvent_Karyawan" name="Karyawan Login">
      <bpmn:outgoing>Flow_KaryawanLogin</bpmn:outgoing>
    </bpmn:startEvent>

    <bpmn:task id="Task_KaryawanLogin" name="Akses Login">
      <bpmn:incoming>Flow_KaryawanLogin</bpmn:incoming>
      <bpmn:outgoing>Flow_KaryawanAuth</bpmn:outgoing>
    </bpmn:task>

    <bpmn:exclusiveGateway id="Gateway_KaryawanAuth" name="Authentication Success?">
      <bpmn:incoming>Flow_KaryawanAuth</bpmn:incoming>
      <bpmn:outgoing>Flow_KaryawanSuccess</bpmn:outgoing>
      <bpmn:outgoing>Flow_KaryawanFail</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:task id="Task_KaryawanDashboard" name="Akses Dashboard">
      <bpmn:incoming>Flow_KaryawanSuccess</bpmn:incoming>
      <bpmn:outgoing>Flow_KaryawanMenu</bpmn:outgoing>
    </bpmn:task>

    <bpmn:exclusiveGateway id="Gateway_KaryawanMenu" name="Pilih Menu">
      <bpmn:incoming>Flow_KaryawanMenu</bpmn:incoming>
      <bpmn:outgoing>Flow_KaryawanTransaksi</bpmn:outgoing>
      <bpmn:outgoing>Flow_KaryawanInventory</bpmn:outgoing>
      <bpmn:outgoing>Flow_KaryawanLogout</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:task id="Task_KaryawanTransaksi" name="Akses Transaksi">
      <bpmn:incoming>Flow_KaryawanTransaksi</bpmn:incoming>
      <bpmn:outgoing>Flow_KaryawanTransaksiBack</bpmn:outgoing>
    </bpmn:task>

    <bpmn:task id="Task_KaryawanInventory" name="Akses Inventory">
      <bpmn:incoming>Flow_KaryawanInventory</bpmn:incoming>
      <bpmn:outgoing>Flow_KaryawanInventoryBack</bpmn:outgoing>
    </bpmn:task>

    <bpmn:task id="Task_KaryawanLogout" name="Logout">
      <bpmn:incoming>Flow_KaryawanLogout</bpmn:incoming>
      <bpmn:outgoing>Flow_KaryawanEnd</bpmn:outgoing>
    </bpmn:task>

    <bpmn:endEvent id="EndEvent_Karyawan" name="Karyawan End">
      <bpmn:incoming>Flow_KaryawanEnd</bpmn:incoming>
      <bpmn:incoming>Flow_KaryawanFail</bpmn:incoming>
    </bpmn:endEvent>

    <!-- Sequence Flows -->
    <bpmn:sequenceFlow id="Flow_KaryawanLogin" sourceRef="StartEvent_Karyawan" targetRef="Task_KaryawanLogin" />
    <bpmn:sequenceFlow id="Flow_KaryawanAuth" sourceRef="Task_KaryawanLogin" targetRef="Gateway_KaryawanAuth" />
    <bpmn:sequenceFlow id="Flow_KaryawanSuccess" name="Success" sourceRef="Gateway_KaryawanAuth" targetRef="Task_KaryawanDashboard" />
    <bpmn:sequenceFlow id="Flow_KaryawanFail" name="Failed" sourceRef="Gateway_KaryawanAuth" targetRef="EndEvent_Karyawan" />
    <bpmn:sequenceFlow id="Flow_KaryawanMenu" sourceRef="Task_KaryawanDashboard" targetRef="Gateway_KaryawanMenu" />
    <bpmn:sequenceFlow id="Flow_KaryawanTransaksi" name="Transaksi" sourceRef="Gateway_KaryawanMenu" targetRef="Task_KaryawanTransaksi" />
    <bpmn:sequenceFlow id="Flow_KaryawanInventory" name="Inventory" sourceRef="Gateway_KaryawanMenu" targetRef="Task_KaryawanInventory" />
    <bpmn:sequenceFlow id="Flow_KaryawanLogout" name="Logout" sourceRef="Gateway_KaryawanMenu" targetRef="Task_KaryawanLogout" />
    <bpmn:sequenceFlow id="Flow_KaryawanTransaksiBack" sourceRef="Task_KaryawanTransaksi" targetRef="Gateway_KaryawanMenu" />
    <bpmn:sequenceFlow id="Flow_KaryawanInventoryBack" sourceRef="Task_KaryawanInventory" targetRef="Gateway_KaryawanMenu" />
    <bpmn:sequenceFlow id="Flow_KaryawanEnd" sourceRef="Task_KaryawanLogout" targetRef="EndEvent_Karyawan" />
  </bpmn:process>

  <!-- SYSTEM PROCESS -->
  <bpmn:process id="Process_System" isExecutable="true">
    <bpmn:startEvent id="StartEvent_System" name="System Start">
      <bpmn:outgoing>Flow_SystemReady</bpmn:outgoing>
    </bpmn:startEvent>

    <bpmn:task id="Task_Authentication" name="Proses Authentication">
      <bpmn:incoming>Flow_SystemReady</bpmn:incoming>
      <bpmn:outgoing>Flow_AuthResult</bpmn:outgoing>
    </bpmn:task>

    <bpmn:exclusiveGateway id="Gateway_UserRole" name="Check User Role">
      <bpmn:incoming>Flow_AuthResult</bpmn:incoming>
      <bpmn:outgoing>Flow_AdminRole</bpmn:outgoing>
      <bpmn:outgoing>Flow_KaryawanRole</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <bpmn:task id="Task_AdminAccess" name="Provide Admin Access">
      <bpmn:incoming>Flow_AdminRole</bpmn:incoming>
      <bpmn:outgoing>Flow_AdminComplete</bpmn:outgoing>
    </bpmn:task>

    <bpmn:task id="Task_KaryawanAccess" name="Provide Karyawan Access">
      <bpmn:incoming>Flow_KaryawanRole</bpmn:incoming>
      <bpmn:outgoing>Flow_KaryawanComplete</bpmn:outgoing>
    </bpmn:task>

    <bpmn:endEvent id="EndEvent_System" name="System End">
      <bpmn:incoming>Flow_AdminComplete</bpmn:incoming>
      <bpmn:incoming>Flow_KaryawanComplete</bpmn:incoming>
    </bpmn:endEvent>

    <!-- Sequence Flows -->
    <bpmn:sequenceFlow id="Flow_SystemReady" sourceRef="StartEvent_System" targetRef="Task_Authentication" />
    <bpmn:sequenceFlow id="Flow_AuthResult" sourceRef="Task_Authentication" targetRef="Gateway_UserRole" />
    <bpmn:sequenceFlow id="Flow_AdminRole" name="Admin" sourceRef="Gateway_UserRole" targetRef="Task_AdminAccess" />
    <bpmn:sequenceFlow id="Flow_KaryawanRole" name="Karyawan" sourceRef="Gateway_UserRole" targetRef="Task_KaryawanAccess" />
    <bpmn:sequenceFlow id="Flow_AdminComplete" sourceRef="Task_AdminAccess" targetRef="EndEvent_System" />
    <bpmn:sequenceFlow id="Flow_KaryawanComplete" sourceRef="Task_KaryawanAccess" targetRef="EndEvent_System" />
  </bpmn:process>
</bpmn:definitions>
```

---

## 📝 **Cara Import ke BPMN.io**

1. **Buka** https://bpmn.io/
2. **Klik** "Open File" atau "Import"
3. **Copy-paste** XML di atas atau save sebagai file `.bpmn`
4. **Import** file ke BPMN.io
5. **Edit** sesuai kebutuhan

---

## 🎯 **Process Flow Description**

### **🔐 Authentication Process**
- **Start Event**: User mengakses sistem
- **Login Task**: Input credentials
- **Gateway**: Validasi authentication
- **Role Check**: Sistem menentukan role user
- **Dashboard Access**: Redirect ke dashboard sesuai role

### **👨‍💼 Admin Process Flow**
1. **Login** → **Authentication** → **Dashboard**
2. **Menu Selection**: Transaksi, Inventory, Laporan, Logout
3. **Feature Access**: Full access ke semua fitur
4. **Return to Menu**: Kembali ke menu utama
5. **Logout**: Keluar dari sistem

### **👨‍💻 Karyawan Process Flow**
1. **Login** → **Authentication** → **Dashboard**
2. **Menu Selection**: Transaksi, Inventory (terbatas), Logout
3. **Limited Access**: Akses terbatas sesuai role
4. **Return to Menu**: Kembali ke menu utama
5. **Logout**: Keluar dari sistem

### **⚙️ System Process Flow**
1. **System Start**: Sistem siap menerima request
2. **Authentication**: Proses validasi user
3. **Role Check**: Menentukan level akses
4. **Access Provision**: Memberikan akses sesuai role
5. **System End**: Proses selesai

---

## 📊 **Key Features Covered**

✅ **Multi-Role Authentication** (Admin & Karyawan)
✅ **Role-Based Access Control**
✅ **Transaction Management**
✅ **Inventory Management**
✅ **Reporting System** (Admin only)
✅ **Secure Logout Process**
✅ **Error Handling** (Failed authentication)

---

**📊 BPMN Compliance:** Full
**🔄 Process Automation:** Enabled
**📱 Multi-User Support:** Complete
**🔐 Security Integration:** Implemented
