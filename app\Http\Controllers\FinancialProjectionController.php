<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Transaction;
use App\Models\OtherProduct;
use App\Models\ProcessedInventory;
use App\Models\Expense;
use App\Models\RawInventory;
use App\Exports\FinancialProjectionExport;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;
use PDF;

class FinancialProjectionController extends Controller
{
    public function index(Request $request)
    {
        // Default parameters
        $months = $request->query('months', 12);
        $growthRate = $request->query('growth_rate', 5.0);
        $costIncreaseRate = $request->query('cost_increase_rate', 3.0);
        
        // Mendapatkan data transaksi bulan lalu
        $lastMonth = Carbon::now()->subMonth();
        $lastMonthStart = $lastMonth->copy()->startOfMonth();
        $lastMonthEnd = $lastMonth->copy()->endOfMonth();
        
        $transactions = Transaction::whereBetween('created_at', [$lastMonthStart, $lastMonthEnd])->get();
        
        // Menghitung total pendapatan dan biaya bulan lalu
        $lastMonthRevenue = $transactions->sum('total_amount');
        $lastMonthCOGS = $transactions->sum('total_cost');
        
        // Mengambil biaya operasional (contoh: 30% dari pendapatan)
        $lastMonthOperationalCost = $lastMonthRevenue * 0.3;
        
        // Jika tidak ada data bulan lalu, gunakan nilai default
        if ($lastMonthRevenue <= 0) {
            $lastMonthRevenue = 10000000; // Contoh: Rp 10 juta
            $lastMonthCOGS = $lastMonthRevenue * 0.6; // Contoh: 60% dari pendapatan
            $lastMonthOperationalCost = $lastMonthRevenue * 0.3; // Contoh: 30% dari pendapatan
        }
        
        $lastMonthNetProfit = $lastMonthRevenue - $lastMonthCOGS - $lastMonthOperationalCost;
        
        // Membuat proyeksi
        $projections = [];
        $totalProjectedRevenue = 0;
        $totalProjectedProfit = 0;
        
        for ($i = 0; $i < $months; $i++) {
            $monthDate = Carbon::now()->addMonths($i + 1);
            $monthName = $monthDate->translatedFormat('F Y');
            
            // Proyeksi pendapatan dengan pertumbuhan bulanan
            $projectedRevenue = $lastMonthRevenue * pow(1 + ($growthRate / 100), $i + 1);
            
            // Proyeksi HPP
            $projectedCOGS = $lastMonthCOGS * pow(1 + ($costIncreaseRate / 100), $i + 1);
            
            // Proyeksi biaya operasional
            $projectedOperationalCost = $lastMonthOperationalCost * pow(1 + ($costIncreaseRate / 100), $i + 1);
            
            // Laba kotor dan bersih
            $projectedGrossProfit = $projectedRevenue - $projectedCOGS;
            $projectedNetProfit = $projectedGrossProfit - $projectedOperationalCost;
            
            // Margin laba
            $profitMargin = ($projectedNetProfit / $projectedRevenue) * 100;
            
            $projections[] = [
                'month' => $monthName,
                'revenue' => round($projectedRevenue),
                'cogs' => round($projectedCOGS),
                'gross_profit' => round($projectedGrossProfit),
                'operating_expense' => round($projectedOperationalCost),
                'net_profit' => round($projectedNetProfit),
                'profit_margin' => round($profitMargin, 1)
            ];
            
            $totalProjectedRevenue += $projectedRevenue;
            $totalProjectedProfit += $projectedNetProfit;
        }
        
        // Membuat skenario
        $scenarios = [
            'optimistic' => [
                'revenue_growth' => $growthRate * 1.5,
                'cost_increase' => $costIncreaseRate * 0.8,
                'total_revenue' => 0,
                'total_profit' => 0
            ],
            'normal' => [
                'revenue_growth' => $growthRate,
                'cost_increase' => $costIncreaseRate,
                'total_revenue' => $totalProjectedRevenue,
                'total_profit' => $totalProjectedProfit
            ],
            'pessimistic' => [
                'revenue_growth' => $growthRate * 0.6,
                'cost_increase' => $costIncreaseRate * 1.2,
                'total_revenue' => 0,
                'total_profit' => 0
            ]
        ];
        
        // Hitung skenario optimistik dan pesimistik
        $optimisticRevenue = 0;
        $optimisticProfit = 0;
        $pessimisticRevenue = 0;
        $pessimisticProfit = 0;
        
        for ($i = 0; $i < $months; $i++) {
            // Optimistik
            $optRevenue = $lastMonthRevenue * pow(1 + ($scenarios['optimistic']['revenue_growth'] / 100), $i + 1);
            $optCOGS = $lastMonthCOGS * pow(1 + ($scenarios['optimistic']['cost_increase'] / 100), $i + 1);
            $optOpCost = $lastMonthOperationalCost * pow(1 + ($scenarios['optimistic']['cost_increase'] / 100), $i + 1);
            $optProfit = $optRevenue - $optCOGS - $optOpCost;
            
            $optimisticRevenue += $optRevenue;
            $optimisticProfit += $optProfit;
            
            // Pesimistik
            $pessRevenue = $lastMonthRevenue * pow(1 + ($scenarios['pessimistic']['revenue_growth'] / 100), $i + 1);
            $pessCOGS = $lastMonthCOGS * pow(1 + ($scenarios['pessimistic']['cost_increase'] / 100), $i + 1);
            $pessOpCost = $lastMonthOperationalCost * pow(1 + ($scenarios['pessimistic']['cost_increase'] / 100), $i + 1);
            $pessProfit = $pessRevenue - $pessCOGS - $pessOpCost;
            
            $pessimisticRevenue += $pessRevenue;
            $pessimisticProfit += $pessProfit;
        }
        
        $scenarios['optimistic']['total_revenue'] = $optimisticRevenue;
        $scenarios['optimistic']['total_profit'] = $optimisticProfit;
        $scenarios['pessimistic']['total_revenue'] = $pessimisticRevenue;
        $scenarios['pessimistic']['total_profit'] = $pessimisticProfit;
        
        // Ringkasan
        $summary = [
            'total_projected_revenue' => round($totalProjectedRevenue),
            'total_projected_profit' => round($totalProjectedProfit),
            'average_monthly_revenue' => round($totalProjectedRevenue / $months),
            'average_monthly_profit' => round($totalProjectedProfit / $months),
            'average_profit_margin' => round(($totalProjectedProfit / $totalProjectedRevenue) * 100, 1)
        ];
        
        // Data untuk chart
        $projectionChartData = [
            'labels' => array_column($projections, 'month'),
            'revenue' => array_column($projections, 'revenue'),
            'expense' => array_map(function($item) {
                return $item['cogs'] + $item['operating_expense'];
            }, $projections),
            'profit' => array_column($projections, 'net_profit')
        ];
        
        return view('reports.financial-projection', compact(
            'projections', 'months', 'growthRate', 'costIncreaseRate',
            'summary', 'scenarios', 'projectionChartData'
        ));
    }

    /**
     * Export financial projection data to Excel
     */
    public function exportExcel(Request $request)
    {
        // Mendapatkan parameter proyeksi
        $months = $request->query('months', 12);
        $growthRate = $request->query('growth_rate', 5.0);
        $costIncreaseRate = $request->query('cost_increase_rate', 3.0);
        
        // Jalankan metode perhitungan yang sama dengan index
        list($projections, $scenarios, $summary) = $this->calculateProjection($months, $growthRate, $costIncreaseRate);
        
        $parameters = [
            'months' => $months,
            'growth_rate' => $growthRate,
            'cost_increase_rate' => $costIncreaseRate
        ];
        
        $fileName = 'proyeksi_keuangan_' . Carbon::now()->format('Y-m-d') . '.xlsx';
        
        return Excel::download(
            new FinancialProjectionExport($projections, $scenarios, $summary, $parameters),
            $fileName
        );
    }
    
    /**
     * Export financial projection data to PDF
     */
    public function exportPdf(Request $request)
    {
        // Mendapatkan parameter proyeksi
        $months = $request->query('months', 12);
        $growthRate = $request->query('growth_rate', 5.0);
        $costIncreaseRate = $request->query('cost_increase_rate', 3.0);
        
        // Jalankan metode perhitungan yang sama dengan index
        list($projections, $scenarios, $summary) = $this->calculateProjection($months, $growthRate, $costIncreaseRate);
        
        $data = [
            'projections' => $projections,
            'scenarios' => $scenarios,
            'summary' => $summary,
            'months' => $months,
            'growthRate' => $growthRate,
            'costIncreaseRate' => $costIncreaseRate,
            'date' => Carbon::now()->format('d F Y')
        ];
        
        $pdf = PDF::loadView('reports.financial-projection-pdf', $data);
        $fileName = 'proyeksi_keuangan_' . Carbon::now()->format('Y-m-d') . '.pdf';
        
        return $pdf->download($fileName);
    }
    
    /**
     * Calculate financial projection based on parameters
     * 
     * @param int $months Number of months to project
     * @param float $growthRate Revenue growth rate percentage
     * @param float $costIncreaseRate Cost increase rate percentage
     * @return array Returns array containing projections, scenarios, and summary
     */
    private function calculateProjection($months, $growthRate, $costIncreaseRate)
    {
        // Get historical data for better projection basis
        $historicalData = $this->getHistoricalData();
        
        // Use historical growth rates if available, otherwise use provided parameters
        $actualGrowthRate = $historicalData['growth_rate'] ?? $growthRate;
        $actualCostIncreaseRate = $historicalData['cost_increase_rate'] ?? $costIncreaseRate;
        
        // Get last month data
        $lastMonth = Carbon::now()->subMonth();
        $lastMonthStart = $lastMonth->copy()->startOfMonth();
        $lastMonthEnd = $lastMonth->copy()->endOfMonth();
        
        $transactions = Transaction::whereBetween('created_at', [$lastMonthStart, $lastMonthEnd])->get();
        
        // Calculate last month financials
        $lastMonthRevenue = $transactions->sum('total_amount');
        $lastMonthCOGS = $transactions->sum('total_cost');
        
        // Calculate operational expenses more accurately from actual expense data if available
        $expenses = Expense::whereBetween('date', [$lastMonthStart, $lastMonthEnd])->sum('amount');
        $lastMonthOperationalCost = $expenses > 0 ? $expenses : ($lastMonthRevenue * 0.3);
        
        // If no data for last month, use historical average or defaults
        if ($lastMonthRevenue <= 0) {
            $lastMonthRevenue = $historicalData['avg_monthly_revenue'] ?? 10000000;
            $lastMonthCOGS = $historicalData['avg_monthly_cogs'] ?? ($lastMonthRevenue * 0.6);
            $lastMonthOperationalCost = $historicalData['avg_monthly_expenses'] ?? ($lastMonthRevenue * 0.3);
        }
        
        $lastMonthNetProfit = $lastMonthRevenue - $lastMonthCOGS - $lastMonthOperationalCost;
        
        // Create projections
        $projections = [];
        $totalProjectedRevenue = 0;
        $totalProjectedProfit = 0;
        
        for ($i = 0; $i < $months; $i++) {
            $monthDate = Carbon::now()->addMonths($i + 1);
            $monthName = $monthDate->translatedFormat('F Y');
            
            // Seasonal adjustments - higher sales during holidays/festivals
            $seasonalFactor = $this->getSeasonalFactor($monthDate->month);
            
            // Projected revenue with seasonal adjustments
            $projectedRevenue = $lastMonthRevenue * pow(1 + ($actualGrowthRate / 100), $i + 1) * $seasonalFactor;
            
            // Projected COGS
            $projectedCOGS = $lastMonthCOGS * pow(1 + ($actualCostIncreaseRate / 100), $i + 1);
            
            // Projected operational expenses
            $projectedOperationalCost = $lastMonthOperationalCost * pow(1 + ($actualCostIncreaseRate / 100), $i + 1);
            
            // Profit calculations
            $projectedGrossProfit = $projectedRevenue - $projectedCOGS;
            $projectedNetProfit = $projectedGrossProfit - $projectedOperationalCost;
            $profitMargin = ($projectedNetProfit / $projectedRevenue) * 100;
            
            $projections[] = [
                'month' => $monthName,
                'revenue' => round($projectedRevenue),
                'cogs' => round($projectedCOGS),
                'gross_profit' => round($projectedGrossProfit),
                'operating_expense' => round($projectedOperationalCost),
                'net_profit' => round($projectedNetProfit),
                'profit_margin' => round($profitMargin, 1)
            ];
            
            $totalProjectedRevenue += $projectedRevenue;
            $totalProjectedProfit += $projectedNetProfit;
        }
        
        // Create scenario analysis
        $scenarios = [
            'optimistic' => [
                'revenue_growth' => $actualGrowthRate * 1.5,
                'cost_increase' => $actualCostIncreaseRate * 0.8,
                'total_revenue' => 0,
                'total_profit' => 0
            ],
            'normal' => [
                'revenue_growth' => $actualGrowthRate,
                'cost_increase' => $actualCostIncreaseRate,
                'total_revenue' => $totalProjectedRevenue,
                'total_profit' => $totalProjectedProfit
            ],
            'pessimistic' => [
                'revenue_growth' => $actualGrowthRate * 0.6,
                'cost_increase' => $actualCostIncreaseRate * 1.2,
                'total_revenue' => 0,
                'total_profit' => 0
            ]
        ];
        
        // Calculate scenario values
        $optimisticRevenue = 0;
        $optimisticProfit = 0;
        $pessimisticRevenue = 0;
        $pessimisticProfit = 0;
        
        for ($i = 0; $i < $months; $i++) {
            // Seasonal factor for this month
            $monthDate = Carbon::now()->addMonths($i + 1);
            $seasonalFactor = $this->getSeasonalFactor($monthDate->month);
            
            // Optimistic scenario
            $optRevenue = $lastMonthRevenue * pow(1 + ($scenarios['optimistic']['revenue_growth'] / 100), $i + 1) * $seasonalFactor;
            $optCOGS = $lastMonthCOGS * pow(1 + ($scenarios['optimistic']['cost_increase'] / 100), $i + 1);
            $optOpCost = $lastMonthOperationalCost * pow(1 + ($scenarios['optimistic']['cost_increase'] / 100), $i + 1);
            $optProfit = $optRevenue - $optCOGS - $optOpCost;
            
            $optimisticRevenue += $optRevenue;
            $optimisticProfit += $optProfit;
            
            // Pessimistic scenario
            $pessRevenue = $lastMonthRevenue * pow(1 + ($scenarios['pessimistic']['revenue_growth'] / 100), $i + 1) * $seasonalFactor;
            $pessCOGS = $lastMonthCOGS * pow(1 + ($scenarios['pessimistic']['cost_increase'] / 100), $i + 1);
            $pessOpCost = $lastMonthOperationalCost * pow(1 + ($scenarios['pessimistic']['cost_increase'] / 100), $i + 1);
            $pessProfit = $pessRevenue - $pessCOGS - $pessOpCost;
            
            $pessimisticRevenue += $pessRevenue;
            $pessimisticProfit += $pessProfit;
        }
        
        $scenarios['optimistic']['total_revenue'] = $optimisticRevenue;
        $scenarios['optimistic']['total_profit'] = $optimisticProfit;
        $scenarios['pessimistic']['total_revenue'] = $pessimisticRevenue;
        $scenarios['pessimistic']['total_profit'] = $pessimisticProfit;
        
        // Create summary
        $summary = [
            'total_projected_revenue' => round($totalProjectedRevenue),
            'total_projected_profit' => round($totalProjectedProfit),
            'average_monthly_revenue' => round($totalProjectedRevenue / $months),
            'average_monthly_profit' => round($totalProjectedProfit / $months),
            'average_profit_margin' => round(($totalProjectedProfit / $totalProjectedRevenue) * 100, 1),
            'roi' => round(($totalProjectedProfit / ($lastMonthCOGS * $months)) * 100, 1),
            'break_even_point' => $this->calculateBreakEvenPoint($lastMonthCOGS, $lastMonthOperationalCost, $lastMonthRevenue)
        ];
        
        return [$projections, $scenarios, $summary];
    }
    
    /**
     * Get seasonal factor for a given month
     * Adjusts revenue projection based on historical seasonal trends
     */
    private function getSeasonalFactor($month)
    {
        // Define seasonal factors based on historical data or business knowledge
        // 1.0 is baseline (no adjustment), >1.0 means higher sales, <1.0 means lower sales
        $seasonalFactors = [
            1 => 1.0,  // January
            2 => 0.9,  // February
            3 => 1.0,  // March
            4 => 1.0,  // April
            5 => 1.0,  // May
            6 => 1.05, // June - slight increase (school holidays)
            7 => 1.1,  // July - higher (school holidays)
            8 => 1.15, // August - higher (national holiday)
            9 => 1.0,  // September
            10 => 1.0, // October
            11 => 1.1, // November - higher (year-end festivities beginning)
            12 => 1.3  // December - highest (holiday season)
        ];
        
        return $seasonalFactors[$month] ?? 1.0;
    }
    
    /**
     * Get historical financial data for better projections
     */
    private function getHistoricalData()
    {
        // Get data from the last 6 months for trend analysis
        $sixMonthsAgo = Carbon::now()->subMonths(6)->startOfMonth();
        $today = Carbon::now();
        
        // Get monthly revenue data
        $monthlyRevenues = [];
        $monthlyCOGS = [];
        $monthlyExpenses = [];
        
        for ($i = 0; $i < 6; $i++) {
            $startDate = Carbon::now()->subMonths(6 - $i)->startOfMonth();
            $endDate = Carbon::now()->subMonths(6 - $i)->endOfMonth();
            
            // Get transactions for this month
            $transactions = Transaction::whereBetween('created_at', [$startDate, $endDate])->get();
            $revenue = $transactions->sum('total_amount');
            $cogs = $transactions->sum('total_cost');
            
            // Get expenses for this month
            $expenses = Expense::whereBetween('date', [$startDate, $endDate])->sum('amount');
            
            if ($revenue > 0) {
                $monthlyRevenues[] = $revenue;
                $monthlyCOGS[] = $cogs;
                $monthlyExpenses[] = $expenses > 0 ? $expenses : ($revenue * 0.3);
            }
        }
        
        // If we don't have enough data, return empty array
        if (count($monthlyRevenues) < 2) {
            return [];
        }
        
        // Calculate average monthly values
        $avgMonthlyRevenue = array_sum($monthlyRevenues) / count($monthlyRevenues);
        $avgMonthlyCOGS = array_sum($monthlyCOGS) / count($monthlyCOGS);
        $avgMonthlyExpenses = array_sum($monthlyExpenses) / count($monthlyExpenses);
        
        // Calculate average monthly growth rate
        $growthRates = [];
        for ($i = 1; $i < count($monthlyRevenues); $i++) {
            if ($monthlyRevenues[$i-1] > 0) {
                $growthRates[] = (($monthlyRevenues[$i] - $monthlyRevenues[$i-1]) / $monthlyRevenues[$i-1]) * 100;
            }
        }
        $avgGrowthRate = count($growthRates) > 0 ? array_sum($growthRates) / count($growthRates) : 5.0;
        
        // Calculate average monthly cost increase rate
        $costIncreaseRates = [];
        for ($i = 1; $i < count($monthlyCOGS); $i++) {
            if ($monthlyCOGS[$i-1] > 0) {
                $costIncreaseRates[] = (($monthlyCOGS[$i] - $monthlyCOGS[$i-1]) / $monthlyCOGS[$i-1]) * 100;
            }
        }
        $avgCostIncreaseRate = count($costIncreaseRates) > 0 ? array_sum($costIncreaseRates) / count($costIncreaseRates) : 3.0;
        
        return [
            'avg_monthly_revenue' => $avgMonthlyRevenue,
            'avg_monthly_cogs' => $avgMonthlyCOGS,
            'avg_monthly_expenses' => $avgMonthlyExpenses,
            'growth_rate' => $avgGrowthRate,
            'cost_increase_rate' => $avgCostIncreaseRate
        ];
    }
    
    /**
     * Calculate break-even point based on financial data
     */
    private function calculateBreakEvenPoint($fixedCosts, $variableCosts, $revenue)
    {
        // Simplified break-even calculation
        if ($revenue <= 0) {
            return 0;
        }
        
        $contributionMargin = $revenue - $variableCosts;
        $contributionMarginRatio = $contributionMargin / $revenue;
        
        if ($contributionMarginRatio <= 0) {
            return 0;
        }
        
        return round($fixedCosts / $contributionMarginRatio);
    }
}