# 🏗️ CLASS DIAGRAM - SISTEM UBI BAKAR CILEMBU

## Class Diagram - Laravel Models dan Relationships

### 📋 **Deskripsi Class Diagram**

Class diagram ini menggambarkan struktur kelas dan relasi antar kelas dalam sistem berdasarkan Laravel Models yang sudah diimplementasi:

#### **🔐 User Management**
- **User**: Model utama untuk authentication dan authorization dengan multi-role system

#### **💰 Transaction System**
- **Transaction**: Model transaksi dengan payment gateway integration
- **TransactionItem**: Detail item dalam setiap transaksi

#### **📦 Inventory Management**
- **RawInventory**: Man<PERSON><PERSON><PERSON> bahan mentah (ubi mentah)
- **ProcessedInventory**: Manajemen produk jadi (ubi bakar)
- **OtherProduct**: Manajemen produk lainnya (minuman, snack, dll)

#### **🏭 Production System**
- **ProductionProcess**: Proses produksi dari raw ke processed
- **ProductionLog**: Log detail produksi per batch

#### **🚚 Distribution System**
- **Distribution**: Distribusi ke berbagai pasar
- **DistributionItem**: Detail item yang didistribusikan

#### **🏢 Supporting Classes**
- **Supplier**: Manajemen pemasok bahan mentah
- **Expense**: Pencatatan pengeluaran operasional
- **AuditLog**: Tracking semua aktivitas sistem

```mermaid
classDiagram
    %% User Management Classes
    class User {
        +int id
        +string name
        +string email
        +string password
        +enum role
        +datetime last_activity
        +datetime email_verified_at
        +string remember_token
        +datetime created_at
        +datetime updated_at
        +datetime deleted_at
        --
        +isAdmin() bool
        +isEmployee() bool
        +isCashier() bool
        +isWarehouse() bool
        +transactions() HasMany
    }
    
    %% Transaction Classes
    class Transaction {
        +int id
        +string invoice_number
        +int user_id
        +string customer_name
        +string customer_phone
        +decimal subtotal
        +decimal tax
        +decimal discount
        +decimal total_amount
        +decimal amount_paid
        +decimal change_amount
        +enum payment_method
        +string payment_gateway
        +string payment_gateway_transaction_id
        +string snap_token
        +enum status
        +text notes
        +datetime created_at
        +datetime updated_at
        +datetime deleted_at
        --
        +user() BelongsTo
        +items() HasMany
        +isPaidViaGateway() bool
        +isPendingPayment() bool
    }
    
    class TransactionItem {
        +int id
        +int transaction_id
        +int product_id
        +int processed_inventory_id
        +string product_name
        +decimal price
        +int quantity
        +decimal discount
        +decimal subtotal
        +datetime created_at
        +datetime updated_at
        --
        +transaction() BelongsTo
        +product() BelongsTo
        +processedInventory() BelongsTo
        +otherProduct() BelongsTo
    }
    
    %% Inventory Classes
    class RawInventory {
        +int id
        +string batch_number
        +string supplier_name
        +decimal quantity_kg
        +decimal cost_per_kg
        +decimal total_cost
        +date purchase_date
        +date expiry_date
        +enum quality
        +text notes
        +decimal current_stock
        +boolean is_active
        +decimal min_stock_threshold
        +string name
        +datetime created_at
        +datetime updated_at
        +datetime deleted_at
        --
        +processedInventory() HasMany
        +supplier() BelongsTo
    }
    
    class ProcessedInventory {
        +int id
        +string batch_number
        +int raw_inventory_id
        +decimal quantity_processed_kg
        +int quantity_produced
        +decimal cost_per_unit
        +decimal selling_price
        +date production_date
        +date expiry_date
        +string product_type
        +int current_stock
        +boolean is_active
        +text notes
        +int min_stock_threshold
        +string name
        +string image
        +int priority_level
        +boolean needs_immediate_sale
        +int days_until_expiry
        +string recommended_market
        +datetime created_at
        +datetime updated_at
        +datetime deleted_at
        --
        +rawInventory() BelongsTo
        +transactionItems() HasMany
        +calculateDaysUntilExpiry() int
        +isLowStock() bool
    }
    
    class OtherProduct {
        +int id
        +string name
        +string sku
        +text description
        +decimal purchase_price
        +decimal selling_price
        +int current_stock
        +int min_stock_threshold
        +string category
        +string unit
        +string supplier
        +boolean is_active
        +text notes
        +string image
        +datetime created_at
        +datetime updated_at
        +datetime deleted_at
        --
        +transactionItems() HasMany
        +isLowStock() bool
        +getProfit() decimal
    }
    
    %% Production Classes
    class ProductionLog {
        +int id
        +string batch_number
        +int raw_inventory_id
        +decimal raw_quantity_used
        +int produced_quantity
        +decimal production_cost
        +date production_date
        +enum quality_grade
        +text notes
        +datetime created_at
        +datetime updated_at
        --
        +rawInventory() BelongsTo
    }
    
    class ProductionProcess {
        +int id
        +string process_number
        +int user_id
        +date planned_date
        +date actual_date
        +enum status
        +decimal total_cost
        +text notes
        +datetime created_at
        +datetime updated_at
        +datetime deleted_at
        --
        +user() BelongsTo
        +productionLogs() HasMany
    }
    
    %% Distribution Classes
    class Distribution {
        +int id
        +string distribution_number
        +int user_id
        +string market_name
        +date distribution_date
        +text notes
        +enum status
        +datetime created_at
        +datetime updated_at
        +datetime deleted_at
        --
        +user() BelongsTo
        +items() HasMany
        +generateDistributionNumber() string
    }
    
    class DistributionItem {
        +int id
        +int distribution_id
        +int processed_inventory_id
        +int other_product_id
        +int quantity
        +decimal price_per_item
        +decimal total_price
        +datetime created_at
        +datetime updated_at
        +datetime deleted_at
        --
        +distribution() BelongsTo
        +processedInventory() BelongsTo
        +otherProduct() BelongsTo
    }
    
    %% Supplier Class
    class Supplier {
        +int id
        +string name
        +string contact_person
        +string phone_number
        +string email
        +text address
        +text notes
        +boolean is_active
        +datetime created_at
        +datetime updated_at
        +datetime deleted_at
        --
        +rawInventory() HasMany
    }
    
    %% Expense Class
    class Expense {
        +int id
        +string expense_number
        +int user_id
        +string category
        +string description
        +decimal amount
        +date expense_date
        +text notes
        +datetime created_at
        +datetime updated_at
        +datetime deleted_at
        --
        +user() BelongsTo
    }
    
    %% Audit Log Class
    class AuditLog {
        +int id
        +int user_id
        +string action
        +string model_type
        +int model_id
        +json old_values
        +json new_values
        +string ip_address
        +string user_agent
        +datetime created_at
        --
        +user() BelongsTo
    }
    
    %% Relationships
    User ||--o{ Transaction : "creates"
    User ||--o{ ProductionProcess : "manages"
    User ||--o{ Distribution : "handles"
    User ||--o{ Expense : "records"
    User ||--o{ AuditLog : "generates"
    
    Transaction ||--o{ TransactionItem : "contains"
    
    TransactionItem }o--|| ProcessedInventory : "references"
    TransactionItem }o--|| OtherProduct : "references"
    
    RawInventory ||--o{ ProcessedInventory : "processed_into"
    RawInventory ||--o{ ProductionLog : "used_in"
    
    ProcessedInventory ||--o{ DistributionItem : "distributed_as"
    OtherProduct ||--o{ DistributionItem : "distributed_as"
    
    Distribution ||--o{ DistributionItem : "contains"
    
    ProductionProcess ||--o{ ProductionLog : "includes"
    
    Supplier ||--o{ RawInventory : "supplies"
    
    %% Styling
    classDef userClass fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef transactionClass fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef inventoryClass fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef productionClass fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef distributionClass fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef supportClass fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    
    class User userClass
    class Transaction,TransactionItem transactionClass
    class RawInventory,ProcessedInventory,OtherProduct inventoryClass
    class ProductionLog,ProductionProcess productionClass
    class Distribution,DistributionItem distributionClass
    class Supplier,Expense,AuditLog supportClass
```
