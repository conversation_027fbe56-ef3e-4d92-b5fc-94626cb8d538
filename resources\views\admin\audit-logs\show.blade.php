@extends('layouts.app')

@section('title', 'Detail Aktivitas')

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Detail Aktivitas</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.audit-logs.index') }}">Catatan Aktivitas</a></li>
        <li class="breadcrumb-item active">Detail</li>
    </ol>

    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i>
                    Informasi Umum
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">ID:</div>
                        <div class="col-md-9">{{ $auditLog->id }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Pengguna:</div>
                        <div class="col-md-9">{{ $auditLog->user ? $auditLog->user->name : 'Sistem' }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Aksi:</div>
                        <div class="col-md-9">
                            <span class="badge bg-{{ $auditLog->action == 'create' ? 'success' : ($auditLog->action == 'update' ? 'primary' : ($auditLog->action == 'delete' ? 'danger' : 'warning')) }}">
                                @if($auditLog->action == 'create')
                                    Tambah
                                @elseif($auditLog->action == 'update')
                                    Ubah
                                @elseif($auditLog->action == 'delete')
                                    Hapus
                                @else
                                    {{ ucfirst($auditLog->action) }}
                                @endif
                            </span>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Jenis Aktivitas:</div>
                        <div class="col-md-9">
                            @if(class_basename($auditLog->model_type) == 'User')
                                Pengguna
                            @elseif(class_basename($auditLog->model_type) == 'RawInventory')
                                Stok Ubi Mentah
                            @elseif(class_basename($auditLog->model_type) == 'ProcessedInventory')
                                Stok Ubi Bakar
                            @elseif(class_basename($auditLog->model_type) == 'OtherProduct')
                                Produk Lainnya
                            @elseif(class_basename($auditLog->model_type) == 'Transaction')
                                Transaksi
                            @elseif(class_basename($auditLog->model_type) == 'ProductionLog')
                                Log Produksi
                            @else
                                {{ class_basename($auditLog->model_type) }}
                            @endif
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Deskripsi:</div>
                        <div class="col-md-9">
                            @if(class_basename($auditLog->model_type) == 'User')
                                @if($auditLog->action == 'create')
                                    Menambahkan pengguna baru
                                @elseif($auditLog->action == 'update')
                                    Mengubah data pengguna
                                @elseif($auditLog->action == 'delete')
                                    Menghapus pengguna
                                @endif
                            @elseif(class_basename($auditLog->model_type) == 'RawInventory')
                                @if($auditLog->action == 'create')
                                    Menambahkan stok ubi mentah baru
                                @elseif($auditLog->action == 'update')
                                    Mengubah data stok ubi mentah
                                @elseif($auditLog->action == 'delete')
                                    Menghapus stok ubi mentah
                                @endif
                            @elseif(class_basename($auditLog->model_type) == 'ProcessedInventory')
                                @if($auditLog->action == 'create')
                                    Menambahkan stok ubi bakar baru
                                @elseif($auditLog->action == 'update')
                                    Mengubah data stok ubi bakar
                                @elseif($auditLog->action == 'delete')
                                    Menghapus stok ubi bakar
                                @endif
                            @elseif(class_basename($auditLog->model_type) == 'OtherProduct')
                                @if($auditLog->action == 'create')
                                    Menambahkan produk baru
                                @elseif($auditLog->action == 'update')
                                    Mengubah data produk
                                @elseif($auditLog->action == 'delete')
                                    Menghapus produk
                                @endif
                            @elseif(class_basename($auditLog->model_type) == 'Transaction')
                                @if($auditLog->action == 'create')
                                    Membuat transaksi baru
                                @elseif($auditLog->action == 'update')
                                    Mengubah data transaksi
                                @elseif($auditLog->action == 'delete')
                                    Menghapus transaksi
                                @endif
                            @elseif(class_basename($auditLog->model_type) == 'ProductionLog')
                                @if($auditLog->action == 'create')
                                    Mencatat produksi baru
                                @elseif($auditLog->action == 'update')
                                    Mengubah data produksi
                                @elseif($auditLog->action == 'delete')
                                    Menghapus catatan produksi
                                @endif
                            @else
                                {{ class_basename($auditLog->model_type) }} - {{ $auditLog->action }}
                            @endif
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3 fw-bold">Waktu:</div>
                        <div class="col-md-9">{{ $auditLog->created_at->format('d M Y H:i:s') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($auditLog->old_values || $auditLog->new_values)
    <div class="row">
        <div class="col-xl-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-exchange-alt me-1"></i>
                    Detail Perubahan Data
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Kolom</th>
                                    <th>Nilai Lama</th>
                                    <th>Nilai Baru</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if($auditLog->action == 'update')
                                    @foreach($auditLog->new_values as $key => $value)
                                        <tr>
                                            <td>{{ $key }}</td>
                                            <td>{{ isset($auditLog->old_values[$key]) ? $auditLog->old_values[$key] : '-' }}</td>
                                            <td>{{ $value }}</td>
                                        </tr>
                                    @endforeach
                                @elseif($auditLog->action == 'create')
                                    @foreach($auditLog->new_values as $key => $value)
                                        <tr>
                                            <td>{{ $key }}</td>
                                            <td>-</td>
                                            <td>{{ $value }}</td>
                                        </tr>
                                    @endforeach
                                @elseif($auditLog->action == 'delete')
                                    @foreach($auditLog->old_values as $key => $value)
                                        <tr>
                                            <td>{{ $key }}</td>
                                            <td>{{ $value }}</td>
                                            <td>-</td>
                                        </tr>
                                    @endforeach
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <div class="mb-4">
        <a href="{{ route('admin.audit-logs.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Kembali ke Daftar Aktivitas
        </a>
    </div>
</div>
@endsection
