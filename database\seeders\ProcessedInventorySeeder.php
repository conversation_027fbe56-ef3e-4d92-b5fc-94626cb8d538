<?php

namespace Database\Seeders;

use App\Models\ProcessedInventory;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class ProcessedInventorySeeder extends Seeder
{
    public function run(): void
    {
        $products = [
            [
                'name' => 'Ubi Bakar Original',
                'batch_number' => 'UBO-001',
                'quantity_produced' => 100,
                'current_stock' => 100,
                'cost_per_unit' => 5000,
                'selling_price' => 8000,
                'production_date' => Carbon::now(),
                'expiry_date' => Carbon::now()->addDays(7),
                'product_type' => 'Original',
                'is_active' => true,
                'min_stock_threshold' => 20,
                'notes' => 'Ubi bakar original dengan tekstur lembut',
                'cost_per_item' => 5000,
                'raw_material_per_item' => 0.25, // dalam kg
                'quantity_processed_kg' => 25, // 100 item * 0.25 kg
            ],
            [
                'name' => 'Ubi Bakar Coklat',
                'batch_number' => 'UBC-001',
                'quantity_produced' => 100,
                'current_stock' => 100,
                'cost_per_unit' => 6000,
                'selling_price' => 10000,
                'production_date' => Carbon::now(),
                'expiry_date' => Carbon::now()->addDays(7),
                'product_type' => 'Premium',
                'is_active' => true,
                'min_stock_threshold' => 20,
                'notes' => 'Ubi bakar dengan topping coklat premium',
                'cost_per_item' => 6000,
                'raw_material_per_item' => 0.25, // dalam kg
                'quantity_processed_kg' => 25, // 100 item * 0.25 kg
            ],
            [
                'name' => 'Ubi Bakar Keju',
                'batch_number' => 'UBK-001',
                'quantity_produced' => 100,
                'current_stock' => 100,
                'cost_per_unit' => 6000,
                'selling_price' => 10000,
                'production_date' => Carbon::now(),
                'expiry_date' => Carbon::now()->addDays(7),
                'product_type' => 'Premium',
                'is_active' => true,
                'min_stock_threshold' => 20,
                'notes' => 'Ubi bakar dengan topping keju melimpah',
                'cost_per_item' => 6000,
                'raw_material_per_item' => 0.25, // dalam kg
                'quantity_processed_kg' => 25, // 100 item * 0.25 kg
            ],
            [
                'name' => 'Ubi Bakar Spesial',
                'batch_number' => 'UBS-001',
                'quantity_produced' => 100,
                'current_stock' => 100,
                'cost_per_unit' => 7000,
                'selling_price' => 12000,
                'production_date' => Carbon::now(),
                'expiry_date' => Carbon::now()->addDays(7),
                'product_type' => 'Special',
                'is_active' => true,
                'min_stock_threshold' => 20,
                'notes' => 'Ubi bakar dengan topping coklat dan keju',
                'cost_per_item' => 7000,
                'raw_material_per_item' => 0.25, // dalam kg
                'quantity_processed_kg' => 25, // 100 item * 0.25 kg
            ],
        ];

        foreach ($products as $product) {
            ProcessedInventory::create($product);
        }
    }
} 