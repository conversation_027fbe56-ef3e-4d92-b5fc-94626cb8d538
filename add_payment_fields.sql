-- Add payment gateway fields to transactions table
ALTER TABLE transactions 
ADD COLUMN payment_gateway VARCHAR(255) NULL AFTER payment_method,
ADD COLUMN payment_gateway_transaction_id VARCHAR(255) NULL AFTER payment_gateway,
ADD COLUMN payment_gateway_order_id VARCHAR(255) NULL AFTER payment_gateway_transaction_id,
ADD COLUMN payment_gateway_status VARCHAR(255) NULL AFTER payment_gateway_order_id,
ADD COLUMN payment_gateway_response JSON NULL AFTER payment_gateway_status,
ADD COLUMN snap_token VARCHAR(255) NULL AFTER payment_gateway_response,
ADD COLUMN snap_redirect_url VARCHAR(500) NULL AFTER snap_token,
ADD COLUMN payment_gateway_paid_at TIMESTAMP NULL AFTER snap_redirect_url,
ADD COLUMN payment_gateway_expired_at TIMESTAMP NULL AFTER payment_gateway_paid_at,
ADD COLUMN payment_status ENUM('pending', 'paid', 'failed', 'cancelled', 'expired') DEFAULT 'pending' AFTER status;

-- Update payment_method enum to include 'gateway'
ALTER TABLE transactions MODIFY COLUMN payment_method ENUM('cash', 'transfer', 'qris', 'debit', 'credit', 'gateway') NOT NULL;

-- Update status enum to include 'pending' and 'failed'
ALTER TABLE transactions MODIFY COLUMN status ENUM('pending', 'completed', 'cancelled', 'refunded', 'failed') DEFAULT 'completed';

-- Update existing transactions to have proper payment_status
UPDATE transactions SET payment_status = 'paid' WHERE status = 'completed' AND payment_method != 'gateway';
UPDATE transactions SET payment_status = 'pending' WHERE status = 'pending';
