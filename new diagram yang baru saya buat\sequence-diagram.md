# 🔄 SEQUENCE DIAGRAM - SISTEM UBI BAKAR CILEMBU

## Sequence Diagram - Interaction Flow

### 📋 **Deskripsi Sequence Diagram**

Sequence diagram ini menggambarkan interaksi antar objek dalam sistem berdasarkan use case yang telah didefinisikan. Diagram ini menunjukkan alur komunikasi antara user, controller, model, dan service dalam berbagai skenario.

---

## 🎯 **Sequence Diagrams**

### **1. Authentication Sequence - Login Process**

```mermaid
sequenceDiagram
    participant User as 👤 User
    participant Browser as 🌐 Browser
    participant LoginController as 🔐 LoginController
    participant AuthMiddleware as 🛡️ AuthMiddleware
    participant UserModel as 👥 User Model
    participant Database as 🗄️ Database
    participant Dashboard as 📊 Dashboard

    User->>Browser: Akses halaman login
    Browser->>LoginController: GET /login
    LoginController-->>Browser: Tampilkan form login
    Browser-->>User: Form login

    User->>Browser: Input email & password
    Browser->>LoginController: POST /login {email, password}

    LoginController->>UserModel: validateCredentials(email, password)
    UserModel->>Database: SELECT * FROM users WHERE email = ?
    Database-->>UserModel: User data
    UserModel->>UserModel: Hash::check(password, user.password)

    alt Valid Credentials
        UserModel-->>LoginController: User object with role
        LoginController->>AuthMiddleware: authenticate(user)
        AuthMiddleware-->>LoginController: Authentication success

        alt Admin Role
            LoginController->>Dashboard: redirect('/dashboard')
            Dashboard-->>Browser: Admin Dashboard
            Browser-->>User: Admin Dashboard Interface
        else Karyawan Role
            LoginController->>Dashboard: redirect('/employee/dashboard')
            Dashboard-->>Browser: Employee Dashboard
            Browser-->>User: Employee Dashboard Interface
        end

    else Invalid Credentials
        UserModel-->>LoginController: Authentication failed
        LoginController-->>Browser: Error message
        Browser-->>User: Login error
    end
```

### **2. Transaction Creation Sequence - POS System**

```mermaid
sequenceDiagram
    participant User as 👤 User
    participant Browser as 🌐 Browser
    participant TransactionController as 💰 TransactionController
    participant ProcessedInventory as 📦 ProcessedInventory
    participant OtherProduct as 🛍️ OtherProduct
    participant MidtransService as 💳 MidtransService
    participant Database as 🗄️ Database

    User->>Browser: Akses POS System
    Browser->>TransactionController: GET /transactions/pos

    TransactionController->>ProcessedInventory: getAvailableProducts()
    ProcessedInventory->>Database: SELECT * FROM processed_inventory WHERE is_active = 1
    Database-->>ProcessedInventory: Product list
    ProcessedInventory-->>TransactionController: Available products

    TransactionController->>OtherProduct: getActiveProducts()
    OtherProduct->>Database: SELECT * FROM other_products WHERE is_active = 1
    Database-->>OtherProduct: Other products
    OtherProduct-->>TransactionController: Other products list

    TransactionController-->>Browser: POS Interface with products
    Browser-->>User: POS System Interface

    User->>Browser: Pilih produk & quantity
    Browser->>TransactionController: POST /cart/add {product_id, quantity}

    TransactionController->>ProcessedInventory: checkStock(product_id, quantity)
    ProcessedInventory->>Database: SELECT current_stock FROM processed_inventory WHERE id = ?
    Database-->>ProcessedInventory: Stock data

    alt Stock Available
        ProcessedInventory-->>TransactionController: Stock sufficient
        TransactionController-->>Browser: Product added to cart
        Browser-->>User: Cart updated

        User->>Browser: Proses pembayaran
        Browser->>TransactionController: POST /transactions/process {cart_data, payment_method}

        alt Payment Gateway
            TransactionController->>MidtransService: createSnapToken(transaction_data)
            MidtransService->>MidtransService: Generate snap token
            MidtransService-->>TransactionController: Snap token & redirect URL
            TransactionController-->>Browser: Redirect to Midtrans
            Browser-->>User: Midtrans payment page

            User->>MidtransService: Complete payment
            MidtransService->>TransactionController: Payment callback
            TransactionController->>Database: UPDATE transactions SET payment_status = 'paid'

        else Manual Payment
            TransactionController->>Database: INSERT INTO transactions
            Database-->>TransactionController: Transaction saved
        end

        TransactionController->>ProcessedInventory: updateStock(product_id, -quantity)
        ProcessedInventory->>Database: UPDATE processed_inventory SET current_stock = current_stock - ?

        TransactionController->>Database: INSERT INTO transaction_items
        TransactionController-->>Browser: Transaction completed
        Browser-->>User: Receipt & success message

    else Stock Insufficient
        ProcessedInventory-->>TransactionController: Stock insufficient
        TransactionController-->>Browser: Stock error
        Browser-->>User: Stock tidak mencukupi
    end
```

### **3. Inventory Management Sequence**

```mermaid
sequenceDiagram
    participant User as 👤 User (Admin)
    participant Browser as 🌐 Browser
    participant InventoryController as 📦 InventoryController
    participant ProcessedInventory as 🍠 ProcessedInventory
    participant RawInventory as 🥔 RawInventory
    participant NotificationService as 🔔 NotificationService
    participant Database as 🗄️ Database

    User->>Browser: Akses Inventory Management
    Browser->>InventoryController: GET /inventory

    InventoryController->>ProcessedInventory: getAllWithStock()
    ProcessedInventory->>Database: SELECT * FROM processed_inventory
    Database-->>ProcessedInventory: Processed inventory data
    ProcessedInventory-->>InventoryController: Processed items

    InventoryController->>RawInventory: getAllWithStock()
    RawInventory->>Database: SELECT * FROM raw_inventory
    Database-->>RawInventory: Raw inventory data
    RawInventory-->>InventoryController: Raw items

    InventoryController-->>Browser: Inventory dashboard
    Browser-->>User: Inventory management interface

    User->>Browser: Tambah inventory baru
    Browser->>InventoryController: POST /inventory/processed {inventory_data}

    InventoryController->>InventoryController: validateData(inventory_data)

    alt Valid Data
        InventoryController->>Database: INSERT INTO processed_inventory
        Database-->>InventoryController: Item saved with ID

        InventoryController->>NotificationService: checkLowStock(new_item)

        alt Low Stock Detected
            NotificationService->>NotificationService: generateAlert(item)
            NotificationService-->>User: Low stock notification
        end

        InventoryController-->>Browser: Success message
        Browser-->>User: Item berhasil ditambahkan

    else Invalid Data
        InventoryController-->>Browser: Validation errors
        Browser-->>User: Error messages
    end
```

### **4. Report Generation Sequence**

```mermaid
sequenceDiagram
    participant User as 👤 User (Admin)
    participant Browser as 🌐 Browser
    participant ReportController as 📊 ReportController
    participant Transaction as 💰 Transaction
    participant TransactionItem as 📝 TransactionItem
    participant ExportService as 📤 ExportService
    participant Database as 🗄️ Database

    User->>Browser: Akses Laporan
    Browser->>ReportController: GET /reports/sales
    ReportController-->>Browser: Report form
    Browser-->>User: Form filter laporan

    User->>Browser: Submit filter {date_range, type}
    Browser->>ReportController: POST /reports/sales/generate

    ReportController->>Transaction: getTransactionsByDateRange(start_date, end_date)
    Transaction->>Database: SELECT * FROM transactions WHERE created_at BETWEEN ? AND ?
    Database-->>Transaction: Transaction data
    Transaction-->>ReportController: Transactions list

    ReportController->>TransactionItem: getItemsByTransactions(transaction_ids)
    TransactionItem->>Database: SELECT * FROM transaction_items WHERE transaction_id IN (?)
    Database-->>TransactionItem: Transaction items
    TransactionItem-->>ReportController: Items data

    ReportController->>ReportController: processReportData(transactions, items)

    User->>Browser: Request export
    Browser->>ReportController: GET /reports/export {format}

    ReportController->>ExportService: generateReport(data, format)

    alt PDF Export
        ExportService->>ExportService: generatePDF(data)
        ExportService-->>ReportController: PDF file
    else Excel Export
        ExportService->>ExportService: generateExcel(data)
        ExportService-->>ReportController: Excel file
    end

    ReportController-->>Browser: Download file
    Browser-->>User: File downloaded
```

### **5. Logout Sequence**

```mermaid
sequenceDiagram
    participant User as 👤 User
    participant Browser as 🌐 Browser
    participant AuthController as 🔐 AuthController
    participant Session as 🗂️ Session
    participant Database as 🗄️ Database

    User->>Browser: Klik logout
    Browser->>AuthController: POST /logout

    AuthController->>Session: invalidate()
    Session->>Session: Clear session data
    Session-->>AuthController: Session cleared

    AuthController->>Database: UPDATE users SET last_activity = NOW()
    Database-->>AuthController: Activity updated

    AuthController-->>Browser: Redirect to login
    Browser-->>User: Login page
```

---

## 📝 **Interaction Patterns**

### **🔐 Authentication Pattern**
1. **User Request** → **Controller Processing** → **Model Validation**
2. **Database Query** → **Role Check** → **Dashboard Redirect**
3. **Error Handling** → **User Feedback**

### **💰 Transaction Pattern**
1. **Product Loading** → **Stock Validation** → **Cart Management**
2. **Payment Processing** → **External Service** → **Callback Handling**
3. **Inventory Update** → **Data Persistence** → **Receipt Generation**

### **📦 Inventory Pattern**
1. **Data Retrieval** → **Display Processing** → **User Interface**
2. **CRUD Operations** → **Validation** → **Database Operations**
3. **Notification Triggers** → **Alert System** → **User Notification**

### **📊 Reporting Pattern**
1. **Filter Processing** → **Data Collection** → **Report Generation**
2. **Export Service** → **Format Processing** → **File Delivery**

---

## 🎯 **Key Interactions Covered**

✅ **Multi-Role Authentication** (Admin & Karyawan)
✅ **Real-time Transaction Processing**
✅ **Payment Gateway Integration** (Midtrans)
✅ **Inventory Stock Management**
✅ **Automated Notifications**
✅ **Report Generation & Export**
✅ **Session Management**
✅ **Error Handling & Validation**

---

**📊 Interaction Efficiency:** Optimized
**🔄 Async Processing:** Enabled
**📱 Real-time Updates:** Implemented
**🔐 Security Validation:** Complete
