<?php

echo "🧪 SIMPLE PAYMENT GATEWAY TEST\n";
echo "==============================\n\n";

// Test 1: Basic PHP functionality
echo "1. Testing basic PHP...\n";
echo "   ✅ PHP is working\n";
echo "   📅 Date: " . date('Y-m-d H:i:s') . "\n\n";

// Test 2: Test URL accessibility
echo "2. Testing URL accessibility...\n";

$testUrls = [
    'Test endpoint' => 'http://localhost/ubi-bakar-cilembu/public/payment/test/26',
    'POS page' => 'http://localhost/ubi-bakar-cilembu/public/pos'
];

foreach ($testUrls as $name => $url) {
    echo "   🌐 Testing {$name}: {$url}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    if ($error) {
        echo "      ❌ Error: {$error}\n";
    } else {
        echo "      ✅ HTTP {$httpCode} ({$duration}ms)\n";
        if ($httpCode == 200) {
            echo "      📄 Response: " . substr($response, 0, 100) . "...\n";
        }
    }
    echo "\n";
}

// Test 3: Test POST request to payment gateway
echo "3. Testing POST request to payment gateway...\n";

$url = 'http://localhost/ubi-bakar-cilembu/public/payment/create/26';
echo "   🌐 URL: {$url}\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'X-Requested-With: XMLHttpRequest'
]);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));

$startTime = microtime(true);
$response = curl_exec($ch);
$endTime = microtime(true);

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

$duration = round(($endTime - $startTime) * 1000, 2);

if ($error) {
    echo "   ❌ Error: {$error}\n";
} else {
    echo "   ✅ HTTP {$httpCode} ({$duration}ms)\n";
    echo "   📄 Response: " . substr($response, 0, 200) . "...\n";
    
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "   📊 JSON Response:\n";
        echo "      Success: " . ($responseData['success'] ?? 'unknown') . "\n";
        echo "      Message: " . ($responseData['message'] ?? 'no message') . "\n";
    }
}

echo "\n";

// Test 4: Check if server is running
echo "4. Checking if local server is running...\n";

$serverCheck = @file_get_contents('http://localhost/');
if ($serverCheck !== false) {
    echo "   ✅ Local server is running\n";
} else {
    echo "   ❌ Local server is not accessible\n";
    echo "   💡 Make sure XAMPP/WAMP is running\n";
    echo "   💡 Check if Apache is started\n";
}

echo "\n🏁 Test completed!\n";

// Test 5: Recommendations
echo "\n💡 TROUBLESHOOTING STEPS:\n";
echo "========================\n";
echo "1. 🔧 Check XAMPP/WAMP Control Panel\n";
echo "   - Make sure Apache is running (green)\n";
echo "   - Make sure MySQL is running (green)\n\n";

echo "2. 🌐 Check browser console\n";
echo "   - Open F12 Developer Tools\n";
echo "   - Go to Console tab\n";
echo "   - Look for JavaScript errors\n\n";

echo "3. 🔍 Check Network tab\n";
echo "   - Open F12 Developer Tools\n";
echo "   - Go to Network tab\n";
echo "   - Try payment gateway again\n";
echo "   - Check if request is sent and what response is received\n\n";

echo "4. 📋 Check Laravel logs\n";
echo "   - Look at storage/logs/laravel.log\n";
echo "   - Check for any error messages\n\n";

echo "5. 🔄 Clear cache\n";
echo "   - Run: php artisan cache:clear\n";
echo "   - Run: php artisan config:clear\n";
echo "   - Run: php artisan route:clear\n\n";
