<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Struk #{{ $transaction->invoice_number }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 0;
            padding: 10px;
            width: 80mm;
            background: white;
        }
        .receipt {
            width: 100%;
            max-width: 80mm;
            margin: 0 auto;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .text-left {
            text-align: left;
        }
        .header {
            margin-bottom: 10px;
            text-align: center;
        }
        .header h2 {
            margin: 0;
            padding: 0;
            font-size: 16px;
            font-weight: bold;
        }
        .header p {
            margin: 0;
            padding: 0;
            font-size: 11px;
        }
        .divider {
            border-top: 1px dashed #000;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        table th, table td {
            padding: 3px 0;
            font-size: 11px;
        }
        .total {
            font-weight: bold;
        }
        .footer {
            margin-top: 10px;
            text-align: center;
            font-size: 10px;
        }
        .info-table td:first-child {
            width: 35%;
        }
        .info-table td:nth-child(2) {
            width: 5%;
        }
        .info-table td:last-child {
            width: 60%;
        }
        .item-table th {
            font-weight: bold;
            border-bottom: 1px solid #000;
        }
        .item-table th:first-child {
            width: 40%;
        }
        .item-table th:nth-child(2) {
            width: 15%;
        }
        .item-table th:nth-child(3) {
            width: 20%;
        }
        .item-table th:last-child {
            width: 25%;
        }
        
        @media print {
            body {
                width: 80mm;
                margin: 0;
                padding: 5px;
            }
            .no-print {
                display: none;
            }
            .receipt {
                page-break-inside: avoid;
            }
            @page {
                size: 80mm auto;
                margin: 0;
            }
        }
        
        .no-print {
            margin-top: 20px;
            text-align: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 3px;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 12px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="header">
            <h2>UBI BAKAR CILEMBU</h2>
            <p>Jl. Contoh No. 123, Kota, Indonesia</p>
            <p>Telp: 081234567890</p>
        </div>
        
        <div class="divider"></div>
        
        <table class="info-table">
            <tr>
                <td>No. Invoice</td>
                <td>:</td>
                <td>{{ $transaction->invoice_number }}</td>
            </tr>
            <tr>
                <td>Tanggal</td>
                <td>:</td>
                <td>{{ $transaction->created_at->format('d/m/Y H:i') }}</td>
            </tr>
            <tr>
                <td>Kasir</td>
                <td>:</td>
                <td>{{ $transaction->user->name ?? 'Admin' }}</td>
            </tr>
            @if($transaction->customer_name)
            <tr>
                <td>Customer</td>
                <td>:</td>
                <td>{{ $transaction->customer_name }}</td>
            </tr>
            @endif
        </table>
        
        <div class="divider"></div>
        
        <table class="item-table">
            <tr>
                <th class="text-left">Item</th>
                <th class="text-right">Qty</th>
                <th class="text-right">Harga</th>
                <th class="text-right">Subtotal</th>
            </tr>
            
            @foreach($transaction->items as $item)
            <tr>
                <td>{{ $item->product_name }}</td>
                <td class="text-right">{{ $item->quantity }}</td>
                <td class="text-right">{{ number_format($item->price, 0, ',', '.') }}</td>
                <td class="text-right">{{ number_format($item->subtotal, 0, ',', '.') }}</td>
            </tr>
            @endforeach
        </table>
        
        <div class="divider"></div>
        
        <table>
            <tr>
                <td><strong>Total</strong></td>
                <td class="text-right total"><strong>Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</strong></td>
            </tr>

            @if($transaction->payment_method === 'gateway')
                <tr>
                    <td>Metode Bayar</td>
                    <td class="text-right">Payment Gateway</td>
                </tr>
                <tr>
                    <td>Provider</td>
                    <td class="text-right">Midtrans</td>
                </tr>
                @php
                    $paymentStatus = $transaction->payment_status ?? 'pending';
                @endphp
                <tr>
                    <td>Status</td>
                    <td class="text-right">
                        @if($paymentStatus === 'paid')
                            SELESAI
                        @elseif($paymentStatus === 'pending')
                            MENUNGGU
                        @elseif($paymentStatus === 'failed')
                            GAGAL
                        @elseif($paymentStatus === 'cancelled')
                            BATAL
                        @elseif($paymentStatus === 'expired')
                            KEDALUWARSA
                        @else
                            {{ strtoupper($paymentStatus) }}
                        @endif
                    </td>
                </tr>
                @if($paymentStatus === 'paid' && $transaction->payment_gateway_paid_at)
                <tr>
                    <td>Dibayar</td>
                    <td class="text-right">{{ $transaction->payment_gateway_paid_at->format('d/m/Y H:i') }}</td>
                </tr>
                @endif
                @if($transaction->payment_gateway_transaction_id)
                <tr>
                    <td>Ref ID</td>
                    <td class="text-right">{{ substr($transaction->payment_gateway_transaction_id, -8) }}</td>
                </tr>
                @endif
            @else
                <tr>
                    <td>Metode Bayar</td>
                    <td class="text-right">
                        @switch($transaction->payment_method)
                            @case('cash')
                                Tunai
                                @break
                            @case('transfer')
                                Transfer Bank
                                @break
                            @case('debit')
                                Kartu Debit
                                @break
                            @case('credit')
                                Kartu Kredit
                                @break
                            @default
                                {{ ucfirst($transaction->payment_method) }}
                        @endswitch
                    </td>
                </tr>
                @if($transaction->amount_paid)
                <tr>
                    <td>Bayar</td>
                    <td class="text-right">Rp {{ number_format($transaction->amount_paid, 0, ',', '.') }}</td>
                </tr>
                @if($transaction->change_amount > 0)
                <tr>
                    <td>Kembali</td>
                    <td class="text-right">Rp {{ number_format($transaction->change_amount, 0, ',', '.') }}</td>
                </tr>
                @endif
                @endif
                <tr>
                    <td>Status</td>
                    <td class="text-right">LUNAS</td>
                </tr>
            @endif
        </table>
        
        <div class="divider"></div>
        
        <div class="footer">
            <p>Terima Kasih Atas Kunjungan Anda</p>
            <p>Selamat Menikmati!</p>
            @if($transaction->note)
            <div class="divider"></div>
            <p><strong>Catatan:</strong></p>
            <p>{{ $transaction->note }}</p>
            @endif
            <div class="divider"></div>
            <p style="font-size: 9px;">Dicetak: {{ now()->format('d/m/Y H:i:s') }}</p>
        </div>
    </div>
    
    <div class="no-print">
        <h4>📄 Export Struk sebagai PDF</h4>
        <p>Untuk menyimpan struk ini sebagai PDF:</p>
        <ol style="text-align: left; display: inline-block;">
            <li>Klik tombol "Print" di bawah</li>
            <li>Pilih "Save as PDF" atau "Microsoft Print to PDF"</li>
            <li>Pilih lokasi penyimpanan</li>
            <li>Klik "Save"</li>
        </ol>
        <br>
        <button onclick="window.print()" class="btn btn-primary">🖨️ Print / Save as PDF</button>
        <a href="{{ route('transactions.show', $transaction) }}" class="btn btn-secondary">⬅️ Kembali</a>
    </div>
    
    <script>
        // Auto-focus for better user experience
        window.onload = function() {
            document.body.focus();
        };
    </script>
</body>
</html>
