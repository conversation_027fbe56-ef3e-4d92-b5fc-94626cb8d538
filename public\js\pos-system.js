/**
 * POS System JavaScript
 * Enhanced functionality for Point of Sale system
 */

// Global variables
let cart = [];
let totalAmount = 0;
let isSubmitting = false;
let currentPaymentMethod = 'manual';

// DOM elements
let productsContainer, cartItems, emptyCart, processPaymentBtn, paymentBox;
let amountDueElement, amountPaidElement, changeElement, cancelPaymentBtn;
let completeTransactionBtn, transactionForm, searchInput;
let categoryButtons, paymentSections, manualPaymentOption, gatewayPaymentOption;

// Initialize POS System
function initializePOS() {
    console.log('Initializing POS System...');
    
    // Get DOM elements
    productsContainer = document.getElementById('products-container');
    cartItems = document.getElementById('cart-items');
    emptyCart = document.getElementById('empty-cart');
    processPaymentBtn = document.getElementById('process-payment-btn');
    paymentBox = document.getElementById('payment-box');
    amountDueElement = document.getElementById('amount-due');
    amountPaidElement = document.getElementById('amount-paid');
    changeElement = document.getElementById('change');
    cancelPaymentBtn = document.getElementById('cancel-payment-btn');
    completeTransactionBtn = document.getElementById('complete-transaction-btn');
    transactionForm = document.getElementById('transaction-form');
    searchInput = document.getElementById('search-product');
    categoryButtons = document.querySelectorAll('.category-btn');
    paymentSections = document.querySelectorAll('.payment-method-section');
    manualPaymentOption = document.getElementById('manual-payment-option');
    gatewayPaymentOption = document.getElementById('gateway-payment-option');

    // Initialize event listeners
    initializeEventListeners();
    
    // Initial render
    renderCart();
    
    console.log('POS System initialized successfully');
}

// Initialize all event listeners
function initializeEventListeners() {
    // Product search
    if (searchInput) {
        searchInput.addEventListener('input', handleProductSearch);
    }
    
    // Category filters
    categoryButtons.forEach(button => {
        button.addEventListener('click', handleCategoryFilter);
    });
    
    // Add to cart
    if (productsContainer) {
        productsContainer.addEventListener('click', handleAddToCart);
    }
    
    // Payment method options
    if (manualPaymentOption) {
        manualPaymentOption.addEventListener('click', () => handlePaymentMethodChange('manual'));
    }
    if (gatewayPaymentOption) {
        gatewayPaymentOption.addEventListener('click', () => handlePaymentMethodChange('gateway'));
    }
    
    // Process payment button
    if (processPaymentBtn) {
        processPaymentBtn.addEventListener('click', handleProcessPayment);
    }
    
    // Cancel payment button
    if (cancelPaymentBtn) {
        cancelPaymentBtn.addEventListener('click', handleCancelPayment);
    }
    
    // Amount paid input
    if (amountPaidElement) {
        amountPaidElement.addEventListener('input', handleAmountPaidChange);
    }
    
    // Numpad buttons
    document.querySelectorAll('.btn-number').forEach(button => {
        button.addEventListener('click', handleNumpadClick);
    });
    
    // Clear amount button
    const clearAmountBtn = document.getElementById('clear-amount');
    if (clearAmountBtn) {
        clearAmountBtn.addEventListener('click', handleClearAmount);
    }
    
    // Exact amount button
    const exactAmountBtn = document.getElementById('exact-amount');
    if (exactAmountBtn) {
        exactAmountBtn.addEventListener('click', handleExactAmount);
    }
    
    // Clear cart button
    const clearCartBtn = document.getElementById('clear-cart-btn');
    if (clearCartBtn) {
        clearCartBtn.addEventListener('click', handleClearCart);
    }
    
    // Refresh button
    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', handleRefresh);
    }
    
    // Payment specific buttons
    initializePaymentButtons();
}

// Initialize payment specific buttons
function initializePaymentButtons() {
    // Manual payment button
    const completeManualBtn = document.getElementById('complete-manual-payment');
    if (completeManualBtn) {
        completeManualBtn.addEventListener('click', handleManualPayment);
    }

    // Gateway payment button
    const completeGatewayBtn = document.getElementById('complete-gateway-payment');
    if (completeGatewayBtn) {
        completeGatewayBtn.addEventListener('click', handleGatewayPayment);
    }

    // Main complete transaction button
    if (completeTransactionBtn) {
        completeTransactionBtn.addEventListener('click', handleCompleteTransaction);
    }
}

// Event handlers
function handleProductSearch() {
    const searchTerm = this.value.toLowerCase();
    const products = document.querySelectorAll('.product-item');
    
    products.forEach(product => {
        const name = product.getAttribute('data-name').toLowerCase();
        product.style.display = name.includes(searchTerm) ? '' : 'none';
    });
}

function handleCategoryFilter() {
    // Remove active class from all buttons
    categoryButtons.forEach(btn => btn.classList.remove('active'));
    // Add active class to clicked button
    this.classList.add('active');
    
    const category = this.getAttribute('data-category');
    const products = document.querySelectorAll('.product-item');
    
    products.forEach(product => {
        if (category === 'all' || product.getAttribute('data-category') === category) {
            product.style.display = '';
        } else {
            product.style.display = 'none';
        }
    });
}

function handleAddToCart(e) {
    if (!e.target.classList.contains('add-to-cart')) return;
    
    const productCard = e.target.closest('.product-item');
    const productId = productCard.getAttribute('data-id');
    const productName = productCard.getAttribute('data-name');
    const productPrice = parseInt(productCard.getAttribute('data-price'));
    const maxStock = parseInt(productCard.getAttribute('data-stock'));
    const productType = productCard.getAttribute('data-type') || 'processed';

    // Validate product data
    if (!productId || !productName || isNaN(productPrice) || isNaN(maxStock)) {
        alert('Data produk tidak lengkap. Silakan refresh halaman.');
        return;
    }
    
    // Check if product already in cart
    const existingItem = cart.find(item => item.id === productId && item.type === productType);
    
    if (existingItem) {
        if (existingItem.quantity < maxStock) {
            existingItem.quantity += 1;
        } else {
            alert('Stok tidak mencukupi!');
            return;
        }
    } else {
        if (maxStock > 0) {
            cart.push({
                id: productId,
                name: productName,
                price: productPrice,
                quantity: 1,
                max_stock: maxStock,
                type: productType
            });
        } else {
            alert('Stok tidak tersedia!');
            return;
        }
    }
    
    renderCart();
}

function handlePaymentMethodChange(method) {
    // Update current payment method
    currentPaymentMethod = method;

    // Update visual selection
    if (manualPaymentOption && gatewayPaymentOption) {
        // Remove active state from both options
        manualPaymentOption.querySelector('.card').classList.remove('border-primary', 'border-success');
        gatewayPaymentOption.querySelector('.card').classList.remove('border-primary', 'border-success');

        // Add active state to selected option
        if (method === 'manual') {
            manualPaymentOption.querySelector('.card').classList.add('border-primary');
            manualPaymentOption.querySelector('.card').style.borderWidth = '3px';
            gatewayPaymentOption.querySelector('.card').style.borderWidth = '2px';
        } else {
            gatewayPaymentOption.querySelector('.card').classList.add('border-success');
            gatewayPaymentOption.querySelector('.card').style.borderWidth = '3px';
            manualPaymentOption.querySelector('.card').style.borderWidth = '2px';
        }
    }

    // Show appropriate section
    showPaymentSection(currentPaymentMethod);
    updateCompleteButtonState();

    console.log('Payment method changed to:', currentPaymentMethod);
}

function handleProcessPayment() {
    if (cart.length === 0) {
        alert('Keranjang belanja kosong!');
        return;
    }

    paymentBox.style.display = 'block';
    amountDueElement.value = formatNumber(totalAmount);

    // Reset payment form
    currentPaymentMethod = 'manual';
    amountPaidElement.value = '';
    changeElement.value = '';
    document.getElementById('transaction-note').value = '';
    document.getElementById('customer-name').value = '';
    document.getElementById('customer-phone').value = '';

    // Reset to manual payment by default
    handlePaymentMethodChange('manual');
    updateCompleteButtonState();
}

function handleCancelPayment() {
    paymentBox.style.display = 'none';
}

function handleAmountPaidChange() {
    const amountPaid = parseNumber(this.value);
    const change = amountPaid - totalAmount;
    
    changeElement.value = change >= 0 ? formatNumber(change) : '0';
    updateCompleteButtonState();
}

function handleNumpadClick() {
    const number = this.getAttribute('data-number');
    let currentValue = amountPaidElement.value.replace(/\./g, '');

    if (number === 'C') {
        currentValue = '';
    } else {
        currentValue += number;
    }

    amountPaidElement.value = formatNumber(parseInt(currentValue) || 0);

    // Trigger input event to calculate change
    const inputEvent = new Event('input', { bubbles: true });
    amountPaidElement.dispatchEvent(inputEvent);
}

function handleClearAmount() {
    amountPaidElement.value = '';
    changeElement.value = '';
    updateCompleteButtonState();
}

function handleExactAmount() {
    amountPaidElement.value = formatNumber(totalAmount);
    changeElement.value = '0';
    updateCompleteButtonState();
}

function handleClearCart() {
    if (confirm('Apakah Anda yakin ingin mengosongkan keranjang?')) {
        cart = [];
        renderCart();
        console.log('Cart cleared');
    }
}

function handleRefresh() {
    if (confirm('Apakah Anda yakin ingin me-refresh halaman? Data keranjang akan hilang.')) {
        location.reload();
    }
}

// Payment handlers
function handleManualPayment(e) {
    e.preventDefault();
    console.log('Manual Payment button clicked');

    if (cart.length === 0) {
        alert('Keranjang belanja kosong!');
        return;
    }

    const amountPaid = parseNumber(amountPaidElement.value);
    if (amountPaid < totalAmount) {
        alert('Jumlah pembayaran tidak mencukupi!');
        amountPaidElement.focus();
        return;
    }

    // Get selected payment method
    const paymentMethod = document.getElementById('manual-payment-method').value;
    submitTransaction(paymentMethod, amountPaid);
}

function handleGatewayPayment(e) {
    e.preventDefault();
    console.log('Gateway Payment button clicked');

    if (cart.length === 0) {
        alert('Keranjang belanja kosong!');
        return;
    }

    const customerName = document.getElementById('customer-name')?.value?.trim();
    if (!customerName) {
        alert('Nama pelanggan harus diisi untuk pembayaran online!');
        document.getElementById('customer-name')?.focus();
        return;
    }

    submitTransaction('gateway', 0);
}



function handleCompleteTransaction(e) {
    if (isSubmitting) return;

    // Prevent double submission
    if (e.target.disabled || isSubmitting) {
        console.log('Transaction already being processed, ignoring click');
        return;
    }

    // Check if cart is empty
    if (cart.length === 0) {
        alert('Keranjang belanja kosong!');
        return;
    }

    // Set submission flag
    isSubmitting = true;

    // Show loading indicator
    e.target.disabled = true;
    e.target.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Memproses...';

    switch (currentPaymentMethod) {
        case 'manual':
            handleManualPayment(e);
            break;
        case 'gateway':
            handleGatewayPayment(e);
            break;
    }
}

// Submit transaction
function submitTransaction(paymentMethod, amountPaid) {
    if (isSubmitting) return;

    isSubmitting = true;

    // Show loading on appropriate button
    const buttons = {
        'cash': document.getElementById('complete-manual-payment'),
        'transfer': document.getElementById('complete-manual-payment'),
        'debit': document.getElementById('complete-manual-payment'),
        'credit': document.getElementById('complete-manual-payment'),
        'gateway': document.getElementById('complete-gateway-payment')
    };

    const button = buttons[paymentMethod];
    if (button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Memproses...';
    }

    // Set form data
    document.getElementById('items-input').value = JSON.stringify(cart);
    document.getElementById('payment-method-input').value = paymentMethod;
    document.getElementById('amount-paid-input').value = amountPaid;
    document.getElementById('customer-name-input').value = document.getElementById('customer-name')?.value || '';
    document.getElementById('customer-phone-input').value = document.getElementById('customer-phone')?.value || '';
    document.getElementById('notes-input').value = document.getElementById('transaction-note')?.value || '';
    document.getElementById('use-payment-gateway-input').value = paymentMethod === 'gateway' ? '1' : '0';

    console.log('Submitting transaction:', {
        payment_method: paymentMethod,
        amount_paid: amountPaid,
        items_count: cart.length
    });

    // Submit form
    transactionForm.submit();
}

// Utility functions
function renderCart() {
    if (cart.length === 0) {
        emptyCart.style.display = 'block';
        cartItems.innerHTML = '';
        processPaymentBtn.disabled = true;
    } else {
        emptyCart.style.display = 'none';
        cartItems.innerHTML = '';

        cart.forEach(item => {
            const cartItem = document.createElement('div');
            cartItem.className = 'cart-item';
            cartItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6>${item.name}</h6>
                        <p class="text-muted">Rp ${formatNumber(item.price)} x ${item.quantity}</p>
                    </div>
                    <div class="d-flex align-items-center">
                        <button class="btn btn-sm btn-outline-danger me-2 decrease-qty" data-id="${item.id}" data-type="${item.type}">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="form-control quantity-input" value="${item.quantity}" min="1" max="${item.max_stock}" data-id="${item.id}" data-type="${item.type}">
                        <button class="btn btn-sm btn-outline-primary ms-2 increase-qty" data-id="${item.id}" data-type="${item.type}">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger ms-3 remove-item" data-id="${item.id}" data-type="${item.type}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            cartItems.appendChild(cartItem);
        });

        addQuantityEventListeners();
        processPaymentBtn.disabled = false;
    }

    updateCartSummary();
}

function addQuantityEventListeners() {
    // Decrease quantity buttons
    document.querySelectorAll('.decrease-qty').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');
            const productType = this.getAttribute('data-type');
            const item = cart.find(item => item.id === productId && item.type === productType);

            if (item && item.quantity > 1) {
                item.quantity -= 1;
                renderCart();
            }
        });
    });

    // Increase quantity buttons
    document.querySelectorAll('.increase-qty').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');
            const productType = this.getAttribute('data-type');
            const item = cart.find(item => item.id === productId && item.type === productType);

            if (item && item.quantity < item.max_stock) {
                item.quantity += 1;
                renderCart();
            } else {
                alert('Stok tidak mencukupi!');
            }
        });
    });

    // Quantity input fields
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            const productId = this.getAttribute('data-id');
            const productType = this.getAttribute('data-type');
            const item = cart.find(item => item.id === productId && item.type === productType);
            const newQuantity = parseInt(this.value);

            if (item && newQuantity >= 1 && newQuantity <= item.max_stock) {
                item.quantity = newQuantity;
                renderCart();
            } else {
                alert('Jumlah tidak valid atau melebihi stok!');
                renderCart();
            }
        });
    });

    // Remove item buttons
    document.querySelectorAll('.remove-item').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.getAttribute('data-id');
            const productType = this.getAttribute('data-type');
            cart = cart.filter(item => !(item.id === productId && item.type === productType));
            renderCart();
        });
    });
}

function updateCartSummary() {
    const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
    totalAmount = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

    // Update cart badge
    const cartBadge = document.getElementById('cart-badge');
    if (cartBadge) {
        cartBadge.textContent = totalItems + ' item' + (totalItems !== 1 ? 's' : '');
    }

    // Update total displays
    const subtotalElement = document.getElementById('subtotal');
    const taxElement = document.getElementById('tax');
    const totalElement = document.getElementById('total');

    if (subtotalElement) subtotalElement.textContent = `Rp ${formatNumber(totalAmount)}`;
    if (taxElement) taxElement.textContent = 'Rp 0';
    if (totalElement) totalElement.textContent = `Rp ${formatNumber(totalAmount)}`;

    if (amountDueElement) {
        amountDueElement.value = formatNumber(totalAmount);
    }
}

function showPaymentSection(method) {
    paymentSections.forEach(section => {
        section.style.display = 'none';
    });

    const activeSection = document.getElementById(`${method}-section`);
    if (activeSection) {
        activeSection.style.display = 'block';
    }

    // Reset status displays
    document.querySelectorAll('.payment-status').forEach(status => {
        status.style.display = 'none';
        status.textContent = '';
        status.className = 'payment-status';
    });

    updateCompleteButtonState();
}



function updateCompleteButtonState() {
    let isValid = false;

    switch (currentPaymentMethod) {
        case 'manual':
            const amountPaid = parseNumber(amountPaidElement.value);
            isValid = amountPaid >= totalAmount;
            const completeManualBtn = document.getElementById('complete-manual-payment');
            if (completeManualBtn) {
                completeManualBtn.disabled = !isValid;
            }
            break;

        case 'gateway':
            isValid = true;
            const completeGatewayBtn = document.getElementById('complete-gateway-payment');
            if (completeGatewayBtn) {
                completeGatewayBtn.disabled = false;
            }
            break;
    }

    if (completeTransactionBtn) {
        completeTransactionBtn.disabled = !isValid;
    }
}

function formatNumber(number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
}

function parseNumber(string) {
    return parseInt(string.replace(/\./g, '')) || 0;
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePOS();
});
