-- Database Export: sim_ubi_cilembu
-- Generated: 2024-05-17

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Table structure for table `audit_logs`
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE `audit_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `action` varchar(255) NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  `old_values` text DEFAULT NULL,
  `new_values` text DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `audit_logs_user_id_foreign` (`user_id`),
  K<PERSON>Y `audit_logs_model_type_model_id_index` (`model_type`,`model_id`),
  <PERSON><PERSON>Y `audit_logs_action_index` (`action`),
  <PERSON><PERSON><PERSON> `audit_logs_created_at_index` (`created_at`),
  CONSTRAINT `audit_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `failed_jobs`
DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `migrations`
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `migrations`
INSERT INTO `migrations` VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_reset_tokens_table', 1),
(3, '2019_08_19_000000_create_failed_jobs_table', 1),
(4, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(5, '2023_05_01_000001_create_raw_inventory_table', 1),
(6, '2023_05_01_000002_create_processed_inventory_table', 1),
(7, '2023_05_01_000003_create_other_products_table', 1),
(8, '2023_05_01_000004_create_transactions_table', 1),
(9, '2023_05_01_000005_create_transaction_items_table', 1),
(10, '2023_05_01_000006_create_production_logs_table', 1),
(11, '2023_05_01_000007_add_foreign_keys', 1),
(12, '2023_05_01_000008_add_product_type_to_processed_inventory', 1),
(13, '2023_05_01_000009_add_min_stock_threshold_to_inventory_tables', 1),
(14, '2023_05_01_000010_add_is_active_to_inventory_tables', 1),
(15, '2023_05_01_000011_add_cost_per_item_to_processed_inventory', 1),
(16, '2025_05_17_012205_add_indexes_to_improve_performance', 2),
(17, '2025_05_17_012215_add_constraints_for_data_integrity', 2),
(18, '2025_05_17_012225_add_soft_deletes_to_important_tables', 2),
(19, '2025_05_17_012234_create_audit_logs_table', 2),
(20, '2025_05_17_012929_add_last_activity_to_users_table', 3),
(21, '2025_05_17_013228_add_soft_delete_to_users_table', 4);

-- Table structure for table `other_products`
DROP TABLE IF EXISTS `other_products`;
CREATE TABLE `other_products` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `sku` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `purchase_price` decimal(10,2) NOT NULL,
  `selling_price` decimal(10,2) NOT NULL,
  `current_stock` int(11) NOT NULL DEFAULT 0,
  `min_stock_threshold` int(11) DEFAULT 5,
  `category` varchar(255) DEFAULT NULL,
  `unit` varchar(255) DEFAULT NULL,
  `supplier` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `other_products_sku_unique` (`sku`),
  KEY `idx_other_prod_sku` (`sku`),
  KEY `idx_other_prod_category` (`category`),
  KEY `idx_other_prod_stock` (`current_stock`),
  KEY `idx_other_prod_active_stock` (`is_active`,`current_stock`),
  CONSTRAINT `chk_other_prod_purchase_price_positive` CHECK (`purchase_price` >= 0),
  CONSTRAINT `chk_other_prod_selling_price_positive` CHECK (`selling_price` >= 0),
  CONSTRAINT `chk_other_prod_current_stock_positive` CHECK (`current_stock` >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `password_reset_tokens`
DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `personal_access_tokens`
DROP TABLE IF EXISTS `personal_access_tokens`;
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `processed_inventory`
DROP TABLE IF EXISTS `processed_inventory`;
CREATE TABLE `processed_inventory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `batch_number` varchar(255) NOT NULL,
  `raw_inventory_id` bigint(20) unsigned NOT NULL,
  `quantity_processed_kg` decimal(10,2) NOT NULL,
  `quantity_produced` int(11) NOT NULL,
  `cost_per_unit` decimal(10,2) NOT NULL,
  `selling_price` decimal(10,2) NOT NULL,
  `current_stock` int(11) NOT NULL DEFAULT 0,
  `production_date` date NOT NULL,
  `expiry_date` date NOT NULL,
  `product_type` enum('Original','Premium','Special') NOT NULL DEFAULT 'Original',
  `min_stock_threshold` int(11) DEFAULT 10,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `cost_per_item` decimal(10,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `processed_inventory_batch_number_unique` (`batch_number`),
  KEY `processed_inventory_raw_inventory_id_foreign` (`raw_inventory_id`),
  KEY `idx_proc_batch_number` (`batch_number`),
  KEY `idx_proc_production_date` (`production_date`),
  KEY `idx_proc_expiry_date` (`expiry_date`),
  KEY `idx_proc_current_stock` (`current_stock`),
  KEY `idx_proc_active_stock` (`is_active`,`current_stock`),
  KEY `idx_proc_product_type` (`product_type`),
  CONSTRAINT `processed_inventory_raw_inventory_id_foreign` FOREIGN KEY (`raw_inventory_id`) REFERENCES `raw_inventory` (`id`),
  CONSTRAINT `chk_proc_quantity_processed_positive` CHECK (`quantity_processed_kg` >= 0),
  CONSTRAINT `chk_proc_quantity_produced_positive` CHECK (`quantity_produced` >= 0),
  CONSTRAINT `chk_proc_cost_per_unit_positive` CHECK (`cost_per_unit` >= 0),
  CONSTRAINT `chk_proc_selling_price_positive` CHECK (`selling_price` >= 0),
  CONSTRAINT `chk_proc_current_stock_positive` CHECK (`current_stock` >= 0),
  CONSTRAINT `chk_proc_dates_valid` CHECK (`expiry_date` > `production_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `production_logs`
DROP TABLE IF EXISTS `production_logs`;
CREATE TABLE `production_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `raw_inventory_id` bigint(20) unsigned NOT NULL,
  `processed_inventory_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `raw_amount_used` decimal(10,2) NOT NULL,
  `produced_amount` int(11) NOT NULL,
  `raw_cost` decimal(10,2) NOT NULL,
  `additional_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_cost` decimal(10,2) NOT NULL,
  `cost_per_item` decimal(10,2) NOT NULL,
  `raw_name` varchar(255) DEFAULT NULL,
  `processed_name` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `production_logs_raw_inventory_id_foreign` (`raw_inventory_id`),
  KEY `production_logs_processed_inventory_id_foreign` (`processed_inventory_id`),
  KEY `production_logs_user_id_foreign` (`user_id`),
  KEY `idx_prod_logs_created` (`created_at`),
  KEY `idx_prod_logs_raw_proc` (`raw_inventory_id`,`processed_inventory_id`),
  CONSTRAINT `production_logs_processed_inventory_id_foreign` FOREIGN KEY (`processed_inventory_id`) REFERENCES `processed_inventory` (`id`),
  CONSTRAINT `production_logs_raw_inventory_id_foreign` FOREIGN KEY (`raw_inventory_id`) REFERENCES `raw_inventory` (`id`),
  CONSTRAINT `production_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `chk_prod_logs_raw_amount_positive` CHECK (`raw_amount_used` >= 0),
  CONSTRAINT `chk_prod_logs_produced_amount_positive` CHECK (`produced_amount` >= 0),
  CONSTRAINT `chk_prod_logs_raw_cost_positive` CHECK (`raw_cost` >= 0),
  CONSTRAINT `chk_prod_logs_additional_cost_positive` CHECK (`additional_cost` >= 0),
  CONSTRAINT `chk_prod_logs_total_cost_positive` CHECK (`total_cost` >= 0),
  CONSTRAINT `chk_prod_logs_cost_per_item_positive` CHECK (`cost_per_item` >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `raw_inventory`
DROP TABLE IF EXISTS `raw_inventory`;
CREATE TABLE `raw_inventory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `batch_number` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `supplier_name` varchar(255) NOT NULL,
  `quantity_kg` decimal(10,2) NOT NULL,
  `cost_per_kg` decimal(10,2) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `current_stock` decimal(10,2) NOT NULL DEFAULT 0.00,
  `purchase_date` date NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `quality` enum('A','B','C') NOT NULL DEFAULT 'A',
  `min_stock_threshold` decimal(10,2) DEFAULT 50.00,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `raw_inventory_batch_number_unique` (`batch_number`),
  KEY `idx_raw_batch_number` (`batch_number`),
  KEY `idx_raw_purchase_date` (`purchase_date`),
  KEY `idx_raw_expiry_date` (`expiry_date`),
  KEY `idx_raw_current_stock` (`current_stock`),
  KEY `idx_raw_active_stock` (`is_active`,`current_stock`),
  CONSTRAINT `chk_raw_quantity_positive` CHECK (`quantity_kg` >= 0),
  CONSTRAINT `chk_raw_cost_per_kg_positive` CHECK (`cost_per_kg` >= 0),
  CONSTRAINT `chk_raw_total_cost_positive` CHECK (`total_cost` >= 0),
  CONSTRAINT `chk_raw_current_stock_positive` CHECK (`current_stock` >= 0),
  CONSTRAINT `chk_raw_dates_valid` CHECK (`expiry_date` IS NULL OR `expiry_date` > `purchase_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `transaction_items`
DROP TABLE IF EXISTS `transaction_items`;
CREATE TABLE `transaction_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `transaction_id` bigint(20) unsigned NOT NULL,
  `product_id` bigint(20) unsigned DEFAULT NULL,
  `processed_inventory_id` bigint(20) unsigned DEFAULT NULL,
  `product_name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `discount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `subtotal` decimal(10,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_items_transaction_id_foreign` (`transaction_id`),
  KEY `transaction_items_product_id_foreign` (`product_id`),
  KEY `transaction_items_processed_inventory_id_foreign` (`processed_inventory_id`),
  KEY `idx_trans_items_trans_prod` (`transaction_id`,`product_id`),
  KEY `idx_trans_items_trans_proc` (`transaction_id`,`processed_inventory_id`),
  CONSTRAINT `transaction_items_processed_inventory_id_foreign` FOREIGN KEY (`processed_inventory_id`) REFERENCES `processed_inventory` (`id`),
  CONSTRAINT `transaction_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `other_products` (`id`),
  CONSTRAINT `transaction_items_transaction_id_foreign` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `chk_trans_items_price_positive` CHECK (`price` >= 0),
  CONSTRAINT `chk_trans_items_quantity_positive` CHECK (`quantity` >= 0),
  CONSTRAINT `chk_trans_items_discount_positive` CHECK (`discount` >= 0),
  CONSTRAINT `chk_trans_items_subtotal_positive` CHECK (`subtotal` >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `transactions`
DROP TABLE IF EXISTS `transactions`;
CREATE TABLE `transactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `customer_name` varchar(255) DEFAULT NULL,
  `customer_phone` varchar(255) DEFAULT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `tax` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `change_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('cash','transfer','qris','debit','credit') NOT NULL DEFAULT 'cash',
  `status` enum('completed','cancelled','refunded') NOT NULL DEFAULT 'completed',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transactions_invoice_number_unique` (`invoice_number`),
  KEY `transactions_user_id_foreign` (`user_id`),
  KEY `idx_trans_invoice` (`invoice_number`),
  KEY `idx_trans_created_at` (`created_at`),
  KEY `idx_trans_status` (`status`),
  KEY `idx_trans_payment` (`payment_method`),
  KEY `idx_trans_user_date` (`user_id`,`created_at`),
  CONSTRAINT `transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `chk_trans_subtotal_positive` CHECK (`subtotal` >= 0),
  CONSTRAINT `chk_trans_tax_positive` CHECK (`tax` >= 0),
  CONSTRAINT `chk_trans_discount_positive` CHECK (`discount` >= 0),
  CONSTRAINT `chk_trans_total_amount_positive` CHECK (`total_amount` >= 0),
  CONSTRAINT `chk_trans_amount_paid_positive` CHECK (`amount_paid` >= 0),
  CONSTRAINT `chk_trans_change_amount_positive` CHECK (`change_amount` >= 0)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table structure for table `users`
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','employee') NOT NULL DEFAULT 'employee',
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_activity` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Data for table `users`
INSERT INTO `users` VALUES
(1, 'Admin UBI', '<EMAIL>', NULL, '$2y$12$Ht5Ql.Ql9Ql9Ql9Ql9Ql9.Ql9Ql9Ql9Ql9Ql9Ql9Ql9Ql9Ql9Ql9', 'admin', NULL, '2023-05-13 14:01:00', '2023-05-13 14:01:00', NULL, NULL),
(2, 'Karyawan Toko', '<EMAIL>', NULL, '$2y$12$Ql9Ql9Ql9Ql9Ql9Ql9Ql.Ql9Ql9Ql9Ql9Ql9Ql9Ql9Ql9Ql9Ql9', 'employee', NULL, '2023-05-13 14:01:00', '2023-05-13 14:01:00', NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;
