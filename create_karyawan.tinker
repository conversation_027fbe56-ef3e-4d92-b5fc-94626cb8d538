use App\Models\User;
use Illuminate\Support\Facades\Hash;

// Hapus user karyawan lama jika email sudah ada
$existingUser = User::where('email', '<EMAIL>')->first();
if ($existingUser) {
    $existingUser->delete();
    echo "Deleted existing user <NAME_EMAIL>\n";
}

// Buat user karyawan baru
$user = new User();
$user->name = 'Karya<PERSON>ko';
$user->email = '<EMAIL>';
$user->password = Hash::make('karyawan123');
$user->role = 'karyawan';
$user->save();

echo "New karyawan user created successfully!\n";
echo "Email: <EMAIL>\n";
echo "Password: karyawan123\n";
echo "Role: karyawan\n"; 