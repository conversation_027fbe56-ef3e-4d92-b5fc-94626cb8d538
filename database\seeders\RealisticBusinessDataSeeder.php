<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\RawInventory;
use App\Models\ProcessedInventory;
use App\Models\OtherProduct;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\Expense;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class RealisticBusinessDataSeeder extends Seeder
{
    private $users = [];
    private $rawMaterials = [];
    private $processedProducts = [];
    private $otherProducts = [];
    
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        // Clear existing data
        $this->clearExistingData();
        
        // Create users
        $this->createUsers();
        
        // Create inventory
        $this->createRawInventory();
        $this->createProcessedInventory();
        $this->createOtherProducts();
        
        // Create 3 months of realistic business data
        $this->createThreeMonthsData();
        
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        $this->command->info('✅ Realistic business data for 3 months has been created successfully!');
    }
    
    private function clearExistingData()
    {
        TransactionItem::truncate();
        Transaction::truncate();
        Expense::truncate();
        ProcessedInventory::truncate();
        OtherProduct::truncate();
        RawInventory::truncate();
        
        // Keep admin user, delete others
        User::where('email', '!=', '<EMAIL>')->delete();
        
        $this->command->info('🗑️ Existing data cleared');
    }
    
    private function createUsers()
    {
        // Ensure admin exists
        $admin = User::firstOrCreate([
            'email' => '<EMAIL>'
        ], [
            'name' => 'Administrator',
            'role' => 'admin',
            'password' => bcrypt('password'),
            'email_verified_at' => now()
        ]);
        
        // Create cashiers
        $cashiers = [
            ['name' => 'Siti Nurhaliza', 'email' => '<EMAIL>'],
            ['name' => 'Budi Santoso', 'email' => '<EMAIL>'],
            ['name' => 'Rina Kartika', 'email' => '<EMAIL>'],
        ];
        
        foreach ($cashiers as $cashier) {
            $this->users[] = User::create([
                'name' => $cashier['name'],
                'email' => $cashier['email'],
                'role' => 'karyawan',
                'password' => bcrypt('password'),
                'email_verified_at' => now()
            ]);
        }
        
        $this->users[] = $admin;
        
        $this->command->info('👥 Users created: ' . count($this->users));
    }
    
    private function createRawInventory()
    {
        $rawMaterials = [
            [
                'name' => 'Ubi Cilembu Grade A',
                'category' => 'Bahan Baku Utama',
                'unit' => 'kg',
                'current_stock' => 500,
                'min_stock_threshold' => 50,
                'cost_per_kg' => 8000,
                'purchase_price' => 8000,
                'supplier_name' => 'Petani Cilembu Sumedang',
                'expiry_date' => now()->addMonths(2),
                'is_active' => true
            ],
            [
                'name' => 'Ubi Cilembu Grade B',
                'category' => 'Bahan Baku Utama',
                'unit' => 'kg',
                'current_stock' => 300,
                'min_stock_threshold' => 30,
                'cost_per_kg' => 6000,
                'purchase_price' => 6000,
                'supplier_name' => 'Petani Cilembu Sumedang',
                'expiry_date' => now()->addMonths(2),
                'is_active' => true
            ],
            [
                'name' => 'Arang Kayu',
                'category' => 'Bahan Bakar',
                'unit' => 'karung',
                'current_stock' => 50,
                'min_stock_threshold' => 10,
                'cost_per_kg' => 15000,
                'purchase_price' => 15000,
                'supplier_name' => 'Supplier Arang Bandung',
                'expiry_date' => null,
                'is_active' => true
            ]
        ];
        
        foreach ($rawMaterials as $material) {
            $this->rawMaterials[] = RawInventory::create($material);
        }
        
        $this->command->info('🥔 Raw materials created: ' . count($this->rawMaterials));
    }
    
    private function createProcessedInventory()
    {
        $processedProducts = [
            [
                'name' => 'Ubi Bakar Cilembu Original',
                'product_type' => 'Ubi Bakar',
                'batch_number' => 'UBC-' . date('Ymd') . '-001',
                'current_stock' => 200,
                'min_stock_threshold' => 20,
                'cost_per_item' => 3000,
                'selling_price' => 8000,
                'raw_material_id' => 1,
                'expiry_date' => now()->addDays(3),
                'is_active' => true
            ],
            [
                'name' => 'Ubi Bakar Cilembu Keju',
                'product_type' => 'Ubi Bakar',
                'batch_number' => 'UBC-' . date('Ymd') . '-002',
                'current_stock' => 150,
                'min_stock_threshold' => 15,
                'cost_per_item' => 4000,
                'selling_price' => 12000,
                'raw_material_id' => 1,
                'expiry_date' => now()->addDays(3),
                'is_active' => true
            ],
            [
                'name' => 'Ubi Bakar Cilembu Coklat',
                'product_type' => 'Ubi Bakar',
                'batch_number' => 'UBC-' . date('Ymd') . '-003',
                'current_stock' => 120,
                'min_stock_threshold' => 12,
                'cost_per_item' => 4500,
                'selling_price' => 13000,
                'raw_material_id' => 1,
                'expiry_date' => now()->addDays(3),
                'is_active' => true
            ]
        ];
        
        foreach ($processedProducts as $product) {
            $this->processedProducts[] = ProcessedInventory::create($product);
        }
        
        $this->command->info('🍠 Processed products created: ' . count($this->processedProducts));
    }
    
    private function createOtherProducts()
    {
        $otherProducts = [
            [
                'name' => 'Es Teh Manis',
                'category' => 'Minuman',
                'unit' => 'gelas',
                'current_stock' => 100,
                'min_stock_threshold' => 20,
                'selling_price' => 5000,
                'supplier_name' => 'Supplier Lokal',
                'is_active' => true
            ],
            [
                'name' => 'Es Jeruk',
                'category' => 'Minuman',
                'unit' => 'gelas',
                'current_stock' => 80,
                'min_stock_threshold' => 15,
                'selling_price' => 7000,
                'supplier_name' => 'Supplier Lokal',
                'is_active' => true
            ],
            [
                'name' => 'Air Mineral',
                'category' => 'Minuman',
                'unit' => 'botol',
                'current_stock' => 200,
                'min_stock_threshold' => 50,
                'selling_price' => 3000,
                'supplier_name' => 'Aqua',
                'is_active' => true
            ]
        ];
        
        foreach ($otherProducts as $product) {
            $this->otherProducts[] = OtherProduct::create($product);
        }
        
        $this->command->info('🥤 Other products created: ' . count($this->otherProducts));
    }
    
    private function createThreeMonthsData()
    {
        $startDate = now()->subMonths(3)->startOfMonth();
        $endDate = now();
        
        $currentDate = $startDate->copy();
        $transactionNumber = 1;
        
        while ($currentDate <= $endDate) {
            $this->createDayData($currentDate, $transactionNumber);
            $currentDate->addDay();
        }
        
        $this->command->info('📊 Three months of business data created');
    }
    
    private function createDayData($date, &$transactionNumber)
    {
        // Determine daily patterns
        $dayOfWeek = $date->dayOfWeek;
        $isWeekend = in_array($dayOfWeek, [0, 6]); // Sunday = 0, Saturday = 6
        $isHoliday = $this->isHoliday($date);
        
        // Base transaction count
        $baseTransactions = $isWeekend || $isHoliday ? rand(25, 45) : rand(15, 30);
        
        // Seasonal adjustments
        $month = $date->month;
        if (in_array($month, [6, 7, 12])) { // Holiday seasons
            $baseTransactions = (int)($baseTransactions * 1.3);
        }
        
        // Create transactions for the day
        for ($i = 0; $i < $baseTransactions; $i++) {
            $this->createTransaction($date, $transactionNumber);
            $transactionNumber++;
        }
        
        // Create daily expenses
        $this->createDailyExpenses($date);
    }

    private function createTransaction($date, $transactionNumber)
    {
        // Random time during business hours (8 AM - 9 PM)
        $hour = rand(8, 21);
        $minute = rand(0, 59);
        $transactionTime = $date->copy()->setTime($hour, $minute);

        // Random cashier
        $cashier = $this->users[array_rand($this->users)];

        // Customer names (realistic Indonesian names)
        $customerNames = [
            'Ibu Sari', 'Pak Budi', 'Mbak Rina', 'Mas Andi', 'Ibu Dewi',
            'Pak Joko', 'Mbak Sinta', 'Mas Rudi', 'Ibu Lestari', 'Pak Agus',
            'Keluarga Wijaya', 'Rombongan Sekolah', 'Tim Kantor', 'Wisatawan Jakarta',
            'Keluarga Besar', 'Grup Arisan', 'Mahasiswa ITB', 'Keluarga Sutrisno'
        ];

        $transaction = Transaction::create([
            'transaction_number' => 'TRX-' . $date->format('Ymd') . '-' . str_pad($transactionNumber, 4, '0', STR_PAD_LEFT),
            'invoice_number' => 'INV-' . $date->format('Ymd') . '-' . str_pad($transactionNumber, 4, '0', STR_PAD_LEFT),
            'user_id' => $cashier->id,
            'customer_name' => $customerNames[array_rand($customerNames)],
            'total_amount' => 0, // Will be calculated
            'payment_method' => $this->getRandomPaymentMethod($date),
            'status' => 'completed',
            'created_at' => $transactionTime,
            'updated_at' => $transactionTime
        ]);

        // Create transaction items
        $this->createTransactionItems($transaction, $transactionTime);
    }

    private function createTransactionItems($transaction, $transactionTime)
    {
        $totalAmount = 0;
        $itemCount = rand(1, 6); // 1-6 items per transaction

        for ($i = 0; $i < $itemCount; $i++) {
            // 70% chance for ubi bakar, 30% for other products
            if (rand(1, 100) <= 70) {
                // Ubi bakar products
                $product = $this->processedProducts[array_rand($this->processedProducts)];
                $quantity = rand(1, 4);
                $price = $product->selling_price;

                // Peak hour pricing (slight increase during lunch/dinner)
                $hour = $transactionTime->hour;
                if (in_array($hour, [12, 13, 18, 19, 20])) {
                    $price = (int)($price * 1.05); // 5% increase
                }

                TransactionItem::create([
                    'transaction_id' => $transaction->id,
                    'processed_inventory_id' => $product->id,
                    'product_name' => $product->name,
                    'quantity' => $quantity,
                    'price' => $price,
                    'subtotal' => $price * $quantity
                ]);

                $totalAmount += $price * $quantity;
            } else {
                // Other products (drinks)
                $product = $this->otherProducts[array_rand($this->otherProducts)];
                $quantity = rand(1, 3);
                $price = $product->selling_price;

                TransactionItem::create([
                    'transaction_id' => $transaction->id,
                    'other_product_id' => $product->id,
                    'product_name' => $product->name,
                    'quantity' => $quantity,
                    'price' => $price,
                    'subtotal' => $price * $quantity
                ]);

                $totalAmount += $price * $quantity;
            }
        }

        // Update transaction total
        $transaction->update(['total_amount' => $totalAmount]);
    }

    private function getRandomPaymentMethod($date)
    {
        // Payment method distribution changes over time
        $year = $date->year;
        $month = $date->month;

        // More digital payments in recent months
        if ($year >= 2025) {
            $methods = ['cash', 'qris', 'qris', 'transfer', 'cash'];
        } else {
            $methods = ['cash', 'cash', 'cash', 'qris', 'transfer'];
        }

        return $methods[array_rand($methods)];
    }

    private function createDailyExpenses($date)
    {
        // Skip expenses on some days
        if (rand(1, 100) > 80) return;

        $expenseTypes = [
            ['category' => 'Bahan Baku', 'amount_range' => [100000, 500000], 'probability' => 30],
            ['category' => 'Operasional', 'amount_range' => [50000, 200000], 'probability' => 60],
            ['category' => 'Gaji Karyawan', 'amount_range' => [200000, 400000], 'probability' => 10],
            ['category' => 'Listrik & Air', 'amount_range' => [75000, 150000], 'probability' => 15],
            ['category' => 'Transportasi', 'amount_range' => [25000, 100000], 'probability' => 25],
            ['category' => 'Pemasaran', 'amount_range' => [50000, 300000], 'probability' => 20],
        ];

        foreach ($expenseTypes as $type) {
            if (rand(1, 100) <= $type['probability']) {
                $amount = rand($type['amount_range'][0], $type['amount_range'][1]);

                Expense::create([
                    'expense_category' => $type['category'],
                    'amount' => $amount,
                    'description' => $this->getExpenseDescription($type['category']),
                    'expense_date' => $date->format('Y-m-d'),
                    'user_id' => $this->users[0]->id, // Admin
                    'created_at' => $date,
                    'updated_at' => $date
                ]);
            }
        }
    }

    private function getExpenseDescription($category)
    {
        $descriptions = [
            'Bahan Baku' => [
                'Pembelian ubi cilembu grade A 50kg',
                'Restok arang kayu 10 karung',
                'Pembelian ubi cilembu grade B 30kg',
                'Bahan tambahan keju dan coklat'
            ],
            'Operasional' => [
                'Biaya kebersihan dan sanitasi',
                'Pembelian gas LPG',
                'Perawatan peralatan masak',
                'Biaya administrasi harian'
            ],
            'Gaji Karyawan' => [
                'Gaji harian karyawan',
                'Bonus kinerja karyawan',
                'Tunjangan transport karyawan'
            ],
            'Listrik & Air' => [
                'Tagihan listrik bulanan',
                'Tagihan air PDAM',
                'Biaya generator cadangan'
            ],
            'Transportasi' => [
                'Biaya pengiriman produk',
                'Transport pembelian bahan baku',
                'Biaya distribusi ke outlet'
            ],
            'Pemasaran' => [
                'Biaya promosi media sosial',
                'Cetak brosur dan banner',
                'Event sampling produk',
                'Biaya iklan online'
            ]
        ];

        $categoryDescriptions = $descriptions[$category] ?? ['Pengeluaran ' . $category];
        return $categoryDescriptions[array_rand($categoryDescriptions)];
    }

    private function isHoliday($date)
    {
        // Indonesian national holidays and special days
        $holidays = [
            '01-01', // New Year
            '02-14', // Valentine's Day
            '03-17', // Nyepi (approximate)
            '04-21', // Kartini Day
            '05-01', // Labor Day
            '06-01', // Pancasila Day
            '08-17', // Independence Day
            '12-25', // Christmas
            '12-31', // New Year's Eve
        ];

        $monthDay = $date->format('m-d');
        return in_array($monthDay, $holidays);
    }
}
