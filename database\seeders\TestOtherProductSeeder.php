<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\OtherProduct;

class TestOtherProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Add test products
        $products = [
            [
                'name' => 'Es Teh Manis',
                'sku' => 'BVG001',
                'description' => 'Minuman teh manis dingin',
                'purchase_price' => 2000,
                'selling_price' => 5000,
                'current_stock' => 50,
                'min_stock_threshold' => 10,
                'category' => 'minuman',
                'unit' => 'gelas',
                'supplier' => 'Internal',
                'is_active' => true,
                'notes' => 'Minuman pendamping'
            ],
            [
                'name' => 'Es Jeruk',
                'sku' => 'BVG002',
                'description' => 'Minuman jeruk segar dingin',
                'purchase_price' => 3000,
                'selling_price' => 7000,
                'current_stock' => 40,
                'min_stock_threshold' => 10,
                'category' => 'minuman',
                'unit' => 'gelas',
                'supplier' => 'Internal',
                'is_active' => true,
                'notes' => 'Minuman pendamping'
            ],
            [
                'name' => 'Keripik Pisang',
                'sku' => 'SNK001',
                'description' => 'Camilan keripik pisang',
                'purchase_price' => 5000,
                'selling_price' => 10000,
                'current_stock' => 30,
                'min_stock_threshold' => 5,
                'category' => 'snack',
                'unit' => 'bungkus',
                'supplier' => 'Supplier Lokal',
                'is_active' => true,
                'notes' => 'Camilan pendamping'
            ],
            [
                'name' => 'Dodol Garut',
                'sku' => 'SNK002',
                'description' => 'Makanan tradisional dodol',
                'purchase_price' => 15000,
                'selling_price' => 25000,
                'current_stock' => 20,
                'min_stock_threshold' => 5,
                'category' => 'oleh-oleh',
                'unit' => 'kotak',
                'supplier' => 'Supplier Garut',
                'is_active' => true,
                'notes' => 'Oleh-oleh khas'
            ]
        ];
        
        foreach ($products as $product) {
            OtherProduct::updateOrCreate(
                ['name' => $product['name'], 'sku' => $product['sku']],
                $product
            );
        }
        
        $this->command->info('Test other products have been added!');
    }
} 