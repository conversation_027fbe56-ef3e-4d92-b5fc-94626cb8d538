<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $role): Response
    {
        // Get the user's role
        $userRole = $request->user()->role ?? null;

        // If user not logged in or doesn't have any valid role
        if (!$request->user() || !$userRole) {
            return $this->handleUnauthorized($request);
        }

        // Check if the user has the specific role required
        if ($role === \App\Models\User::ROLE_ADMIN && $userRole !== \App\Models\User::ROLE_ADMIN) {
            return $this->handleUnauthorized($request);
        }

        if ($role === \App\Models\User::ROLE_EMPLOYEE && $userRole !== \App\Models\User::ROLE_EMPLOYEE) {
            return $this->handleUnauthorized($request);
        }
        
        if ($role === \App\Models\User::ROLE_CASHIER && $userRole !== \App\Models\User::ROLE_CASHIER) {
            return $this->handleUnauthorized($request);
        }

        if ($role === \App\Models\User::ROLE_WAREHOUSE && $userRole !== \App\Models\User::ROLE_WAREHOUSE) {
            return $this->handleUnauthorized($request);
        }
        
        // Admin can access all routes
        if ($userRole === \App\Models\User::ROLE_ADMIN) {
            return $next($request);
        }

        return $next($request);
    }

    /**
     * Handle unauthorized access
     */
    private function handleUnauthorized(Request $request): Response
    {
        // If request is ajax, return json response
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json(['message' => 'Tidak memiliki izin.'], 403);
        }

        // Redirect to home with error message
        return redirect()->route('home')->with('error', 'Anda tidak memiliki izin untuk mengakses halaman tersebut.');
    }
}
