-- Dummy Data untuk database sim_ubi_cilembu
-- Pastikan database sudah dipilih
USE `sim_ubi_cilembu`;

-- Nonak<PERSON>fkan foreign key checks sementara
SET FOREIGN_KEY_CHECKS = 0;

-- Ha<PERSON> data yang ada (jika perlu)
DELETE FROM `raw_inventory`;
DELETE FROM `processed_inventory`;
DELETE FROM `other_products`;
DELETE FROM `transactions`;
DELETE FROM `transaction_items`;
DELETE FROM `production_logs`;

-- Data untuk raw_inventory
INSERT INTO `raw_inventory` (`batch_number`, `name`, `supplier_name`, `quantity_kg`, `cost_per_kg`, `total_cost`, `current_stock`, `purchase_date`, `expiry_date`, `quality`, `min_stock_threshold`, `is_active`, `created_at`, `updated_at`) VALUES
('BATCH-UBI001', 'Ubi Cilembu Kuning', '<PERSON><PERSON> Ma<PERSON>mu<PERSON> C<PERSON>', 100.00, 15000.00, 1500000.00, 75.50, '2024-05-01', '2024-06-15', 'A', 20.00, 1, '2024-05-01 08:00:00', '2024-05-17 10:00:00'),
('BATCH-UBI002', 'Ubi Cilembu Madu', 'Kelompok Tani Sukamaju', 80.00, 18000.00, 1440000.00, 65.00, '2024-05-05', '2024-06-20', 'A', 15.00, 1, '2024-05-05 09:30:00', '2024-05-17 10:00:00'),
('BATCH-UBI003', 'Ubi Cilembu Merah', 'Tani Makmur Cilembu', 50.00, 14000.00, 700000.00, 35.00, '2024-05-10', '2024-06-25', 'B', 10.00, 1, '2024-05-10 10:15:00', '2024-05-17 10:00:00'),
('BATCH-UBI004', 'Ubi Cilembu Premium', 'Supplier Ubi Jaya', 120.00, 20000.00, 2400000.00, 110.00, '2024-05-12', '2024-07-01', 'A', 25.00, 1, '2024-05-12 11:00:00', '2024-05-17 10:00:00'),
('BATCH-UBI005', 'Ubi Cilembu Organik', 'Tani Organik Sejahtera', 60.00, 25000.00, 1500000.00, 60.00, '2024-05-15', '2024-07-10', 'A', 15.00, 1, '2024-05-15 14:30:00', '2024-05-17 10:00:00');

-- Data untuk processed_inventory
INSERT INTO `processed_inventory` (`name`, `batch_number`, `raw_inventory_id`, `quantity_processed_kg`, `quantity_produced`, `cost_per_unit`, `selling_price`, `current_stock`, `production_date`, `expiry_date`, `product_type`, `min_stock_threshold`, `is_active`, `cost_per_item`, `created_at`, `updated_at`) VALUES
('Ubi Bakar Original', 'PROC-UBI001', 1, 10.00, 40, 3750.00, 10000.00, 25, '2024-05-02', '2024-05-09', 'Original', 10, 1, 3750.00, '2024-05-02 09:00:00', '2024-05-17 10:00:00'),
('Ubi Bakar Madu', 'PROC-UBI002', 2, 8.00, 30, 4800.00, 12000.00, 20, '2024-05-06', '2024-05-13', 'Premium', 8, 1, 4800.00, '2024-05-06 10:00:00', '2024-05-17 10:00:00'),
('Ubi Bakar Spesial', 'PROC-UBI003', 4, 5.00, 20, 6000.00, 15000.00, 15, '2024-05-13', '2024-05-20', 'Special', 5, 1, 6000.00, '2024-05-13 11:30:00', '2024-05-17 10:00:00'),
('Ubi Bakar Original XL', 'PROC-UBI004', 1, 12.00, 35, 4285.71, 12000.00, 30, '2024-05-03', '2024-05-10', 'Original', 10, 1, 4285.71, '2024-05-03 14:00:00', '2024-05-17 10:00:00'),
('Ubi Bakar Organik', 'PROC-UBI005', 5, 6.00, 25, 6000.00, 18000.00, 22, '2024-05-16', '2024-05-23', 'Premium', 8, 1, 6000.00, '2024-05-16 09:30:00', '2024-05-17 10:00:00');

-- Data untuk other_products
INSERT INTO `other_products` (`name`, `sku`, `description`, `purchase_price`, `selling_price`, `current_stock`, `min_stock_threshold`, `category`, `unit`, `supplier`, `is_active`, `created_at`, `updated_at`) VALUES
('Air Mineral 600ml', 'OTH-DRINK001', 'Air mineral dalam kemasan botol 600ml', 2500.00, 5000.00, 50, 20, 'Minuman', 'Botol', 'Distributor Minuman Jaya', 1, '2024-05-01 08:30:00', '2024-05-17 10:00:00'),
('Teh Botol 450ml', 'OTH-DRINK002', 'Teh manis dalam kemasan botol 450ml', 3500.00, 7000.00, 40, 15, 'Minuman', 'Botol', 'Distributor Minuman Jaya', 1, '2024-05-01 08:35:00', '2024-05-17 10:00:00'),
('Kopi Sachet', 'OTH-DRINK003', 'Kopi instan dalam kemasan sachet', 1500.00, 3000.00, 100, 30, 'Minuman', 'Sachet', 'Grosir Sembako Makmur', 1, '2024-05-01 08:40:00', '2024-05-17 10:00:00'),
('Keripik Singkong', 'OTH-SNACK001', 'Keripik singkong rasa original 100gr', 5000.00, 10000.00, 25, 10, 'Makanan Ringan', 'Bungkus', 'UD Snack Lezat', 1, '2024-05-01 08:45:00', '2024-05-17 10:00:00'),
('Keripik Pisang', 'OTH-SNACK002', 'Keripik pisang manis 100gr', 6000.00, 12000.00, 20, 8, 'Makanan Ringan', 'Bungkus', 'UD Snack Lezat', 1, '2024-05-01 08:50:00', '2024-05-17 10:00:00'),
('Saus Sambal Sachet', 'OTH-COND001', 'Saus sambal dalam kemasan sachet', 500.00, 1000.00, 200, 50, 'Bumbu', 'Sachet', 'Grosir Sembako Makmur', 1, '2024-05-01 08:55:00', '2024-05-17 10:00:00'),
('Saus Tomat Sachet', 'OTH-COND002', 'Saus tomat dalam kemasan sachet', 500.00, 1000.00, 180, 50, 'Bumbu', 'Sachet', 'Grosir Sembako Makmur', 1, '2024-05-01 09:00:00', '2024-05-17 10:00:00'),
('Mayones Sachet', 'OTH-COND003', 'Mayones dalam kemasan sachet', 1000.00, 2000.00, 150, 40, 'Bumbu', 'Sachet', 'Grosir Sembako Makmur', 1, '2024-05-01 09:05:00', '2024-05-17 10:00:00');

-- Data untuk production_logs
INSERT INTO `production_logs` (`raw_inventory_id`, `processed_inventory_id`, `user_id`, `raw_amount_used`, `produced_amount`, `raw_cost`, `additional_cost`, `total_cost`, `cost_per_item`, `raw_name`, `processed_name`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 10.00, 40, 150000.00, 0.00, 150000.00, 3750.00, 'Ubi Cilembu Kuning', 'Ubi Bakar Original', '2024-05-02 09:00:00', '2024-05-02 09:00:00'),
(2, 2, 1, 8.00, 30, 144000.00, 0.00, 144000.00, 4800.00, 'Ubi Cilembu Madu', 'Ubi Bakar Madu', '2024-05-06 10:00:00', '2024-05-06 10:00:00'),
(4, 3, 1, 5.00, 20, 100000.00, 20000.00, 120000.00, 6000.00, 'Ubi Cilembu Premium', 'Ubi Bakar Spesial', '2024-05-13 11:30:00', '2024-05-13 11:30:00'),
(1, 4, 1, 12.00, 35, 180000.00, 20000.00, 200000.00, 5714.29, 'Ubi Cilembu Kuning', 'Ubi Bakar Original XL', '2024-05-03 14:00:00', '2024-05-03 14:00:00'),
(5, 5, 1, 6.00, 25, 150000.00, 0.00, 150000.00, 6000.00, 'Ubi Cilembu Organik', 'Ubi Bakar Organik', '2024-05-16 09:30:00', '2024-05-16 09:30:00');

-- Data untuk transactions
INSERT INTO `transactions` (`invoice_number`, `user_id`, `customer_name`, `customer_phone`, `subtotal`, `tax`, `discount`, `total_amount`, `amount_paid`, `change_amount`, `payment_method`, `status`, `notes`, `created_at`, `updated_at`) VALUES
('INV-20240502-001', 2, 'Budi Santoso', '081234567890', 50000.00, 0.00, 0.00, 50000.00, 50000.00, 0.00, 'cash', 'completed', NULL, '2024-05-02 12:30:00', '2024-05-02 12:30:00'),
('INV-20240503-001', 2, 'Ani Wijaya', '082345678901', 72000.00, 0.00, 2000.00, 70000.00, 100000.00, 30000.00, 'cash', 'completed', NULL, '2024-05-03 15:45:00', '2024-05-03 15:45:00'),
('INV-20240505-001', 2, 'Citra Dewi', '083456789012', 36000.00, 0.00, 0.00, 36000.00, 36000.00, 0.00, 'transfer', 'completed', NULL, '2024-05-05 11:20:00', '2024-05-05 11:20:00'),
('INV-20240507-001', 2, 'Dodi Pratama', '084567890123', 45000.00, 0.00, 5000.00, 40000.00, 50000.00, 10000.00, 'cash', 'completed', NULL, '2024-05-07 14:10:00', '2024-05-07 14:10:00'),
('INV-20240510-001', 2, 'Eka Putri', '085678901234', 60000.00, 0.00, 0.00, 60000.00, 60000.00, 0.00, 'qris', 'completed', NULL, '2024-05-10 16:30:00', '2024-05-10 16:30:00');

-- Data untuk transaction_items
INSERT INTO `transaction_items` (`transaction_id`, `product_id`, `processed_inventory_id`, `product_name`, `price`, `quantity`, `discount`, `subtotal`, `created_at`, `updated_at`) VALUES
(1, NULL, 1, 'Ubi Bakar Original', 10000.00, 3, 0.00, 30000.00, '2024-05-02 12:30:00', '2024-05-02 12:30:00'),
(1, 1, NULL, 'Air Mineral 600ml', 5000.00, 4, 0.00, 20000.00, '2024-05-02 12:30:00', '2024-05-02 12:30:00'),
(2, NULL, 2, 'Ubi Bakar Madu', 12000.00, 5, 0.00, 60000.00, '2024-05-03 15:45:00', '2024-05-03 15:45:00'),
(2, 2, NULL, 'Teh Botol 450ml', 7000.00, 2, 2000.00, 12000.00, '2024-05-03 15:45:00', '2024-05-03 15:45:00'),
(3, NULL, 1, 'Ubi Bakar Original', 10000.00, 3, 0.00, 30000.00, '2024-05-05 11:20:00', '2024-05-05 11:20:00'),
(3, 3, NULL, 'Kopi Sachet', 3000.00, 2, 0.00, 6000.00, '2024-05-05 11:20:00', '2024-05-05 11:20:00'),
(4, NULL, 4, 'Ubi Bakar Original XL', 12000.00, 3, 5000.00, 31000.00, '2024-05-07 14:10:00', '2024-05-07 14:10:00'),
(4, 1, NULL, 'Air Mineral 600ml', 5000.00, 2, 0.00, 10000.00, '2024-05-07 14:10:00', '2024-05-07 14:10:00'),
(4, 6, NULL, 'Saus Sambal Sachet', 1000.00, 4, 0.00, 4000.00, '2024-05-07 14:10:00', '2024-05-07 14:10:00'),
(5, NULL, 3, 'Ubi Bakar Spesial', 15000.00, 4, 0.00, 60000.00, '2024-05-10 16:30:00', '2024-05-10 16:30:00');

-- Aktifkan kembali foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
