<?php

namespace App\Exports;

use App\Models\ProcessedInventory;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class ExpiryRecommendationExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle, ShouldAutoSize
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return ProcessedInventory::getExpiryTrackingList();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Nama Produk',
            'Jenis Produk',
            'Batch',
            'Tanggal Produksi',
            'Tanggal Kadaluarsa',
            '<PERSON><PERSON> Hari',
            'Stok Tersedia (Unit)',
            'Harga Satuan',
            'Total Nilai',
            'Prioritas',
            'Pasar Rekomendasi',
            'Status'
        ];
    }

    /**
     * @param mixed $item
     * @return array
     */
    public function map($item): array
    {
        // Menghitung total nilai
        $totalValue = $item->current_stock * $item->cost_per_unit;
        
        // Status berdasarkan prioritas
        $status = 'Normal';
        if ($item->priority_level === 'Tinggi') {
            $status = 'Segera Jual!';
        } elseif ($item->priority_level === 'Sedang') {
            $status = 'Pantau';
        }
        
        return [
            $item->id,
            $item->name,
            $item->product_type,
            $item->batch_number,
            $item->production_date ? $item->production_date->format('d/m/Y') : '-',
            $item->expiry_date ? $item->expiry_date->format('d/m/Y') : '-',
            $item->days_until_expiry,
            $item->current_stock,
            'Rp ' . number_format($item->cost_per_unit, 0, ',', '.'),
            'Rp ' . number_format($totalValue, 0, ',', '.'),
            $item->priority_level,
            $item->recommended_market ?: 'Belum ada rekomendasi',
            $status
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        $highPriorityRows = [];
        $mediumPriorityRows = [];
        
        // Dapatkan data untuk styling baris
        $data = $this->collection();
        $row = 2; // start from row 2 (after header)
        
        foreach ($data as $item) {
            if ($item->priority_level === 'Tinggi') {
                $highPriorityRows[] = $row;
            } elseif ($item->priority_level === 'Sedang') {
                $mediumPriorityRows[] = $row;
            }
            $row++;
        }
        
        // Style untuk header
        $sheet->getStyle('A1:M1')->applyFromArray([
            'font' => [
                'bold' => true,
                'color' => ['rgb' => 'FFFFFF'],
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => ['rgb' => '8B4513'],
            ],
        ]);
        
        // Style untuk baris prioritas tinggi
        foreach ($highPriorityRows as $row) {
            $sheet->getStyle('A' . $row . ':M' . $row)->applyFromArray([
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFCCCC'],
                ],
            ]);
        }
        
        // Style untuk baris prioritas sedang
        foreach ($mediumPriorityRows as $row) {
            $sheet->getStyle('A' . $row . ':M' . $row)->applyFromArray([
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'FFFFCC'],
                ],
            ]);
        }
        
        return [];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Rekomendasi Ubi Matang ' . Carbon::now()->format('d-m-Y');
    }
}
