@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-receipt"></i>
        <span>Detail Transaksi</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Detail Transaksi #{{ $transaction->invoice_number }}</span>
                    <div>
                        <a href="{{ route('transactions.print', $transaction) }}" class="btn btn-primary me-2" target="_blank">
                            <i class="fas fa-print"></i> Cetak
                        </a>
                        <a href="{{ route('transactions.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Informasi Transaksi</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 40%">No. Invoice</th>
                                    <td>{{ $transaction->invoice_number }}</td>
                                </tr>
                                <tr>
                                    <th>Tanggal & Waktu</th>
                                    <td>{{ $transaction->created_at->format('d M Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th>Kasir</th>
                                    <td>{{ $transaction->user->name ?? 'Admin' }}</td>
                                </tr>
                                <tr>
                                    <th>Catatan</th>
                                    <td>{{ $transaction->note ?? '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Informasi Pembayaran</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 40%">Total</th>
                                    <td>Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</td>
                                </tr>
                                <tr>
                                    <th>Metode Pembayaran</th>
                                    <td>{{ ucfirst($transaction->payment_method) }}</td>
                                </tr>
                                @if($transaction->payment_gateway_paid_at)
                                <tr>
                                    <th>Dibayar Pada</th>
                                    <td>{{ $transaction->payment_gateway_paid_at->format('d M Y H:i:s') }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <th>Jumlah Dibayar</th>
                                    <td>Rp {{ number_format($transaction->amount_paid, 0, ',', '.') }}</td>
                                </tr>
                                @if($transaction->change_amount > 0)
                                <tr>
                                    <th>Kembalian</th>
                                    <td>Rp {{ number_format($transaction->change_amount, 0, ',', '.') }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>

                    <h5>Daftar Item</h5>
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Nama Produk</th>
                                    <th>Harga Satuan</th>
                                    <th>Jumlah</th>
                                    <th>Subtotal</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($transaction->items as $index => $item)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>{{ $item->product_name }}</td>
                                    <td>Rp {{ number_format($item->price, 0, ',', '.') }}</td>
                                    <td>{{ $item->quantity }}</td>
                                    <td>Rp {{ number_format($item->subtotal, 0, ',', '.') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-end">Total</th>
                                    <th>Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>





@endsection