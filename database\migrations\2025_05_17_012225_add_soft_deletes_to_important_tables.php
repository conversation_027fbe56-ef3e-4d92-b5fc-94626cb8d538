<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Menambahkan soft delete ke tabel raw_inventory
        Schema::table('raw_inventory', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Menambahkan soft delete ke tabel processed_inventory
        Schema::table('processed_inventory', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Menambahkan soft delete ke tabel transactions
        Schema::table('transactions', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Menambahkan soft delete ke tabel other_products
        Schema::table('other_products', function (Blueprint $table) {
            $table->softDeletes();
        });

        // Menambahkan soft delete ke tabel production_logs
        Schema::table('production_logs', function (Blueprint $table) {
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Menghapus soft delete dari tabel raw_inventory
        Schema::table('raw_inventory', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Menghapus soft delete dari tabel processed_inventory
        Schema::table('processed_inventory', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Menghapus soft delete dari tabel transactions
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Menghapus soft delete dari tabel other_products
        Schema::table('other_products', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Menghapus soft delete dari tabel production_logs
        Schema::table('production_logs', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
    }
};
