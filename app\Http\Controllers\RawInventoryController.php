<?php

namespace App\Http\Controllers;

use App\Models\RawInventory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class RawInventoryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $rawInventory = RawInventory::where('is_active', true)
            ->orderBy('name')
            ->get();
        $lowStock = RawInventory::whereRaw('current_stock <= min_stock_threshold')->get();

        return view('inventory.raw.index', compact('rawInventory', 'lowStock'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('inventory.raw.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'supplier_name' => 'nullable|string|max:255',
            'quantity_kg' => 'required|numeric|min:0',
            'cost_per_kg' => 'required|numeric|min:0',
            'quality' => 'required|in:A,B,C',
            'purchase_date' => 'required|date',
            'expiry_date' => 'nullable|date|after:purchase_date',
            'notes' => 'nullable|string',
            'min_stock_threshold' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Generate batch number
        $batchNumber = 'BATCH-' . Str::upper(Str::random(8));

        // Calculate total cost
        $totalCost = $request->quantity_kg * $request->cost_per_kg;



        // Create raw inventory with all fields
        RawInventory::create([
            'name' => $request->name,
            'batch_number' => $batchNumber,
            'supplier_name' => $request->supplier_name,
            'quantity_kg' => $request->quantity_kg,
            'cost_per_kg' => $request->cost_per_kg,
            'total_cost' => $totalCost,
            'purchase_date' => $request->purchase_date,
            'expiry_date' => $request->expiry_date,
            'quality' => $request->quality,
            'notes' => $request->notes,
            'current_stock' => $request->quantity_kg,
            'min_stock_threshold' => $request->min_stock_threshold,
            'is_active' => true
        ]);

        return redirect()->route('raw-inventory.index')
            ->with('success', 'Stok ubi mentah berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(RawInventory $rawInventory)
    {
        return view('inventory.raw.show', compact('rawInventory'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(RawInventory $rawInventory)
    {
        // Mendapatkan daftar raw inventories untuk dropdown atau referensi lain jika diperlukan
        $rawInventories = RawInventory::where('is_active', true)
            ->orderBy('name')
            ->get();
        
        return view('inventory.raw.edit', compact('rawInventory', 'rawInventories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, RawInventory $rawInventory)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'supplier_name' => 'nullable|string|max:255',
            'current_stock' => 'required|numeric|min:0',
            'cost_per_kg' => 'required|numeric|min:0',
            'quality' => 'required|in:A,B,C',
            'expiry_date' => 'nullable|date|after:purchase_date',
            'notes' => 'nullable|string',
            'min_stock_threshold' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Update total cost
        $totalCost = $request->current_stock * $request->cost_per_kg;

        $rawInventory->update([
            'name' => $request->name,
            'supplier_name' => $request->supplier_name,
            'current_stock' => $request->current_stock,
            'cost_per_kg' => $request->cost_per_kg,
            'total_cost' => $totalCost,
            'quality' => $request->quality,
            'expiry_date' => $request->expiry_date,
            'notes' => $request->notes,
            'min_stock_threshold' => $request->min_stock_threshold,
        ]);

        return redirect()->route('raw-inventory.index')
            ->with('success', 'Stok ubi mentah berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(RawInventory $rawInventory)
    {
        // Check if this raw material is used in processed items
        if ($rawInventory->processedInventory()->count() > 0) {
            return redirect()->back()
                ->with('error', 'Tidak dapat menghapus! Ubi mentah ini digunakan dalam produk ubi matang.');
        }

        // Soft delete by setting is_active to false
        $rawInventory->update(['is_active' => false]);

        return redirect()->route('raw-inventory.index')
            ->with('success', 'Stok ubi mentah berhasil dihapus.');
    }

    /**
     * Add stock to existing raw inventory
     */
    public function addStock(Request $request, RawInventory $rawInventory)
    {
        $validator = Validator::make($request->all(), [
            'additional_stock' => 'required|numeric|min:0.1',
            'cost_per_kg' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Calculate new average cost
        $currentValue = $rawInventory->current_stock * $rawInventory->cost_per_kg;
        $newValue = $request->additional_stock * $request->cost_per_kg;
        $newTotalStock = $rawInventory->current_stock + $request->additional_stock;
        
        if ($newTotalStock > 0) {
            $newAverageCost = ($currentValue + $newValue) / $newTotalStock;
        } else {
            $newAverageCost = $request->cost_per_kg;
        }

        // Update stock and costs
        $rawInventory->update([
            'current_stock' => $newTotalStock,
            'cost_per_kg' => $newAverageCost,
            'total_cost' => $newTotalStock * $newAverageCost
        ]);

        return redirect()->route('raw-inventory.index')
            ->with('success', 'Stok ubi mentah berhasil ditambahkan.');
    }
}
