<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use App\Models\User;
use Symfony\Component\HttpFoundation\Response;

class CustomSessionTimeout
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Jika user sudah login, atur session timeout berdasarkan role
        if ($request->user()) {
            if ($request->user()->isAdmin()) {
                // Admin: 60 menit (1 jam)
                Config::set('session.lifetime', 60);
            } else {
                // Karyawan: 480 menit (8 jam)
                Config::set('session.lifetime', 480);
            }
        }

        return $next($request);
    }
}
