<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique(); // Nomor invoice/faktur
            $table->unsignedBigInteger('user_id'); // User yang melakukan transaksi
            $table->string('customer_name')->nullable(); // Nama pelanggan
            $table->string('customer_phone')->nullable(); // Kontak pelanggan
            $table->decimal('subtotal', 12, 2); // Subtotal transaksi
            $table->decimal('tax', 12, 2)->default(0); // Pajak
            $table->decimal('discount', 12, 2)->default(0); // Diskon
            $table->decimal('total_amount', 12, 2); // Total transaksi
            $table->decimal('amount_paid', 12, 2); // Jumlah yang dibay<PERSON>an
            $table->decimal('change_amount', 12, 2)->default(0); // Kembalian
            $table->enum('payment_method', ['cash', 'transfer', 'qris', 'debit', 'credit']); // Metode pembayaran
            $table->enum('status', ['completed', 'cancelled', 'refunded'])->default('completed'); // Status transaksi
            $table->text('notes')->nullable(); // Catatan transaksi
            $table->timestamps();
            
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
