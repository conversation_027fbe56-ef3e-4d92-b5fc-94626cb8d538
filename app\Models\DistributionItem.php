<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Auditable;

class DistributionItem extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'distribution_id',
        'processed_inventory_id',
        'other_product_id',
        'quantity',
        'price_per_item',
        'total_price'
    ];

    /**
     * Get the distribution this item belongs to.
     */
    public function distribution(): BelongsTo
    {
        return $this->belongsTo(Distribution::class);
    }

    /**
     * Get the processed inventory associated with this item.
     */
    public function processedInventory(): BelongsTo
    {
        return $this->belongsTo(ProcessedInventory::class);
    }

    /**
     * Get the other product associated with this item.
     */
    public function otherProduct(): BelongsTo
    {
        return $this->belongsTo(OtherProduct::class);
    }

    /**
     * Get the product name (either from processed inventory or other product).
     */
    public function getProductNameAttribute()
    {
        if ($this->processed_inventory_id) {
            return $this->processedInventory->name ?? 'Unknown Processed Item';
        } elseif ($this->other_product_id) {
            return $this->otherProduct->name ?? 'Unknown Other Product';
        }
        
        return 'Unknown Product';
    }
}
