<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rekomendasi Ubi Matang untuk Se<PERSON>a Dijual</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #8B4513;
            padding-bottom: 10px;
        }
        .logo {
            font-size: 22px;
            font-weight: bold;
            color: #8B4513;
            margin-bottom: 5px;
        }
        h1 {
            font-size: 18px;
            color: #333;
            margin: 10px 0;
        }
        .date {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .table-container {
            width: 100%;
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            font-size: 12px;
        }
        th {
            background-color: #8B4513;
            color: white;
            text-align: left;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .priority-high {
            background-color: #ffcccc;
            border-left: 4px solid #dc3545;
        }
        .priority-medium {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .priority-low {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .badge {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
        }
        .badge-high {
            background-color: #dc3545;
            color: white;
        }
        .badge-medium {
            background-color: #ffc107;
            color: #333;
        }
        .badge-low {
            background-color: #28a745;
            color: white;
        }
        .summary {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
        }
        .summary-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
            color: #8B4513;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">Ubi Bakar Cilembu</div>
        <h1>Rekomendasi Ubi Matang untuk Segera Dijual</h1>
        <div class="date">Tanggal: {{ $date }}</div>
    </div>
    
    <div class="summary">
        <div class="summary-title">Ringkasan</div>
        <p>
            Prioritas Tinggi: {{ $recommendedItems->where('priority_level', 'Tinggi')->count() }} item<br>
            Prioritas Sedang: {{ $recommendedItems->where('priority_level', 'Sedang')->count() }} item<br>
            Prioritas Rendah: {{ $recommendedItems->where('priority_level', 'Rendah')->count() }} item<br>
            <br>
            Potensi Kerugian: Rp {{ number_format($recommendedItems->where('priority_level', 'Tinggi')->sum(function($item) {
                return $item->current_stock * $item->cost_per_unit;
            }), 0, ',', '.') }}
        </p>
    </div>
    
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>Produk</th>
                    <th>Batch</th>
                    <th>Tgl Produksi</th>
                    <th>Tgl Kadaluarsa</th>
                    <th>Sisa Hari</th>
                    <th>Stok</th>
                    <th>Nilai Total</th>
                    <th>Prioritas</th>
                    <th>Pasar Rekomendasi</th>
                </tr>
            </thead>
            <tbody>
                @forelse($recommendedItems as $item)
                    @php
                        $priorityClass = '';
                        $badgeClass = '';
                        
                        if ($item->priority_level === 'Tinggi') {
                            $priorityClass = 'priority-high';
                            $badgeClass = 'badge-high';
                        } elseif ($item->priority_level === 'Sedang') {
                            $priorityClass = 'priority-medium';
                            $badgeClass = 'badge-medium';
                        } else {
                            $priorityClass = 'priority-low';
                            $badgeClass = 'badge-low';
                        }
                        
                        $totalValue = $item->current_stock * $item->cost_per_unit;
                    @endphp
                    <tr class="{{ $priorityClass }}">
                        <td>
                            <strong>{{ $item->name }}</strong><br>
                            <small>{{ $item->product_type }}</small>
                        </td>
                        <td>{{ $item->batch_number }}</td>
                        <td>{{ $item->production_date ? $item->production_date->format('d/m/Y') : '-' }}</td>
                        <td>{{ $item->expiry_date ? $item->expiry_date->format('d/m/Y') : '-' }}</td>
                        <td>
                            <strong>{{ $item->days_until_expiry >= 0 ? $item->days_until_expiry : 'Kadaluarsa' }}</strong>
                        </td>
                        <td>{{ $item->current_stock }}</td>
                        <td>Rp {{ number_format($totalValue, 0, ',', '.') }}</td>
                        <td>
                            <span class="badge {{ $badgeClass }}">
                                {{ $item->priority_level }}
                            </span>
                        </td>
                        <td>
                            {{ $item->recommended_market ?: 'Belum ada rekomendasi' }}
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="9" style="text-align: center;">Tidak ada data rekomendasi ubi matang.</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    
    <div class="footer">
        <p>Laporan ini dibuat secara otomatis oleh Sistem Informasi Manajemen Ubi Bakar Cilembu © {{ date('Y') }}</p>
    </div>
</body>
</html>
