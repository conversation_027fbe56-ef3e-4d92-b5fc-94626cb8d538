<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UpdateUserPasswords extends Migration
{
    public function up()
    {
        // Update Admin password
        DB::table('users')
            ->where('email', '<EMAIL>')
            ->update([
                'password' => Hash::make('admin123')
            ]);

        // Update Karyawan password
        DB::table('users')
            ->where('email', '<EMAIL>')
            ->update([
                'password' => Hash::make('karyawan123')
            ]);
    }

    public function down()
    {
        // Revert to original password if needed
        DB::table('users')
            ->whereIn('email', ['<EMAIL>', '<EMAIL>'])
            ->update([
                'password' => Hash::make('password')
            ]);
    }
} 