<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EmployeeMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Cek apakah user login dan rolenya employee
        if ($request->user() && $request->user()->isEmployee()) {
            return $next($request);
        }

        // Jika bukan employee/karyawan, redirect ke home
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json(['message' => 'Tidak memiliki izin.'], 403);
        }

        return redirect()->route('home')->with('error', 'Anda tidak memiliki izin untuk mengakses halaman tersebut.');
    }
}
