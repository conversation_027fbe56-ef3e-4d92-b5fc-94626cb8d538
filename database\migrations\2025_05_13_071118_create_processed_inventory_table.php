<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('processed_inventory', function (Blueprint $table) {
            $table->id();
            $table->string('batch_number')->unique(); // Nomor batch produksi
            $table->unsignedBigInteger('raw_inventory_id')->nullable(); // Referensi ke bahan mentah
            $table->decimal('quantity_processed_kg', 10, 2); // Jumlah bahan mentah yang diproses
            $table->decimal('quantity_produced', 10, 2); // <PERSON><PERSON><PERSON> ubi bakar yang dihasilkan (dalam buah)
            $table->decimal('cost_per_unit', 10, 2); // Biaya produksi per unit
            $table->decimal('selling_price', 10, 2); // Harga jual per unit
            $table->date('production_date'); // Tanggal produksi
            $table->date('expiry_date'); // Tanggal kadaluarsa
            $table->enum('product_type', ['Original', 'Premium', 'Special']); // Jenis produk
            $table->decimal('current_stock', 10, 2); // Stok saat ini
            $table->boolean('is_active')->default(true); // Status aktif
            $table->text('notes')->nullable(); // Catatan tambahan
            $table->timestamps();
            
            $table->foreign('raw_inventory_id')->references('id')->on('raw_inventory')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('processed_inventory');
    }
};
