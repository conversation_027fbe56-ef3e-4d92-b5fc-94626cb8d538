<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EmployeeDashboardController;
use App\Http\Controllers\RawInventoryController;
use App\Http\Controllers\ProcessedInventoryController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\ProductionController;
use App\Http\Controllers\DistributionController;
use App\Http\Controllers\FinancialReportController;
use App\Http\Controllers\ExpiryRecommendationController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\OtherProductController;
use App\Http\Controllers\Admin\UserManagementController;
use App\Http\Controllers\Admin\AuditLogController;

// Landing page
Route::get('/', function () {
    return redirect()->route('login');
});





// Route untuk test login langsung
Route::get('/test-direct-login', function() {
    try {
        // Cari user admin
        $user = \App\Models\User::where('email', '<EMAIL>')->first();

        if ($user) {
            // Login langsung
            Auth::login($user);
            return redirect('/dashboard')->with('success', 'Login berhasil!');
        } else {
            return 'User admin tidak ditemukan. <a href="/create-simple-admin">Buat user admin</a>';
        }

    } catch (\Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
});

// Route untuk buat user admin sederhana
Route::get('/create-simple-admin', function() {
    try {
        // Hapus user admin yang ada
        \App\Models\User::where('email', '<EMAIL>')->delete();

        // Buat user admin baru
        $admin = new \App\Models\User();
        $admin->name = 'Administrator';
        $admin->email = '<EMAIL>';
        $admin->password = bcrypt('password');
        $admin->role = 'admin';
        $admin->email_verified_at = now();
        $admin->save();

        return [
            'status' => 'success',
            'message' => 'Admin user created!',
            'user' => [
                'id' => $admin->id,
                'name' => $admin->name,
                'email' => $admin->email,
                'role' => $admin->role
            ],
            'next_step' => 'Go to /login or /test-direct-login'
        ];

    } catch (\Exception $e) {
        return [
            'status' => 'error',
            'message' => 'Failed: ' . $e->getMessage()
        ];
    }
});

// Authentication routes
Auth::routes();

// Home routes (setelah login)
Route::middleware(['auth'])->group(function () {
    Route::get('/home', [DashboardController::class, 'index'])->name('home');
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
});

// Routes untuk Admin
Route::middleware(['auth', \App\Http\Middleware\AdminMiddleware::class])->group(function () {
    
    // Dashboard & Reports
    Route::get('/reports/sales', [DashboardController::class, 'salesReport'])->name('reports.sales');
    Route::get('/reports/financial', [FinancialReportController::class, 'index'])->name('reports.financial');
    Route::get('/reports/inventory', [FinancialReportController::class, 'inventoryReport'])->name('reports.inventory');
    
    // Raw Inventory Management
    Route::resource('raw-inventory', RawInventoryController::class);
    Route::post('/raw-inventory/{id}/restock', [RawInventoryController::class, 'restock'])->name('raw-inventory.restock');
    
    // Processed Inventory Management
    Route::resource('processed-inventory', ProcessedInventoryController::class);
    Route::post('/processed-inventory/{id}/restock', [ProcessedInventoryController::class, 'restock'])->name('processed-inventory.restock');
    
    // Other Products Management
    Route::resource('other-products', OtherProductController::class);
    Route::post('/other-products/{id}/restock', [OtherProductController::class, 'restock'])->name('other-products.restock');
    
    // Production Management
    Route::resource('production', ProductionController::class);
    Route::post('/production/{id}/complete', [ProductionController::class, 'complete'])->name('production.complete');
    
    // Supplier Management
    Route::resource('suppliers', SupplierController::class);
    
    // Expense Management
    Route::resource('expenses', ExpenseController::class);
    
    // User Management
    Route::resource('admin/users', UserManagementController::class);
    Route::post('/admin/users/{user}/approve', [UserManagementController::class, 'approve'])->name('admin.users.approve');
    Route::post('/admin/users/{user}/reject', [UserManagementController::class, 'reject'])->name('admin.users.reject');
    
    // Audit Logs
    Route::get('/admin/audit-logs', [AuditLogController::class, 'index'])->name('admin.audit-logs.index');
    
    // Expiry Recommendations
    Route::prefix('expiry-recommendations')->name('expiry-recommendations.')->group(function () {
        Route::get('/', [ExpiryRecommendationController::class, 'index'])->name('index');
        Route::get('/sales-report', [ExpiryRecommendationController::class, 'salesReport'])->name('sales-report');
        Route::post('/set-market/{id}', [ExpiryRecommendationController::class, 'setMarket'])->name('set-market');
        Route::post('/create-distribution/{id}', [ExpiryRecommendationController::class, 'createDistribution'])->name('create-distribution');
    });
    
    // Distribution Routes (hanya dapat diakses oleh Admin dan Warehouse)
    Route::resource('distributions', DistributionController::class);
    Route::get('/distributions/report', [DistributionController::class, 'report'])->name('distributions.report');
    Route::post('/distributions/{distribution}/update-status', [DistributionController::class, 'updateStatus'])->name('distributions.update-status');
});

// Routes khusus untuk employee/karyawan lama (backward compatibility)
Route::middleware(['auth', \App\Http\Middleware\EmployeeMiddleware::class])->group(function () {
    Route::get('/employee-dashboard', [EmployeeDashboardController::class, 'index'])->name('employee.dashboard');
});

// Routes untuk semua authenticated users
Route::middleware(['auth'])->group(function () {
    
    // Transaction Management
    Route::resource('transactions', TransactionController::class);
    Route::post('/transactions/{transaction}/cancel', [TransactionController::class, 'cancel'])->name('transactions.cancel');
    Route::get('/pos', [TransactionController::class, 'create'])->name('pos');

    // Payment Gateway routes (authenticated)
    Route::prefix('payment')->group(function () {
        Route::match(['GET', 'POST'], '/create/{transaction}', [App\Http\Controllers\PaymentController::class, 'createPayment'])->name('payment.create');
        Route::get('/status/{transaction}', [App\Http\Controllers\PaymentController::class, 'checkStatus'])->name('payment.status');
        Route::post('/update-status/{transaction}', [App\Http\Controllers\PaymentController::class, 'updateStatus'])->name('payment.update-status');
    });

    // Distribution report - accessible by all authenticated users
    Route::get('/distributions/report', [DistributionController::class, 'report'])->name('distributions.report');

});
