<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProcessedInventory;
use App\Models\OtherProduct;

class UpdateProductImagesSeeder extends Seeder
{
    public function run(): void
    {
        // Update ProcessedInventory images
        ProcessedInventory::where('name', 'LIKE', '%Original%')->update(['image' => 'ubibakar1.jpeg']);
        ProcessedInventory::where('name', 'LIKE', '%Coklat%')->update(['image' => 'ubibakar1.jpeg']);
        ProcessedInventory::where('name', 'LIKE', '%Keju%')->update(['image' => 'ubibakar1.jpeg']);
        ProcessedInventory::where('name', 'LIKE', '%Spesial%')->update(['image' => 'ubibakar1.jpeg']);

        // Update OtherProduct images
        OtherProduct::where('name', 'LIKE', '%TEH%')->update(['image' => 'produk lain 1.png']);
        OtherProduct::where('name', 'LIKE', '%ROKOK%')->update(['image' => 'produk lain 2.jpg']);
        OtherProduct::where('name', 'LIKE', '%Es Teh%')->update(['image' => 'produk lain 1.png']);
        OtherProduct::where('name', 'LIKE', '%Es Jeruk%')->update(['image' => 'produk lain 3.jpg']);
        OtherProduct::where('name', 'LIKE', '%Keripik%')->update(['image' => 'produk lain 5.jpg']);

        $this->command->info('Product images updated successfully!');
    }
}
