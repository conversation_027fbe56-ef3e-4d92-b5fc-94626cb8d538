# 🏗️ SYSTEM ARCHITECTURE DIAGRAM - SISTEM UBI BAKAR CILEMBU

## System Architecture Overview

```mermaid
graph TB
    %% User Layer
    subgraph "👥 User Layer"
        Admin[👨‍💼 Admin]
        Employee[👨‍💻 Employee/<PERSON><PERSON><PERSON>]
        Customer[👤 Customer]
    end
    
    %% Presentation Layer
    subgraph "🖥️ Presentation Layer"
        WebBrowser[🌐 Web Browser]
        MobileDevice[📱 Mobile Device]
        TabletPOS[📟 Tablet POS]
    end
    
    %% Application Layer
    subgraph "⚡ Application Layer (Laravel 12)"
        subgraph "🎮 Controllers"
            AuthController[🔐 Auth Controller]
            DashboardController[📊 Dashboard Controller]
            TransactionController[💰 Transaction Controller]
            InventoryController[📦 Inventory Controller]
            PaymentController[💳 Payment Controller]
            ProductionController[🏭 Production Controller]
            DistributionController[🚚 Distribution Controller]
        end
        
        subgraph "🔒 Middleware"
            AuthMiddleware[🔐 Authentication]
            RoleMiddleware[👥 Role-based Access]
            CSRFMiddleware[🛡️ CSRF Protection]
        end
        
        subgraph "📋 Models (Eloquent ORM)"
            UserModel[👤 User]
            TransactionModel[💰 Transaction]
            InventoryModel[📦 Inventory]
            ProductionModel[🏭 Production]
            DistributionModel[🚚 Distribution]
        end
        
        subgraph "🎨 Views (Blade Templates)"
            AdminViews[👨‍💼 Admin Views]
            EmployeeViews[👨‍💻 Employee Views]
            POSViews[💰 POS Interface]
            ReportViews[📊 Report Views]
        end
    end
    
    %% Service Layer
    subgraph "🔧 Service Layer"
        PaymentService[💳 Payment Service]
        InventoryService[📦 Inventory Service]
        ReportService[📊 Report Service]
        NotificationService[🔔 Notification Service]
    end
    
    %% External Services
    subgraph "🌐 External Services"
        MidtransAPI[💳 Midtrans Payment Gateway]
        EmailService[📧 Email Service]
        SMSService[📱 SMS Service]
    end
    
    %% Data Layer
    subgraph "🗄️ Data Layer"
        MySQL[(🗄️ MySQL Database)]
        Redis[(⚡ Redis Cache)]
        FileStorage[📁 File Storage]
    end
    
    %% Infrastructure Layer
    subgraph "🏗️ Infrastructure Layer"
        WebServer[🌐 Web Server]
        AppServer[⚙️ Application Server]
        DatabaseServer[🗄️ Database Server]
        CacheServer[⚡ Cache Server]
    end
    
    %% User Interactions
    Admin --> WebBrowser
    Employee --> TabletPOS
    Customer --> MobileDevice
    
    %% Presentation to Application
    WebBrowser --> AuthController
    TabletPOS --> TransactionController
    MobileDevice --> PaymentController
    
    %% Controller Interactions
    AuthController --> AuthMiddleware
    TransactionController --> RoleMiddleware
    PaymentController --> CSRFMiddleware
    
    %% Middleware to Models
    AuthMiddleware --> UserModel
    RoleMiddleware --> TransactionModel
    CSRFMiddleware --> InventoryModel
    
    %% Controllers to Services
    TransactionController --> PaymentService
    InventoryController --> InventoryService
    DashboardController --> ReportService
    
    %% Services to External
    PaymentService --> MidtransAPI
    NotificationService --> EmailService
    NotificationService --> SMSService
    
    %% Models to Database
    UserModel --> MySQL
    TransactionModel --> MySQL
    InventoryModel --> MySQL
    ProductionModel --> MySQL
    DistributionModel --> MySQL
    
    %% Caching
    ReportService --> Redis
    InventoryService --> Redis
    
    %% File Storage
    InventoryModel --> FileStorage
    
    %% Views Rendering
    DashboardController --> AdminViews
    TransactionController --> POSViews
    InventoryController --> EmployeeViews
    
    %% Infrastructure
    WebBrowser --> WebServer
    WebServer --> AppServer
    AppServer --> DatabaseServer
    AppServer --> CacheServer
    
    %% Styling
    classDef userLayer fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef presentationLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef applicationLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef serviceLayer fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef externalLayer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef dataLayer fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef infraLayer fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    
    class Admin,Employee,Customer userLayer
    class WebBrowser,MobileDevice,TabletPOS presentationLayer
    class AuthController,DashboardController,TransactionController,InventoryController,PaymentController,ProductionController,DistributionController,AuthMiddleware,RoleMiddleware,CSRFMiddleware,UserModel,TransactionModel,InventoryModel,ProductionModel,DistributionModel,AdminViews,EmployeeViews,POSViews,ReportViews applicationLayer
    class PaymentService,InventoryService,ReportService,NotificationService serviceLayer
    class MidtransAPI,EmailService,SMSService externalLayer
    class MySQL,Redis,FileStorage dataLayer
    class WebServer,AppServer,DatabaseServer,CacheServer infraLayer
```

## 📊 **Technology Stack Summary**

### **🎯 Frontend Technologies**
- **Blade Templates** - Server-side rendering
- **Bootstrap 5** - Responsive UI framework
- **Chart.js** - Data visualization
- **JavaScript/jQuery** - Interactive features
- **TailwindCSS 4.0** - Utility-first CSS
- **Vite** - Modern build tool

### **⚙️ Backend Technologies**
- **Laravel 12** - PHP framework
- **PHP 8.2+** - Programming language
- **Eloquent ORM** - Database abstraction
- **Laravel Auth** - Authentication system
- **Middleware** - Request filtering

### **🗄️ Database & Storage**
- **MySQL** - Primary database
- **Redis** - Caching layer
- **File System** - Image storage
- **Migration System** - Database versioning

### **🔗 External Integrations**
- **Midtrans** - Payment gateway (15+ methods)
- **Email Service** - Notification system
- **SMS Service** - Alert system

### **🏗️ Infrastructure**
- **Apache/Nginx** - Web server
- **PHP-FPM** - Application server
- **Composer** - Dependency management
- **NPM** - Frontend package management

## 🎯 **Key Architecture Benefits**

### **🔒 Security**
- Multi-layer security (CSRF, XSS, SQL Injection protection)
- Role-based access control
- Secure payment processing
- Audit logging

### **📈 Scalability**
- Modular architecture
- Caching strategy
- Database optimization
- Service-oriented design

### **🔧 Maintainability**
- Clean code structure
- Separation of concerns
- Comprehensive documentation
- Version control

### **🚀 Performance**
- Redis caching
- Database indexing
- Optimized queries
- Asset optimization
