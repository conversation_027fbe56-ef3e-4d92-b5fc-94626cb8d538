<?php

namespace App\Http\Controllers;

use App\Models\Expense;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ExpenseController extends Controller
{
    /**
     * Display a listing of expenses.
     */
    public function index()
    {
        $expenses = Expense::with('user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        $categories = Expense::getCategories();
        
        return view('expense.index', compact('expenses', 'categories'));
    }

    /**
     * Show the form for creating a new expense.
     */
    public function create()
    {
        $categories = Expense::getCategories();
        return view('expense.create', compact('categories'));
    }

    /**
     * Store a newly created expense in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'expense_category' => 'required|string',
            'expense_name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'expense_date' => 'required|date',
            'description' => 'nullable|string',
            'receipt_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048'
        ]);
        
        // Handle file upload
        $receiptPath = null;
        if ($request->hasFile('receipt_image')) {
            $file = $request->file('receipt_image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $receiptPath = $file->storeAs('receipts', $filename, 'public');
        }
        
        // Create expense record
        Expense::create([
            'expense_number' => Expense::generateExpenseNumber(),
            'user_id' => Auth::id(),
            'expense_category' => $validated['expense_category'],
            'expense_name' => $validated['expense_name'],
            'amount' => $validated['amount'],
            'expense_date' => $validated['expense_date'],
            'description' => $validated['description'],
            'receipt_image' => $receiptPath
        ]);
        
        return redirect()->route('expense.index')
            ->with('success', 'Pengeluaran berhasil dicatat');
    }

    /**
     * Display the specified expense.
     */
    public function show(Expense $expense)
    {
        $expense->load('user');
        return view('expense.show', compact('expense'));
    }

    /**
     * Show the form for editing the specified expense.
     */
    public function edit(Expense $expense)
    {
        $categories = Expense::getCategories();
        return view('expense.edit', compact('expense', 'categories'));
    }

    /**
     * Update the specified expense in storage.
     */
    public function update(Request $request, Expense $expense)
    {
        $validated = $request->validate([
            'expense_category' => 'required|string',
            'expense_name' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'expense_date' => 'required|date',
            'description' => 'nullable|string',
            'receipt_image' => 'nullable|image|mimes:jpeg,png,jpg|max:2048'
        ]);
        
        // Handle file upload
        if ($request->hasFile('receipt_image')) {
            // Delete old receipt if exists
            if ($expense->receipt_image) {
                Storage::disk('public')->delete($expense->receipt_image);
            }
            
            $file = $request->file('receipt_image');
            $filename = time() . '_' . $file->getClientOriginalName();
            $receiptPath = $file->storeAs('receipts', $filename, 'public');
            
            $expense->receipt_image = $receiptPath;
        }
        
        $expense->expense_category = $validated['expense_category'];
        $expense->expense_name = $validated['expense_name'];
        $expense->amount = $validated['amount'];
        $expense->expense_date = $validated['expense_date'];
        $expense->description = $validated['description'];
        $expense->save();
        
        return redirect()->route('expense.index')
            ->with('success', 'Pengeluaran berhasil diperbarui');
    }

    /**
     * Remove the specified expense from storage.
     */
    public function destroy(Expense $expense)
    {
        // Delete receipt file if exists
        if ($expense->receipt_image) {
            Storage::disk('public')->delete($expense->receipt_image);
        }
        
        $expense->delete();
        
        return redirect()->route('expense.index')
            ->with('success', 'Pengeluaran berhasil dihapus');
    }
    
    /**
     * Generate expense report
     */
    public function report(Request $request)
    {
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));
        $category = $request->input('category', '');
        
        $query = Expense::whereBetween('expense_date', [$startDate, $endDate]);
        
        if ($category) {
            $query->where('expense_category', $category);
        }
        
        $expenses = $query->with('user')
            ->orderBy('expense_date', 'desc')
            ->get();
            
        $totalExpense = $expenses->sum('amount');
        
        // Get expense data by category for chart
        $categoryData = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->select('expense_category', DB::raw('SUM(amount) as total'))
            ->groupBy('expense_category')
            ->orderBy('total', 'desc')
            ->get();
            
        $categoryLabels = $categoryData->pluck('expense_category')->toArray();
        $categoryTotals = $categoryData->pluck('total')->toArray();
        
        // Get daily expense data for chart
        $dailyData = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->select(DB::raw('DATE(expense_date) as date'), DB::raw('SUM(amount) as total'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $dailyLabels = $dailyData->pluck('date')->toArray();
        $dailyTotals = $dailyData->pluck('total')->toArray();
        
        $categories = Expense::getCategories();
        
        return view('expense.report', compact(
            'expenses', 
            'startDate', 
            'endDate', 
            'category',
            'categories',
            'totalExpense',
            'categoryLabels',
            'categoryTotals',
            'dailyLabels',
            'dailyTotals'
        ));
    }
}
