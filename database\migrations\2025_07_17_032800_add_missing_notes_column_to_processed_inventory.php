<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('processed_inventory', function (Blueprint $table) {
            // Add notes column if it doesn't exist
            if (!Schema::hasColumn('processed_inventory', 'notes')) {
                $table->text('notes')->nullable()->after('is_active');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('processed_inventory', function (Blueprint $table) {
            if (Schema::hasColumn('processed_inventory', 'notes')) {
                $table->dropColumn('notes');
            }
        });
    }
};
