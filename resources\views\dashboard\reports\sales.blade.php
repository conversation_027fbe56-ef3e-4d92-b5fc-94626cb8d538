@extends('layouts.app')

@section('content')
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-chart-line"></i> <PERSON><PERSON><PERSON></h1>
        <div>
            <a href="{{ route('reports.export', ['type' => 'sales', 'start_date' => $startDate, 'end_date' => $endDate]) }}" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Export Excel
            </a>
            <a href="{{ route('dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>Filter Data</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('reports.sales') }}" method="GET" class="row">
                        <div class="col-md-4 mb-3">
                            <label for="start_date" class="form-label">Tanggal Awal</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ $startDate }}">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ $endDate }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-header bg-primary text-white">
                    <h5>Total Penjualan</h5>
                </div>
                <div class="card-body">
                    <h2 class="mb-0">Rp {{ number_format($totalSales, 0, ',', '.') }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-header bg-success text-white">
                    <h5>Jumlah Transaksi</h5>
                </div>
                <div class="card-body">
                    <h2 class="mb-0">{{ $transactions->where('status', 'completed')->count() }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-header bg-danger text-white">
                    <h5>Total Refund</h5>
                </div>
                <div class="card-body">
                    <h2 class="mb-0">Rp {{ number_format($totalRefunds, 0, ',', '.') }}</h2>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Penjualan per Jenis Produk</h5>
                </div>
                <div class="card-body">
                    <canvas id="productSalesChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Detail Produk Terjual</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Jenis Produk</th>
                                <th>Jumlah Terjual</th>
                                <th>Persentase</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $totalQuantity = $productSales->sum('total_quantity');
                            @endphp
                            @foreach($productSales as $product)
                                <tr>
                                    <td>{{ $product->product_type }}</td>
                                    <td>{{ $product->total_quantity }} buah</td>
                                    <td>
                                        @if($totalQuantity > 0)
                                            {{ round(($product->total_quantity / $totalQuantity) * 100) }}%
                                        @else
                                            0%
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5>Data Transaksi</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>No Invoice</th>
                            <th>Tanggal</th>
                            <th>Kasir</th>
                            <th>Metode Pembayaran</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($transactions as $transaction)
                            <tr>
                                <td>{{ $transaction->invoice_number }}</td>
                                <td>{{ $transaction->created_at->format('d/m/Y H:i') }}</td>
                                <td>{{ $transaction->user->name }}</td>
                                <td>
                                    @if($transaction->payment_method == 'cash')
                                        <span class="badge bg-success">Cash</span>
                                    @elseif($transaction->payment_method == 'transfer')
                                        <span class="badge bg-primary">Transfer</span>
                                    @elseif($transaction->payment_method == 'qris')
                                        <span class="badge bg-info">QRIS</span>
                                    @elseif($transaction->payment_method == 'debit')
                                        <span class="badge bg-secondary">Debit</span>
                                    @elseif($transaction->payment_method == 'credit')
                                        <span class="badge bg-warning">Credit</span>
                                    @endif
                                </td>
                                <td>Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</td>
                                <td>
                                    @if($transaction->status == 'completed')
                                        <span class="badge bg-success">Selesai</span>
                                    @elseif($transaction->status == 'cancelled')
                                        <span class="badge bg-danger">Dibatalkan</span>
                                    @elseif($transaction->status == 'refunded')
                                        <span class="badge bg-warning">Refund</span>
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('transactions.show', $transaction->id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('transactions.print', $transaction->id) }}" class="btn btn-sm btn-secondary" target="_blank">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">Tidak ada transaksi dalam periode ini</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const productData = @json($productSales);
        
        const labels = productData.map(item => item.product_type);
        const data = productData.map(item => item.total_quantity);
        const colors = [
            '#8B4513',  // Coklat tua
            '#FF8C00',  // Oranye
            '#4CAF50',  // Hijau
            '#D2691E',  // Coklat medium
            '#FFA500'   // Oranye standard
        ];
        
        const ctx = document.getElementById('productSalesChart').getContext('2d');
        const productSalesChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors.slice(0, labels.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${label}: ${value} buah (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
@endsection 