@extends('layouts.app')

@section('title', 'Rekomendasi Ubi <PERSON>')

@push('styles')
<style>
    .priority-high {
        background-color: rgba(255, 0, 0, 0.1);
        border-left: 4px solid #dc3545;
    }
    
    .priority-medium {
        background-color: rgba(255, 193, 7, 0.1);
        border-left: 4px solid #ffc107;
    }
    
    .priority-low {
        background-color: rgba(40, 167, 69, 0.1);
        border-left: 4px solid #28a745;
    }
    
    .stat-card {
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }
    
    .stat-card.high-priority {
        background: linear-gradient(135deg, #fff5f5 0%, #ffe0e0 100%);
        border-left: 5px solid #dc3545;
    }
    
    .stat-card.medium-priority {
        background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
        border-left: 5px solid #ffc107;
    }
    
    .stat-card.low-priority {
        background: linear-gradient(135deg, #f0fff4 0%, #d4edda 100%);
        border-left: 5px solid #28a745;
    }
    
    .stat-card.total-loss {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-left: 5px solid #6c757d;
    }
    
    .btn-set-market {
        white-space: nowrap;
    }
    
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .badge-priority {
        padding: 5px 10px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 12px;
    }
    
    .badge-high {
        background-color: #dc3545;
        color: white;
    }
    
    .badge-medium {
        background-color: #ffc107;
        color: #212529;
    }
    
    .badge-low {
        background-color: #28a745;
        color: white;
    }
    
    .days-indicator {
        font-weight: bold;
    }
    
    .days-indicator.danger {
        color: #dc3545;
    }
    
    .days-indicator.warning {
        color: #ffc107;
    }
    
    .days-indicator.success {
        color: #28a745;
    }
</style>
@endpush

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Rekomendasi Ubi Matang untuk Segera Dijual</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item active">Rekomendasi Ubi Matang</li>
    </ol>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-info-circle me-1"></i>
                    Tentang Fitur Ini
                </div>
                <div class="card-body">
                    <p>Fitur ini membantu Anda mengidentifikasi stok ubi matang yang perlu segera dijual berdasarkan tanggal kadaluarsa. Ubi akan dikategorikan berdasarkan prioritas:</p>
                    <ul>
                        <li><strong>Prioritas Tinggi:</strong> Ubi yang akan kadaluarsa dalam 3 hari atau kurang</li>
                        <li><strong>Prioritas Sedang:</strong> Ubi yang akan kadaluarsa dalam 4-7 hari</li>
                        <li><strong>Prioritas Rendah:</strong> Ubi yang akan kadaluarsa dalam lebih dari 7 hari</li>
                    </ul>
                    <p>Sistem juga memberikan rekomendasi pasar untuk distribusi berdasarkan data historis transaksi.</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stat-card high-priority">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-0">Prioritas Tinggi</h6>
                        <h2 class="my-2">{{ $highPriorityCount }}</h2>
                        <p class="mb-0 text-danger"><i class="fas fa-exclamation-triangle me-1"></i> Segera dijual!</p>
                    </div>
                    <div class="icon-bg rounded-circle d-flex align-items-center justify-content-center">
                        <i class="fas fa-fire-alt fa-2x text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card medium-priority">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-0">Prioritas Sedang</h6>
                        <h2 class="my-2">{{ $mediumPriorityCount }}</h2>
                        <p class="mb-0 text-warning"><i class="fas fa-clock me-1"></i> Pantau</p>
                    </div>
                    <div class="icon-bg rounded-circle d-flex align-items-center justify-content-center">
                        <i class="fas fa-hourglass-half fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card low-priority">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-0">Prioritas Rendah</h6>
                        <h2 class="my-2">{{ $lowPriorityCount }}</h2>
                        <p class="mb-0 text-success"><i class="fas fa-check-circle me-1"></i> Aman</p>
                    </div>
                    <div class="icon-bg rounded-circle d-flex align-items-center justify-content-center">
                        <i class="fas fa-leaf fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="stat-card total-loss">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-0">Potensi Kerugian</h6>
                        <h2 class="my-2">Rp {{ number_format($totalPotentialLoss, 0, ',', '.') }}</h2>
                        <p class="mb-0 text-secondary"><i class="fas fa-chart-line me-1"></i> Prioritas Tinggi</p>
                    </div>
                    <div class="icon-bg rounded-circle d-flex align-items-center justify-content-center">
                        <i class="fas fa-money-bill-wave fa-2x text-secondary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-table me-1"></i>
                Daftar Ubi Matang yang Perlu Segera Dijual
            </div>
            <div class="d-flex">
                <a href="{{ route('expiry-recommendations.sales-report') }}" class="btn btn-sm btn-info me-2">
                    <i class="fas fa-chart-bar me-1"></i> Laporan Penjualan
                </a>
                <a href="{{ route('expiry-recommendations.update-all') }}" class="btn btn-sm btn-primary me-2">
                    <i class="fas fa-sync-alt me-1"></i> Perbarui Semua
                </a>
                <div class="dropdown">
                    <button class="btn btn-sm btn-success dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-file-export me-1"></i> Export
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                        <li><a class="dropdown-item" href="{{ route('expiry-recommendations.export-excel') }}"><i class="fas fa-file-excel me-1"></i> Excel</a></li>
                        <li><a class="dropdown-item" href="{{ route('expiry-recommendations.export-pdf') }}"><i class="fas fa-file-pdf me-1"></i> PDF</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Nama Produk</th>
                            <th>Batch</th>
                            <th>Tgl Produksi</th>
                            <th>Tgl Kadaluarsa</th>
                            <th>Sisa Hari</th>
                            <th>Stok</th>
                            <th>Nilai Total</th>
                            <th>Prioritas</th>
                            <th>Pasar Rekomendasi</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($recommendedItems as $item)
                            @php
                                $priorityClass = '';
                                $badgeClass = '';
                                $daysClass = '';
                                
                                if ($item->priority_level === 'Tinggi') {
                                    $priorityClass = 'priority-high';
                                    $badgeClass = 'badge-high';
                                    $daysClass = 'danger';
                                } elseif ($item->priority_level === 'Sedang') {
                                    $priorityClass = 'priority-medium';
                                    $badgeClass = 'badge-medium';
                                    $daysClass = 'warning';
                                } else {
                                    $priorityClass = 'priority-low';
                                    $badgeClass = 'badge-low';
                                    $daysClass = 'success';
                                }
                                
                                $totalValue = $item->current_stock * $item->cost_per_unit;
                            @endphp
                            <tr class="{{ $priorityClass }}">
                                <td>
                                    <strong>{{ $item->name }}</strong><br>
                                    <small class="text-muted">{{ $item->product_type }}</small>
                                </td>
                                <td>{{ $item->batch_number }}</td>
                                <td>{{ $item->production_date ? $item->production_date->format('d/m/Y') : '-' }}</td>
                                <td>{{ $item->expiry_date ? $item->expiry_date->format('d/m/Y') : '-' }}</td>
                                <td>
                                    <span class="days-indicator {{ $daysClass }}">
                                        {{ $item->days_until_expiry >= 0 ? $item->days_until_expiry : 'Kadaluarsa' }}
                                    </span>
                                </td>
                                <td>{{ $item->current_stock }}</td>
                                <td>Rp {{ number_format($totalValue, 0, ',', '.') }}</td>
                                <td>
                                    <span class="badge badge-priority {{ $badgeClass }}">
                                        {{ $item->priority_level }}
                                    </span>
                                </td>
                                <td>
                                    @if($item->recommended_market)
                                        <span class="text-primary">
                                            <i class="fas fa-store me-1"></i> {{ $item->recommended_market }}
                                        </span>
                                    @else
                                        <span class="text-muted">Belum ada rekomendasi</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <a href="{{ route('expiry-recommendations.update', $item->id) }}" class="btn btn-sm btn-info me-1" title="Perbarui Status">
                                            <i class="fas fa-sync-alt"></i>
                                        </a>

                                        <button type="button"
                                            class="btn btn-sm btn-primary btn-set-market"
                                            data-bs-toggle="modal"
                                            data-bs-target="#setMarketModal"
                                            data-item-id="{{ $item->id }}"
                                            data-item-name="{{ $item->name }}"
                                            data-batch-number="{{ $item->batch_number }}"
                                            data-expiry-date="{{ $item->expiry_date ? $item->expiry_date->format('d/m/Y') : '-' }}"
                                            data-stock="{{ $item->current_stock }}"
                                            title="Distribusi ke Pasar">
                                            <i class="fas fa-truck-loading me-1"></i> Distribusi
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="text-center">Tidak ada data rekomendasi ubi matang.</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal for setting market and quantity -->
<div class="modal fade" id="setMarketModal" tabindex="-1" aria-labelledby="setMarketModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="setMarketModalLabel">Buat Distribusi Produk</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="setMarketForm" action="" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="itemName" class="form-label">Nama Produk</label>
                                <input type="text" class="form-control" id="itemName" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label for="batch_number" class="form-label">Nomor Batch</label>
                                <input type="text" class="form-control" id="batch_number" name="batch_number" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">Tanggal Kadaluarsa</label>
                                <input type="text" class="form-control" id="expiry_date" readonly>
                            </div>
                            
                            <div class="mb-3">
                                <label for="current_stock" class="form-label">Stok Tersedia</label>
                                <input type="text" class="form-control" id="current_stock" readonly>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> Masukkan detail distribusi produk
                            </div>
                            
                            <div class="mb-3">
                                <label for="destination" class="form-label">Tujuan Distribusi <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="destination" name="destination"
                                       list="market-suggestions" required placeholder="Masukkan tujuan distribusi">
                                <datalist id="market-suggestions">
                                    @foreach($markets as $market)
                                        <option value="{{ $market }}">
                                    @endforeach
                                </datalist>
                            </div>


                            
                            <div class="mb-3">
                                <label for="quantity" class="form-label">Jumlah yang Akan Didistribusikan <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                                    <span class="input-group-text">unit</span>
                                </div>
                                <div class="form-text" id="stock_warning"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="distribution_date" class="form-label">Tanggal Distribusi <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="distribution_date" name="distribution_date" value="{{ date('Y-m-d') }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-label">Catatan</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2" placeholder="Catatan tambahan (opsional)"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary" id="saveDistributionBtn">
                        <i class="fas fa-truck me-1"></i> Buat Distribusi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set Market Modal
        const setMarketModal = document.getElementById('setMarketModal');
        if (setMarketModal) {
            
            // Handle quantity validation
            const quantityInput = document.getElementById('quantity');
            const currentStockInput = document.getElementById('current_stock');
            const stockWarning = document.getElementById('stock_warning');
            const saveButton = document.getElementById('saveDistributionBtn');
            
            if (quantityInput && currentStockInput) {
                quantityInput.addEventListener('input', function() {
                    const quantity = parseInt(this.value) || 0;
                    const currentStock = parseInt(currentStockInput.value) || 0;
                    
                    if (quantity > currentStock) {
                        stockWarning.textContent = 'Jumlah melebihi stok tersedia!';
                        stockWarning.className = 'form-text text-danger';
                        saveButton.disabled = true;
                    } else if (quantity <= 0) {
                        stockWarning.textContent = 'Jumlah harus lebih dari 0!';
                        stockWarning.className = 'form-text text-danger';
                        saveButton.disabled = true;
                    } else {
                        stockWarning.textContent = 'Jumlah valid.';
                        stockWarning.className = 'form-text text-success';
                        saveButton.disabled = false;
                    }
                });
            }
            
            // Modal show event
            setMarketModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const itemId = button.getAttribute('data-item-id');
                const itemName = button.getAttribute('data-item-name');
                const batchNumber = button.getAttribute('data-batch-number');
                const expiryDate = button.getAttribute('data-expiry-date');
                const currentStock = button.getAttribute('data-stock');
                
                const form = this.querySelector('#setMarketForm');
                const itemNameInput = this.querySelector('#itemName');
                const batchInput = this.querySelector('#batch_number');
                const expiryInput = this.querySelector('#expiry_date');
                const stockInput = this.querySelector('#current_stock');
                
                // Reset form
                form.reset();
                stockWarning.textContent = '';
                saveButton.disabled = false;
                
                // Set values
                itemNameInput.value = itemName;
                batchInput.value = batchNumber;
                expiryInput.value = expiryDate;
                stockInput.value = currentStock;
                
                // Set form action untuk membuat distribusi
                form.action = `{{ url('expiry-recommendations/create-distribution') }}/${itemId}`;
            });
        }
    });
</script>
@endpush
