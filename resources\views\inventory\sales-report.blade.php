@extends('layouts.app')

@section('title', 'Laporan Distribusi Ubi ke Pasar')

@push('styles')
<style>
    .simple-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .summary-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 25px;
        text-align: center;
        margin-bottom: 20px;
    }

    .summary-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 10px 0;
    }

    .summary-label {
        font-size: 1.1rem;
        opacity: 0.9;
    }
</style>
@endpush

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-4">Laporan Distribusi Ubi ke Pasar</h1>
        <a href="{{ route('expiry-recommendations.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> <PERSON><PERSON><PERSON>
        </a>
    </div>

    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ route('expiry-recommendations.index') }}">Rekomendasi Ubi</a></li>
        <li class="breadcrumb-item active">Laporan Distribusi</li>
    </ol>

    <!-- Filter Section -->
    <div class="simple-card">
        <h5 class="mb-3">Filter Laporan</h5>
        <form method="GET" action="{{ route('expiry-recommendations.sales-report') }}">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Tanggal Mulai</label>
                    <input type="date" class="form-control" id="start_date" name="start_date"
                           value="{{ request('start_date', now()->startOfMonth()->format('Y-m-d')) }}">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">Tanggal Akhir</label>
                    <input type="date" class="form-control" id="end_date" name="end_date"
                           value="{{ request('end_date', now()->format('Y-m-d')) }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i> Tampilkan
                    </button>
                    <a href="{{ route('expiry-recommendations.sales-report') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh me-1"></i> Reset
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Summary Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="summary-box">
                <div class="summary-label">Total Ubi Didistribusikan</div>
                <div class="summary-number">{{ number_format($totalQuantitySold) }}</div>
                <div class="summary-label">Unit</div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="summary-box" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="summary-label">Jumlah Pasar Tujuan</div>
                <div class="summary-number">{{ $totalMarkets }}</div>
                <div class="summary-label">Pasar</div>
            </div>
        </div>
    </div>

    <!-- Simple Distribution List -->
    <div class="simple-card">
        <h5 class="mb-3"><i class="fas fa-list me-2"></i> Daftar Distribusi Ubi ke Pasar</h5>

        @if($distributions->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>Tanggal</th>
                            <th>Pasar Tujuan</th>
                            <th>Produk Ubi</th>
                            <th>Jumlah</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($distributions as $distribution)
                            @foreach($distribution->items as $item)
                                <tr>
                                    <td>{{ $distribution->distribution_date->format('d/m/Y') }}</td>
                                    <td>
                                        <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                        <strong>{{ $distribution->destination }}</strong>
                                    </td>
                                    <td>{{ $item->processedInventory->name ?? ($item->otherProduct->name ?? 'Ubi Bakar') }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ number_format($item->quantity) }} unit</span>
                                    </td>
                                    <td>
                                        @if($distribution->status === 'delivered')
                                            <span class="badge bg-success">✓ Selesai</span>
                                        @elseif($distribution->status === 'in_transit')
                                            <span class="badge bg-warning">🚚 Dalam Perjalanan</span>
                                        @elseif($distribution->status === 'returned')
                                            <span class="badge bg-danger">↩️ Dikembalikan</span>
                                        @else
                                            <span class="badge bg-secondary">📋 Direncanakan</span>
                                        @endif

                                        @if($distribution->is_urgent ?? false)
                                            <span class="badge bg-danger ms-1">⚡ URGENT</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada data distribusi</h5>
                <p class="text-muted">Belum ada distribusi ubi dalam periode yang dipilih.</p>
            </div>
        @endif
    </div>

    <!-- Summary by Market -->
    @if($marketData && $marketData->count() > 0)
    <div class="simple-card">
        <h5 class="mb-3"><i class="fas fa-chart-bar me-2"></i> Ringkasan per Pasar</h5>
        <div class="row">
            @foreach($marketData as $market)
                <div class="col-md-4 mb-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h6 class="card-title">{{ $market->market }}</h6>
                            <h4 class="text-primary">{{ number_format($market->total_quantity) }}</h4>
                            <small class="text-muted">unit ubi terdistribusi</small>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
    @endif
</div>
@endsection
