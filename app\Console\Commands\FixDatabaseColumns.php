<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FixDatabaseColumns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:fix-columns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Memperbaiki kolom yang hilang pada tabel database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Memperbaiki kolom-kolom yang hilang pada tabel...');

        // Memperbaiki kolom raw_material_per_item pada tabel processed_inventory
        if (!$this->columnExists('processed_inventory', 'raw_material_per_item')) {
            $this->info('Menambahkan kolom raw_material_per_item ke tabel processed_inventory');
            DB::statement('ALTER TABLE processed_inventory ADD COLUMN raw_material_per_item DECIMAL(10,2) NULL AFTER selling_price');
            $this->info('Kolom raw_material_per_item berhasil ditambahkan');
        } else {
            $this->info('Kolom raw_material_per_item sudah ada pada tabel processed_inventory');
        }

        // Memperbaiki kolom notes pada tabel raw_inventory
        if (!$this->columnExists('raw_inventory', 'notes')) {
            $this->info('Menambahkan kolom notes ke tabel raw_inventory');
            DB::statement('ALTER TABLE raw_inventory ADD COLUMN notes TEXT NULL AFTER quality');
            $this->info('Kolom notes berhasil ditambahkan pada tabel raw_inventory');
        } else {
            $this->info('Kolom notes sudah ada pada tabel raw_inventory');
        }

        // Memperbaiki kolom notes pada tabel other_products
        if (!$this->columnExists('other_products', 'notes')) {
            $this->info('Menambahkan kolom notes ke tabel other_products');
            DB::statement('ALTER TABLE other_products ADD COLUMN notes TEXT NULL AFTER is_active');
            $this->info('Kolom notes berhasil ditambahkan pada tabel other_products');
        } else {
            $this->info('Kolom notes sudah ada pada tabel other_products');
        }

        $this->info('Proses perbaikan kolom database selesai!');
        return Command::SUCCESS;
    }

    /**
     * Cek apakah kolom ada pada tabel
     * 
     * @param string $table
     * @param string $column
     * @return bool
     */
    private function columnExists($table, $column)
    {
        return Schema::hasColumn($table, $column);
    }
}
