<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class KaryawanLoginController extends Controller
{
    public function showLoginForm()
    {
        return view('auth.karyawan-login');
    }
    
    public function login(Request $request)
    {
        // Validasi input
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);
        
        // Cari user berdasarkan email saja
        $user = User::where('email', $request->email)->first();
        
        if (!$user) {
            return back()->withErrors([
                'email' => 'Email tidak ditemukan di database.'
            ])->withInput($request->only('email'));
        }
        
        // Determine valid roles from database
        $roleInfo = DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
        $validRoles = [];
        if (!empty($roleInfo)) {
            $roleType = $roleInfo[0]->Type;
            if (strpos($roleType, 'enum') !== false) {
                preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
                if (!empty($matches[1])) {
                    $validRoles = explode("','", $matches[1]);
                }
            }
        }
        
        // Check if user is karyawan (could be either 'employee' or 'karyawan' depending on DB schema)
        $isKaryawan = false;
        
        if ($user->role === 'employee' || $user->role === 'karyawan') {
            $isKaryawan = true;
        }
        
        if (!$isKaryawan) {
            return back()->withErrors([
                'email' => 'Akun ini bukan akun karyawan.'
            ])->withInput($request->only('email'));
        }
        
        // Cek password
        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors([
                'password' => 'Password salah.'
            ])->withInput($request->only('email'));
        }
        
        // Login berhasil
        Auth::login($user, $request->has('remember'));
        
        // Redirect ke halaman yang sesuai
        return redirect()->route('transactions.index');
    }
} 