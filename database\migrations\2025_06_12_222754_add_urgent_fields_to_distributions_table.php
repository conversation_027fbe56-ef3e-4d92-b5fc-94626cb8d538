<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('distributions', function (Blueprint $table) {
            $table->string('destination')->nullable()->after('user_id');
            $table->string('vehicle_info')->nullable()->after('distribution_date');
            $table->string('driver_name')->nullable()->after('vehicle_info');
            $table->boolean('is_urgent')->default(false)->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('distributions', function (Blueprint $table) {
            $table->dropColumn(['destination', 'vehicle_info', 'driver_name', 'is_urgent']);
        });
    }
};
