<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateKaryawanUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:create-karyawan';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new karyawan user for testing';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Creating new karyawan user...');

        // Hapus user karyawan lama jika email sudah ada
        $existingUser = User::where('email', '<EMAIL>')->first();
        if ($existingUser) {
            $existingUser->delete();
            $this->info('Deleted existing user <NAME_EMAIL>');
        }

        // Buat user karyawan baru
        $user = new User();
        $user->name = 'Karyawan <PERSON>ko';
        $user->email = '<EMAIL>';
        $user->password = Hash::make('karyawan123');
        $user->role = 'karyawan';
        $user->save();

        $this->info('New karyawan user created successfully!');
        $this->info('Email: <EMAIL>');
        $this->info('Password: karyawan123');
        $this->info('Role: karyawan');
    }
} 