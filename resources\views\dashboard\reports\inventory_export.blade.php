<!DOCTYPE html>
<html>
<head>
    <title>Laporan Inventori - Ubi Bakar Cilembu</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin-bottom: 5px;
        }
        .header p {
            color: #666;
        }
        .section-title {
            margin-top: 20px;
            margin-bottom: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Laporan Inventori - Ubi Bakar Cilembu</h1>
        <p>Tanggal Cetak: {{ date('d/m/Y H:i') }}</p>
    </div>
    
    <div class="section-title">RINGKASAN INVENTORI</div>
    <table>
        <tr>
            <th><PERSON><PERSON>ori</th>
            <th>Jumlah Item</th>
            <th>Total Stok</th>
            <th>Nilai Total (Rp)</th>
        </tr>
        <tr>
            <td>Ubi Mentah</td>
            <td>{{ count($rawInventory) }}</td>
            <td>{{ $rawInventory->sum('current_stock') }} kg</td>
            <td>{{ number_format($rawInventory->sum(function($item) { return $item->current_stock * $item->cost_per_kg; }), 0, ',', '.') }}</td>
        </tr>
        <tr>
            <td>Ubi Matang / Produk Jadi</td>
            <td>{{ count($processedInventory) }}</td>
            <td>{{ $processedInventory->sum('current_stock') }} pcs</td>
            <td>{{ number_format($processedInventory->sum(function($item) { return $item->current_stock * $item->selling_price; }), 0, ',', '.') }}</td>
        </tr>
    </table>
    
    <div class="section-title">INVENTORI UBI MENTAH</div>
    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>Nama</th>
                <th>Batch Number</th>
                <th>Supplier</th>
                <th>Stok (kg)</th>
                <th>Harga/kg (Rp)</th>
                <th>Nilai (Rp)</th>
                <th>Tgl Pembelian</th>
                <th>Tgl Kadaluarsa</th>
            </tr>
        </thead>
        <tbody>
            @forelse($rawInventory as $index => $item)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>{{ $item->name }}</td>
                <td>{{ $item->batch_number ?? '-' }}</td>
                <td>{{ $item->supplier ?? '-' }}</td>
                <td>{{ number_format($item->current_stock, 2, ',', '.') }}</td>
                <td>{{ number_format($item->cost_per_kg, 0, ',', '.') }}</td>
                <td>{{ number_format($item->current_stock * $item->cost_per_kg, 0, ',', '.') }}</td>
                <td>{{ $item->purchase_date ? $item->purchase_date->format('d/m/Y') : '-' }}</td>
                <td>{{ $item->expiry_date ? $item->expiry_date->format('d/m/Y') : '-' }}</td>
            </tr>
            @empty
            <tr>
                <td colspan="9" style="text-align: center;">Tidak ada data ubi mentah</td>
            </tr>
            @endforelse
        </tbody>
    </table>
    
    <div class="section-title">INVENTORI UBI MATANG / PRODUK JADI</div>
    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>Nama</th>
                <th>Batch Number</th>
                <th>Tipe</th>
                <th>Stok (pcs)</th>
                <th>Biaya/Item (Rp)</th>
                <th>Harga Jual (Rp)</th>
                <th>Nilai (Rp)</th>
                <th>Tgl Produksi</th>
                <th>Tgl Kadaluarsa</th>
            </tr>
        </thead>
        <tbody>
            @forelse($processedInventory as $index => $item)
            <tr>
                <td>{{ $index + 1 }}</td>
                <td>{{ $item->name }}</td>
                <td>{{ $item->batch_number ?? '-' }}</td>
                <td>{{ $item->product_type ?? '-' }}</td>
                <td>{{ number_format($item->current_stock, 0, ',', '.') }}</td>
                <td>{{ $item->cost_per_item ? number_format($item->cost_per_item, 0, ',', '.') : '-' }}</td>
                <td>{{ number_format($item->selling_price, 0, ',', '.') }}</td>
                <td>{{ number_format($item->current_stock * $item->selling_price, 0, ',', '.') }}</td>
                <td>{{ $item->production_date ? $item->production_date->format('d/m/Y') : '-' }}</td>
                <td>{{ $item->expiry_date ? $item->expiry_date->format('d/m/Y') : '-' }}</td>
            </tr>
            @empty
            <tr>
                <td colspan="10" style="text-align: center;">Tidak ada data produk jadi</td>
            </tr>
            @endforelse
        </tbody>
    </table>
</body>
</html> 