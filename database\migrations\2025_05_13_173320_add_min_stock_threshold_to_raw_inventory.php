<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('raw_inventory', function (Blueprint $table) {
            $table->decimal('min_stock_threshold', 8, 2)->nullable()->default(10);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('raw_inventory', function (Blueprint $table) {
            $table->dropColumn('min_stock_threshold');
        });
    }
};
