<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UserManagementController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::query();

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by role
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Search by name or email
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $users = $query->with('approver')
                      ->orderBy('created_at', 'desc')
                      ->paginate(15);

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        return view('admin.users.create');
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,employee,cashier,warehouse',
            'status' => 'required|in:pending,approved,rejected',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
            'status' => $request->status,
            'approved_by' => $request->status === 'approved' ? Auth::id() : null,
            'approved_at' => $request->status === 'approved' ? now() : null,
        ]);

        return redirect()->route('admin.users.index')
                        ->with('success', 'User berhasil dibuat.');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load('approver', 'approvedUsers');
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'role' => 'required|in:admin,employee,cashier,warehouse',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
        ]);

        // Update password if provided
        if ($request->filled('password')) {
            $user->update(['password' => Hash::make($request->password)]);
        }

        return redirect()->route('admin.users.index')
                        ->with('success', 'User berhasil diperbarui.');
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user)
    {
        // Prevent deleting own account
        if ($user->id === Auth::id()) {
            return redirect()->route('admin.users.index')
                            ->with('error', 'Anda tidak dapat menghapus akun sendiri.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
                        ->with('success', 'User berhasil dihapus.');
    }

    /**
     * Approve a user.
     */
    public function approve(User $user)
    {
        if ($user->approve(Auth::user())) {
            return redirect()->route('admin.users.index')
                            ->with('success', "User {$user->name} berhasil disetujui.");
        }

        return redirect()->route('admin.users.index')
                        ->with('error', 'Gagal menyetujui user.');
    }

    /**
     * Reject a user.
     */
    public function reject(User $user)
    {
        if ($user->reject(Auth::user())) {
            return redirect()->route('admin.users.index')
                            ->with('success', "User {$user->name} berhasil ditolak.");
        }

        return redirect()->route('admin.users.index')
                        ->with('error', 'Gagal menolak user.');
    }

    /**
     * Bulk approve users.
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id'
        ]);

        $users = User::whereIn('id', $request->user_ids)->get();
        $approvedCount = 0;

        foreach ($users as $user) {
            if ($user->approve(Auth::user())) {
                $approvedCount++;
            }
        }

        return redirect()->route('admin.users.index')
                        ->with('success', "{$approvedCount} user berhasil disetujui.");
    }

    /**
     * Bulk reject users.
     */
    public function bulkReject(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id'
        ]);

        $users = User::whereIn('id', $request->user_ids)->get();
        $rejectedCount = 0;

        foreach ($users as $user) {
            if ($user->reject(Auth::user())) {
                $rejectedCount++;
            }
        }

        return redirect()->route('admin.users.index')
                        ->with('success', "{$rejectedCount} user berhasil ditolak.");
    }
}
