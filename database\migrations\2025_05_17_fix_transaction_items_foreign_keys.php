<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Langkah 0: Nonaktifkan foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Langkah 1: Periksa apakah kolom product_id mengizinkan NULL
        $columnInfo = DB::select("SHOW COLUMNS FROM transaction_items WHERE Field = 'product_id'");
        $isNullable = $columnInfo[0]->Null === 'YES';

        if (!$isNullable) {
            // Ubah kolom product_id agar mengizinkan NULL
            DB::statement('ALTER TABLE transaction_items MODIFY product_id BIGINT UNSIGNED NULL');
        }

        // Langkah 2: Periksa apakah kolom processed_inventory_id mengizinkan NULL
        $columnInfo = DB::select("SHOW COLUMNS FROM transaction_items WHERE Field = 'processed_inventory_id'");
        $isNullable = isset($columnInfo[0]) && $columnInfo[0]->Null === 'YES';

        if (!$isNullable && isset($columnInfo[0])) {
            // Ubah kolom processed_inventory_id agar mengizinkan NULL
            DB::statement('ALTER TABLE transaction_items MODIFY processed_inventory_id BIGINT UNSIGNED NULL');
        }

        // Langkah 2: Hapus foreign key yang ada
        try {
            Schema::table('transaction_items', function (Blueprint $table) {
                // Cek apakah foreign key ada sebelum mencoba menghapusnya
                $foreignKeys = DB::select("
                    SELECT CONSTRAINT_NAME
                    FROM information_schema.TABLE_CONSTRAINTS
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'transaction_items'
                    AND CONSTRAINT_TYPE = 'FOREIGN KEY'
                ");

                $constraints = collect($foreignKeys)->pluck('CONSTRAINT_NAME')->toArray();

                if (in_array('transaction_items_processed_inventory_id_foreign', $constraints)) {
                    $table->dropForeign('transaction_items_processed_inventory_id_foreign');
                }

                if (in_array('transaction_items_product_id_foreign', $constraints)) {
                    $table->dropForeign('transaction_items_product_id_foreign');
                }
            });
        } catch (\Exception $e) {
            // Jika gagal menghapus foreign key, lanjutkan saja
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        }

        // Langkah 3: Perbaiki data yang tidak valid
        // Cari transaction_items dengan processed_inventory_id yang tidak valid
        $invalidItems = DB::table('transaction_items')
            ->leftJoin('processed_inventory', 'transaction_items.processed_inventory_id', '=', 'processed_inventory.id')
            ->whereNotNull('transaction_items.processed_inventory_id')
            ->whereNull('processed_inventory.id')
            ->select('transaction_items.id')
            ->get();

        // Set processed_inventory_id menjadi NULL untuk item yang tidak valid
        foreach ($invalidItems as $item) {
            DB::table('transaction_items')
                ->where('id', $item->id)
                ->update(['processed_inventory_id' => null]);
        }

        // Cari transaction_items dengan product_id yang tidak valid
        $invalidItems = DB::table('transaction_items')
            ->leftJoin('other_products', 'transaction_items.product_id', '=', 'other_products.id')
            ->whereNotNull('transaction_items.product_id')
            ->whereNull('other_products.id')
            ->select('transaction_items.id')
            ->get();

        // Set product_id menjadi NULL untuk item yang tidak valid
        foreach ($invalidItems as $item) {
            DB::table('transaction_items')
                ->where('id', $item->id)
                ->update(['product_id' => null]);
        }

        // Langkah 4: Tambahkan foreign key yang benar
        try {
            Schema::table('transaction_items', function (Blueprint $table) {
                // Tambahkan foreign key untuk processed_inventory_id
                if (Schema::hasColumn('transaction_items', 'processed_inventory_id')) {
                    $table->foreign('processed_inventory_id')
                        ->references('id')
                        ->on('processed_inventory')
                        ->onDelete('set null');
                }

                // Tambahkan foreign key untuk product_id
                if (Schema::hasColumn('transaction_items', 'product_id')) {
                    $table->foreign('product_id')
                        ->references('id')
                        ->on('other_products')
                        ->onDelete('set null');
                }
            });
        } catch (\Exception $e) {
            // Jika gagal menambahkan foreign key, log error
            \Log::error('Gagal menambahkan foreign key: ' . $e->getMessage());
        } finally {
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Tidak perlu melakukan apa-apa di down method
        // karena kita tidak ingin mengembalikan ke keadaan yang bermasalah
    }
};
