<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Auditable;

class ProductionProcess extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'batch_number',
        'user_id',
        'raw_inventory_id',
        'raw_quantity_used',
        'processed_quantity_produced',
        'production_cost',
        'production_date',
        'status',
        'notes'
    ];

    protected $casts = [
        'production_date' => 'date',
        'raw_quantity_used' => 'decimal:2',
        'production_cost' => 'decimal:2',
    ];

    /**
     * Generate a unique batch number
     * Format: PROD-YYYYMMDD-XXXX
     */
    public static function generateBatchNumber()
    {
        $date = now()->format('Ymd');
        $latestProcess = static::whereDate('created_at', now()->format('Y-m-d'))
            ->latest()
            ->first();

        $sequence = $latestProcess ? (int)substr($latestProcess->batch_number, -4) + 1 : 1;
        return 'PROD-' . $date . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the raw inventory used in this production process.
     */
    public function rawInventory(): BelongsTo
    {
        return $this->belongsTo(RawInventory::class);
    }

    /**
     * Get the user who created this production process.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the processed inventory items created from this production process.
     */
    public function processedInventory(): HasMany
    {
        return $this->hasMany(ProcessedInventory::class);
    }

    /**
     * Calculate the cost per unit produced.
     */
    public function getCostPerUnitAttribute()
    {
        if ($this->processed_quantity_produced > 0) {
            return $this->production_cost / $this->processed_quantity_produced;
        }
        
        return 0;
    }

    /**
     * Get the status badge HTML.
     */
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'planned' => 'bg-info',
            'in_progress' => 'bg-warning',
            'completed' => 'bg-success',
            'failed' => 'bg-danger'
        ];

        $class = $badges[$this->status] ?? 'bg-secondary';
        return '<span class="badge ' . $class . '">' . ucfirst($this->status) . '</span>';
    }
}
