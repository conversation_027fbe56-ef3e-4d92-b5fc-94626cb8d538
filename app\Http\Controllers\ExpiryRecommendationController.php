<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ProcessedInventory;
use App\Models\Distribution;
use App\Exports\ExpiryRecommendationExport;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class ExpiryRecommendationController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the recommendations.
     */
    public function index()
    {
        // Update expiry tracking data for all processed inventory items
        $recommendedItems = ProcessedInventory::getExpiryTrackingList();
        
        // Summary stats
        $highPriorityCount = $recommendedItems->where('priority_level', 'Tinggi')->count();
        $mediumPriorityCount = $recommendedItems->where('priority_level', 'Sedang')->count();
        $lowPriorityCount = $recommendedItems->where('priority_level', 'Rendah')->count();
        $totalPotentialLoss = $recommendedItems->where('priority_level', 'Tinggi')
                                               ->sum(function($item) {
                                                   return $item->current_stock * $item->cost_per_unit;
                                               });
        
        // Get available markets for distribution
        $markets = Distribution::select('market_name')
                               ->distinct()
                               ->orderBy('market_name')
                               ->pluck('market_name');
        
        return view('inventory.expiry-recommendations', compact(
            'recommendedItems',
            'highPriorityCount',
            'mediumPriorityCount',
            'lowPriorityCount',
            'totalPotentialLoss',
            'markets'
        ));
    }
    
    /**
     * Update recommendation for a specific item
     */
    public function updateRecommendation($id)
    {
        $item = ProcessedInventory::findOrFail($id);
        $item->updateExpiryTracking();
        
        return redirect()->back()->with('success', 'Rekomendasi berhasil diperbarui');
    }
    
    /**
     * Update all recommendations
     */
    public function updateAllRecommendations()
    {
        $items = ProcessedInventory::where('is_active', true)
                                   ->where('current_stock', '>', 0)
                                   ->where('expiry_date', '!=', null)
                                   ->get();
                                   
        foreach($items as $item) {
            $item->updateExpiryTracking();
        }
        
        return redirect()->back()->with('success', 'Semua rekomendasi berhasil diperbarui');
    }
    
    /**
     * Export recommendations to Excel
     */
    public function exportExcel()
    {
        $recommendations = ProcessedInventory::where('is_active', true)
            ->where('current_stock', '>', 0)
            ->whereRaw('expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY)')
            ->orderBy('expiry_date', 'asc')
            ->get();

        $filename = 'expiry_recommendations_' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($recommendations) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");

            // Add headers
            fputcsv($file, [
                'Nama Produk',
                'Batch Number',
                'Stok Saat Ini',
                'Tanggal Kadaluarsa',
                'Hari Tersisa',
                'Status Prioritas',
                'Rekomendasi'
            ]);

            // Add data
            foreach ($recommendations as $item) {
                $daysLeft = \Carbon\Carbon::parse($item->expiry_date)->diffInDays(now(), false);
                $priority = $daysLeft <= 1 ? 'URGENT' : ($daysLeft <= 3 ? 'HIGH' : 'MEDIUM');
                $recommendation = $daysLeft <= 1 ? 'Distribusi segera atau diskon besar' :
                                ($daysLeft <= 3 ? 'Distribusi prioritas atau promo' : 'Monitor dan rencanakan distribusi');

                fputcsv($file, [
                    $item->name,
                    $item->batch_number,
                    $item->current_stock,
                    \Carbon\Carbon::parse($item->expiry_date)->format('d/m/Y'),
                    $daysLeft . ' hari',
                    $priority,
                    $recommendation
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
    
    /**
     * Export recommendations to PDF
     */
    public function exportPdf()
    {
        $recommendedItems = ProcessedInventory::getExpiryTrackingList();
        
        $pdf = PDF::loadView('exports.expiry-recommendations-pdf', [
            'recommendedItems' => $recommendedItems,
            'date' => now()->format('d M Y')
        ]);
        
        return $pdf->download('rekomendasi-penjualan-ubi-matang-' . now()->format('Y-m-d') . '.pdf');
    }
    
    /**
     * Set recommended market for an item
     */
    public function setMarket(Request $request, $id)
    {
        $request->validate([
            'market_name' => 'required|string|max:255'
        ]);
        
        $item = ProcessedInventory::findOrFail($id);
        $item->recommended_market = $request->market_name;
        $item->save();
        
        return redirect()->back()->with('success', 'Market untuk distribusi berhasil diperbarui');
    }



    /**
     * Export sales report to Excel
     */
    public function salesReport(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));
        $marketFilter = $request->get('market');

        // Get sales data
        $salesData = $this->getSalesData($startDate, $endDate, $marketFilter);

        // Create Excel export
        return $this->exportSalesExcel($salesData, $startDate, $endDate);
    }

    private function getSalesData($startDate, $endDate, $marketFilter = null)
    {
        $query = \App\Models\TransactionItem::select([
            'transaction_items.product_name',
            'transaction_items.quantity',
            'transaction_items.price',
            'transaction_items.subtotal',
            'transactions.created_at',
            'transactions.customer_name',
            'transactions.payment_method'
        ])
        ->join('transactions', 'transaction_items.transaction_id', '=', 'transactions.id')
        ->where('transactions.status', 'completed')
        ->whereBetween('transactions.created_at', [$startDate . ' 00:00:00', $endDate . ' 23:59:59']);

        if ($marketFilter) {
            $query->where('transactions.customer_name', 'like', '%' . $marketFilter . '%');
        }

        return $query->orderBy('transactions.created_at', 'desc')->get();
    }

    private function exportSalesExcel($salesData, $startDate, $endDate)
    {
        $filename = 'sales_report_' . $startDate . '_to_' . $endDate . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($salesData) {
            $file = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");

            // Add headers
            fputcsv($file, [
                'Tanggal',
                'Nama Produk',
                'Quantity',
                'Harga Satuan',
                'Subtotal',
                'Customer',
                'Metode Pembayaran'
            ]);

            // Add data
            foreach ($salesData as $row) {
                fputcsv($file, [
                    \Carbon\Carbon::parse($row->created_at)->format('d/m/Y H:i'),
                    $row->product_name,
                    $row->quantity,
                    'Rp ' . number_format($row->price, 0, ',', '.'),
                    'Rp ' . number_format($row->subtotal, 0, ',', '.'),
                    $row->customer_name ?: '-',
                    ucfirst($row->payment_method)
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Create distribution for expiring product
     */
    public function createDistribution(Request $request, $id)
    {
        $request->validate([
            'destination' => 'required|string|max:255',
            'quantity' => 'required|integer|min:1',
            'distribution_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string'
        ]);

        $item = ProcessedInventory::findOrFail($id);

        // Validate quantity
        if ($request->quantity > $item->current_stock) {
            return back()->withErrors(['quantity' => 'Jumlah melebihi stok tersedia'])
                        ->withInput();
        }

        DB::beginTransaction();

        try {
            // Determine if this is urgent based on expiry date
            $daysUntilExpiry = \Carbon\Carbon::parse($item->expiry_date)->diffInDays(now(), false);
            $isUrgent = $daysUntilExpiry <= 3;

            // Create distribution record
            $distributionNumber = $isUrgent ? 'URGENT-' . date('YmdHis') : 'DIST-' . date('YmdHis');
            $notes = $request->notes;
            if ($isUrgent) {
                $notes .= ' [PRIORITAS TINGGI - Produk mendekati kadaluarsa]';
            }

            $distribution = Distribution::create([
                'distribution_number' => $distributionNumber,
                'user_id' => Auth::id(),
                'destination' => $request->destination,
                'market_name' => $request->destination, // For compatibility
                'distribution_date' => $request->distribution_date,
                'notes' => $notes,
                'status' => 'planned',
                'is_urgent' => $isUrgent
            ]);

            // Create distribution item
            \App\Models\DistributionItem::create([
                'distribution_id' => $distribution->id,
                'processed_inventory_id' => $item->id,
                'quantity' => $request->quantity,
                'price_per_item' => $item->selling_price,
                'total_price' => $request->quantity * $item->selling_price
            ]);

            // Update stock
            $item->current_stock -= $request->quantity;
            $item->save();

            // Update expiry tracking
            $item->updateExpiryTracking();

            DB::commit();

            $message = $isUrgent
                ? 'Distribusi prioritas tinggi berhasil dibuat untuk produk yang mendekati kadaluarsa'
                : 'Distribusi berhasil dibuat';

            return redirect()->route('expiry-recommendations.index')
                           ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Terjadi kesalahan: ' . $e->getMessage()])
                        ->withInput();
        }
    }


}
