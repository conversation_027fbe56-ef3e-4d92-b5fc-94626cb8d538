<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Ka<PERSON>wan - <PERSON> Cilembu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background-color: #FFF8E1;
            font-family: 'Arial', sans-serif;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 0;
        }
        
        .login-container {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 450px;
            padding: 35px;
            text-align: center;
        }
        
        .header-icon {
            font-size: 45px;
            color: #FF8C00;
            margin-bottom: 15px;
        }
        
        h1 {
            color: #8B4513;
            font-size: 28px;
            margin-bottom: 5px;
        }
        
        .subtitle {
            color: #666;
            font-size: 15px;
            margin-bottom: 25px;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .form-group label {
            font-weight: 600;
            color: #333;
            margin-bottom: 7px;
            display: block;
        }
        
        .input-with-icon {
            position: relative;
        }
        
        .input-with-icon i {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            color: #888;
        }
        
        .form-control {
            padding: 13px 13px 13px 45px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 15px;
        }
        
        .form-control:focus {
            border-color: #8B4513;
            box-shadow: 0 0 0 0.2rem rgba(139, 69, 19, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(to right, #8B4513, #FF8C00);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 13px;
            width: 100%;
            font-weight: 600;
            font-size: 16px;
            margin-top: 15px;
        }
        
        .btn-login:hover {
            opacity: 0.9;
        }
        
        .alert {
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 25px;
            font-size: 14px;
        }
        
        .back-link {
            display: block;
            margin-top: 25px;
            color: #666;
            text-decoration: none;
            font-size: 14px;
        }
        
        .back-link:hover {
            color: #8B4513;
        }
        
        .credentials-info {
            background-color: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: left;
            font-size: 14px;
            border-left: 4px solid #FF8C00;
        }
        
        .emergency-login {
            margin-top: 20px;
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        
        .emergency-btn {
            display: inline-block;
            background-color: #f8f9fa;
            padding: 8px 15px;
            border-radius: 4px;
            color: #666;
            text-decoration: none;
            font-size: 14px;
            margin-top: 10px;
        }
        
        .emergency-btn:hover {
            background-color: #e9ecef;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <i class="fas fa-user-circle header-icon"></i>
        <h1>Login Karyawan</h1>
        <p class="subtitle">Akses khusus untuk karyawan Ubi Bakar Cilembu</p>
        
        <!-- Credentials Info -->
        <div class="credentials-info">
            <strong>Info Login:</strong>
            <div class="mt-1">
                <i class="fas fa-envelope-square me-1"></i> <EMAIL>
            </div>
            <div class="mt-1">
                <i class="fas fa-key me-1"></i> karyawan123
            </div>
        </div>
        
        <!-- Error Alert -->
        @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0 ps-3">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif
        
        <!-- Login Form -->
        <form method="POST" action="{{ url('/login-direct') }}">
            @csrf
            
            <!-- Role value determined dynamically -->
            @php
                $roleValue = 'employee'; // Default value
                
                try {
                    $roleInfo = DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
                    if (!empty($roleInfo)) {
                        $roleType = $roleInfo[0]->Type;
                        if (strpos($roleType, 'enum') !== false) {
                            preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
                            if (!empty($matches[1])) {
                                $validRoles = explode("','", $matches[1]);
                                // Use 'karyawan' if it's a valid value, otherwise use 'employee'
                                if (in_array('karyawan', $validRoles)) {
                                    $roleValue = 'karyawan';
                                }
                            }
                        }
                    }
                } catch (\Exception $e) {
                    // Keep default value
                }
            @endphp
            
            <input type="hidden" name="role" value="{{ $roleValue }}">
            
            <div class="form-group">
                <label for="email">Email</label>
                <div class="input-with-icon">
                    <i class="fas fa-envelope"></i>
                    <input type="email" class="form-control" id="email" name="email" 
                           value="<EMAIL>" required readonly>
                </div>
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <div class="input-with-icon">
                    <i class="fas fa-lock"></i>
                    <input type="password" class="form-control" id="password" name="password" 
                           value="karyawan123" required>
                </div>
            </div>
            
            <div class="form-check mb-3 text-start">
                <input class="form-check-input" type="checkbox" id="remember" name="remember" checked>
                <label class="form-check-label" for="remember">
                    Ingat saya
                </label>
            </div>
            
            <button type="submit" class="btn btn-login">
                <i class="fas fa-sign-in-alt me-2"></i> Login Sekarang
            </button>
        </form>
        
        <div class="emergency-login">
            <small>Masih ada masalah login?</small>
            <div>
                <a href="{{ url('/emergency-login-karyawan') }}" class="emergency-btn">
                    <i class="fas fa-exclamation-triangle me-1"></i> Login Emergency
                </a>
                <a href="{{ url('/fix-role-values') }}" class="emergency-btn mt-2">
                    <i class="fas fa-wrench me-1"></i> Perbaiki Roles
                </a>
            </div>
        </div>
        
        <a href="{{ route('login') }}" class="back-link">
            <i class="fas fa-arrow-left me-1"></i> Kembali ke halaman login utama
        </a>
    </div>
</body>
</html> 