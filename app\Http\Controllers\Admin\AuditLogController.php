<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\View\View;

class AuditLogController extends Controller
{
    /**
     * Menampilkan daftar audit logs.
     */
    public function index(Request $request): View
    {
        // Ambil parameter filter
        $action = $request->input('action');
        $modelType = $request->input('model_type');
        $userId = $request->input('user_id');
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');

        // Query dasar
        $query = AuditLog::with('user')->latest();

        // Terapkan filter
        if ($action) {
            $query->where('action', $action);
        }

        if ($modelType) {
            $query->where('model_type', $modelType);
        }

        if ($userId) {
            $query->where('user_id', $userId);
        }

        if ($dateFrom) {
            $query->whereDate('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->whereDate('created_at', '<=', $dateTo);
        }

        // Ambil data dengan pagination
        $auditLogs = $query->paginate(20);

        // Ambil daftar pengguna untuk dropdown filter
        $users = User::all();

        // Ambil daftar model types yang ada di audit logs
        $modelTypes = AuditLog::distinct('model_type')->pluck('model_type');

        // Ambil daftar actions yang ada di audit logs
        $actions = AuditLog::distinct('action')->pluck('action');

        return view('admin.audit-logs.index', compact(
            'auditLogs',
            'users',
            'modelTypes',
            'actions',
            'action',
            'modelType',
            'userId',
            'dateFrom',
            'dateTo'
        ));
    }

    /**
     * Menampilkan detail audit log.
     */
    public function show(AuditLog $auditLog): View
    {
        return view('admin.audit-logs.show', compact('auditLog'));
    }
}
