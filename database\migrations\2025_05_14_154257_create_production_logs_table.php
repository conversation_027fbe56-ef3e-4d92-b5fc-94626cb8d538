<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('raw_inventory_id')->constrained('raw_inventory')->onDelete('restrict');
            $table->foreignId('processed_inventory_id')->constrained('processed_inventory')->onDelete('restrict');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->decimal('raw_amount_used', 10, 2)->comment('dalam kg');
            $table->integer('produced_amount')->comment('jumlah item yang dihasilkan');
            $table->decimal('raw_cost', 12, 2)->comment('biaya bahan mentah');
            $table->decimal('additional_cost', 12, 2)->default(0)->comment('biaya tambahan');
            $table->decimal('total_cost', 12, 2)->comment('total biaya produksi');
            $table->decimal('cost_per_item', 12, 2)->comment('biaya per item');
            $table->string('raw_name')->comment('nama bahan mentah saat produksi');
            $table->string('processed_name')->comment('nama produk saat produksi');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_logs');
    }
};
