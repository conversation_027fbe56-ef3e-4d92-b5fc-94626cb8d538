@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-fire-alt"></i>
        <span>Edit Produk Ubi <PERSON>ng</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Form Edit {{ $processedInventory->name }}</span>
                    <a href="{{ route('processed-inventory.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <form action="{{ route('processed-inventory.update', $processedInventory) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Nama Produk <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $processedInventory->name) }}" required>
                                @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="raw_inventory_id" class="form-label">Bahan Baku</label>
                                <select class="form-select @error('raw_inventory_id') is-invalid @enderror" id="raw_inventory_id" name="raw_inventory_id">
                                    <option value="">-- Pilih Bahan Baku --</option>
                                    @foreach($rawItems as $raw)
                                    <option value="{{ $raw->id }}" {{ old('raw_inventory_id', $processedInventory->raw_inventory_id) == $raw->id ? 'selected' : '' }}>
                                        {{ $raw->name }} (Stok: {{ $raw->current_stock }} kg)
                                    </option>
                                    @endforeach
                                </select>
                                @error('raw_inventory_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="form-text text-muted">Opsional. Pilih jika produk ini dibuat dari ubi mentah tertentu.</small>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="current_stock" class="form-label">Stok Saat Ini <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('current_stock') is-invalid @enderror" id="current_stock" name="current_stock" value="{{ old('current_stock', $processedInventory->current_stock) }}" min="0" required>
                                @error('current_stock')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="cost_per_item" class="form-label">Biaya per Item (Rp)</label>
                                <input type="number" class="form-control @error('cost_per_item') is-invalid @enderror" id="cost_per_item" name="cost_per_item" value="{{ old('cost_per_item', $processedInventory->cost_per_item) }}" min="0">
                                @error('cost_per_item')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="selling_price" class="form-label">Harga Jual (Rp) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('selling_price') is-invalid @enderror" id="selling_price" name="selling_price" value="{{ old('selling_price', $processedInventory->selling_price) }}" min="0" required>
                                @error('selling_price')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="min_stock_threshold" class="form-label">Batas Minimum Stok</label>
                                <input type="number" class="form-control @error('min_stock_threshold') is-invalid @enderror" id="min_stock_threshold" name="min_stock_threshold" value="{{ old('min_stock_threshold', $processedInventory->min_stock_threshold) }}" min="0">
                                <small class="form-text text-muted">Sistem akan memberikan peringatan saat stok di bawah nilai ini.</small>
                                @error('min_stock_threshold')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="raw_material_per_item" class="form-label">Berat Bahan Baku per Item (kg)</label>
                                <input type="number" class="form-control @error('raw_material_per_item') is-invalid @enderror" id="raw_material_per_item" name="raw_material_per_item" value="{{ old('raw_material_per_item', $processedInventory->raw_material_per_item) }}" step="0.01" min="0">
                                <small class="form-text text-muted">Berapa kg ubi mentah yang digunakan untuk membuat 1 item produk ini.</small>
                                @error('raw_material_per_item')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Informasi Produksi dan Kadaluarsa -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Informasi Produksi & Kadaluarsa</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="production_date" class="form-label">Tanggal Produksi <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control @error('production_date') is-invalid @enderror" id="production_date" name="production_date" value="{{ old('production_date', $processedInventory->production_date?->format('Y-m-d')) }}" required>
                                        @error('production_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="expiry_date" class="form-label">Tanggal Kadaluarsa <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control @error('expiry_date') is-invalid @enderror" id="expiry_date" name="expiry_date" value="{{ old('expiry_date', $processedInventory->expiry_date?->format('Y-m-d')) }}" required>
                                        @error('expiry_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">
                                            @php
                                                $daysUntilExpiry = $processedInventory->expiry_date ? \Carbon\Carbon::parse($processedInventory->expiry_date)->diffInDays(now(), false) : null;
                                            @endphp
                                            @if($daysUntilExpiry !== null)
                                                @if($daysUntilExpiry < 0)
                                                    <span class="text-danger">Kadaluarsa {{ abs($daysUntilExpiry) }} hari yang lalu</span>
                                                @elseif($daysUntilExpiry == 0)
                                                    <span class="text-warning">Kadaluarsa hari ini</span>
                                                @elseif($daysUntilExpiry <= 3)
                                                    <span class="text-warning">{{ $daysUntilExpiry }} hari lagi kadaluarsa</span>
                                                @else
                                                    <span class="text-success">{{ $daysUntilExpiry }} hari lagi kadaluarsa</span>
                                                @endif
                                            @endif
                                        </small>
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="product_type" class="form-label">Jenis Produk <span class="text-danger">*</span></label>
                                        <select class="form-select @error('product_type') is-invalid @enderror" id="product_type" name="product_type" required>
                                            <option value="">-- Pilih Jenis Produk --</option>
                                            <option value="Original" {{ old('product_type', $processedInventory->product_type) == 'Original' ? 'selected' : '' }}>Original</option>
                                            <option value="Premium" {{ old('product_type', $processedInventory->product_type) == 'Premium' ? 'selected' : '' }}>Premium</option>
                                            <option value="Special" {{ old('product_type', $processedInventory->product_type) == 'Special' ? 'selected' : '' }}>Special</option>
                                        </select>
                                        @error('product_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8 mb-3">
                                        <label for="notes" class="form-label">Catatan</label>
                                        <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3" placeholder="Catatan tambahan tentang produk ini...">{{ old('notes', $processedInventory->notes) }}</textarea>
                                        @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-4 mb-3">
                                        <label for="is_active" class="form-label">Status Produk</label>
                                        <div class="form-check form-switch mt-2">
                                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" {{ old('is_active', $processedInventory->is_active) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_active">
                                                Produk Aktif
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">Produk aktif akan muncul dalam daftar penjualan.</small>
                                    </div>
                                </div>

                                <!-- Informasi Status Kadaluarsa -->
                                @if($processedInventory->expiry_date)
                                <div class="row">
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-info-circle me-2"></i>Status Kadaluarsa</h6>
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <strong>Prioritas:</strong>
                                                    @php $processedInventory->updateExpiryTracking(); @endphp
                                                    <span class="badge bg-{{ $processedInventory->priority_level == 'Tinggi' ? 'danger' : ($processedInventory->priority_level == 'Sedang' ? 'warning' : 'success') }}">
                                                        {{ $processedInventory->priority_level ?? 'Belum dihitung' }}
                                                    </span>
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>Perlu Dijual Segera:</strong>
                                                    <span class="badge bg-{{ $processedInventory->needs_immediate_sale ? 'danger' : 'success' }}">
                                                        {{ $processedInventory->needs_immediate_sale ? 'Ya' : 'Tidak' }}
                                                    </span>
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>Rekomendasi Pasar:</strong>
                                                    {{ $processedInventory->recommended_market ?? 'Belum ada' }}
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>Hari Tersisa:</strong>
                                                    {{ $processedInventory->days_until_expiry ?? 'Belum dihitung' }} hari
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan Perubahan
                            </button>
                            <a href="{{ route('processed-inventory.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Batal
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const productionDateInput = document.getElementById('production_date');
    const expiryDateInput = document.getElementById('expiry_date');
    const productTypeSelect = document.getElementById('product_type');

    // Auto-calculate expiry date based on production date and product type
    function calculateExpiryDate() {
        const productionDate = new Date(productionDateInput.value);
        const productType = productTypeSelect.value;

        if (productionDate && productType) {
            let daysToAdd = 7; // Default 7 days

            // Adjust days based on product type
            switch(productType) {
                case 'Original':
                    daysToAdd = 7;
                    break;
                case 'Premium':
                    daysToAdd = 5; // Premium products might have shorter shelf life due to toppings
                    break;
                case 'Special':
                    daysToAdd = 3; // Special products with dairy/cream have shorter shelf life
                    break;
            }

            const expiryDate = new Date(productionDate);
            expiryDate.setDate(expiryDate.getDate() + daysToAdd);

            // Format date to YYYY-MM-DD
            const formattedDate = expiryDate.toISOString().split('T')[0];
            expiryDateInput.value = formattedDate;
        }
    }

    // Event listeners for date calculation
    productionDateInput.addEventListener('change', calculateExpiryDate);
    productTypeSelect.addEventListener('change', calculateExpiryDate);

    // Calculate margin when cost or selling price changes
    const costPerItemInput = document.getElementById('cost_per_item');
    const sellingPriceInput = document.getElementById('selling_price');

    function calculateMargin() {
        const cost = parseFloat(costPerItemInput.value) || 0;
        const selling = parseFloat(sellingPriceInput.value) || 0;

        if (cost > 0 && selling > 0) {
            const margin = ((selling - cost) / selling * 100).toFixed(1);

            // Create or update margin display
            let marginDisplay = document.getElementById('margin-display');
            if (!marginDisplay) {
                marginDisplay = document.createElement('small');
                marginDisplay.id = 'margin-display';
                marginDisplay.className = 'form-text text-info';
                sellingPriceInput.parentNode.appendChild(marginDisplay);
            }

            marginDisplay.textContent = `Margin: ${margin}%`;

            // Color coding for margin
            if (margin < 20) {
                marginDisplay.className = 'form-text text-danger';
            } else if (margin < 40) {
                marginDisplay.className = 'form-text text-warning';
            } else {
                marginDisplay.className = 'form-text text-success';
            }
        }
    }

    costPerItemInput.addEventListener('input', calculateMargin);
    sellingPriceInput.addEventListener('input', calculateMargin);

    // Real-time expiry status update
    function updateExpiryStatus() {
        const expiryDate = new Date(expiryDateInput.value);
        const today = new Date();
        const diffTime = expiryDate - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        const statusElement = expiryDateInput.nextElementSibling;
        if (statusElement && statusElement.classList.contains('form-text')) {
            let statusText = '';
            let statusClass = '';

            if (diffDays < 0) {
                statusText = `Kadaluarsa ${Math.abs(diffDays)} hari yang lalu`;
                statusClass = 'text-danger';
            } else if (diffDays === 0) {
                statusText = 'Kadaluarsa hari ini';
                statusClass = 'text-warning';
            } else if (diffDays <= 3) {
                statusText = `${diffDays} hari lagi kadaluarsa`;
                statusClass = 'text-warning';
            } else {
                statusText = `${diffDays} hari lagi kadaluarsa`;
                statusClass = 'text-success';
            }

            statusElement.innerHTML = `<span class="${statusClass}">${statusText}</span>`;
        }
    }

    expiryDateInput.addEventListener('change', updateExpiryStatus);

    // Initial calculations
    calculateMargin();
    updateExpiryStatus();
});
</script>
@endpush

@endsection