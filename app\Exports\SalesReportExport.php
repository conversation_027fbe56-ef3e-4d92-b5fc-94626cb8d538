<?php

namespace App\Exports;

use App\Models\Transaction;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class SalesReportExport implements FromCollection, WithHeadings, WithMapping, WithTitle, WithStyles
{
    protected $transactions;
    protected $startDate;
    protected $endDate;
    
    public function __construct($transactions, $startDate, $endDate)
    {
        $this->transactions = $transactions;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }
    
    public function collection()
    {
        return $this->transactions;
    }
    
    public function headings(): array
    {
        return [
            'No. Invoice',
            'Tanggal',
            'Kasir',
            'Pelanggan',
            'Metode Pembayaran',
            'Item',
            'Subtotal',
            'Pajak',
            'Diskon',
            'Total'
        ];
    }
    
    public function map($transaction): array
    {
        $items = $transaction->items->map(function($item) {
            $productName = '';
            if ($item->processedInventory) {
                $productName = $item->processedInventory->name;
            } elseif ($item->otherProduct) {
                $productName = $item->otherProduct->name;
            } else {
                $productName = 'Produk Tidak Diketahui';
            }
            
            return $item->quantity . 'x ' . $productName . ' @' . number_format($item->price_per_item, 0, ',', '.');
        })->implode("\n");
        
        return [
            $transaction->invoice_number,
            $transaction->created_at->format('d/m/Y H:i'),
            $transaction->user ? $transaction->user->name : 'Sistem',
            $transaction->customer_name ?: 'Umum',
            $transaction->payment_method,
            $items,
            number_format($transaction->subtotal, 0, ',', '.'),
            number_format($transaction->tax, 0, ',', '.'),
            number_format($transaction->discount, 0, ',', '.'),
            number_format($transaction->total_amount, 0, ',', '.')
        ];
    }
    
    public function title(): string
    {
        return 'Laporan Penjualan';
    }
    
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => ['font' => ['bold' => true]],
            
            // Set autosize for columns
            'A' => ['autosize' => true],
            'B' => ['autosize' => true],
            'C' => ['autosize' => true],
            'D' => ['autosize' => true],
            'E' => ['autosize' => true],
            'F' => ['autosize' => true],
            'G' => ['autosize' => true],
            'H' => ['autosize' => true],
            'I' => ['autosize' => true],
            'J' => ['autosize' => true],
        ];
    }
}
