@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-box"></i>
        <span>Detail Produk</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Detail {{ $otherProduct->name }}</span>
                    <div>
                        <a href="{{ route('other-products.edit', $otherProduct) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('other-products.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">SKU</th>
                                    <td>{{ $otherProduct->sku }}</td>
                                </tr>
                                <tr>
                                    <th>Nama Produk</th>
                                    <td>{{ $otherProduct->name }}</td>
                                </tr>
                                <tr>
                                    <th>Kategori</th>
                                    <td>{{ $otherProduct->category ?: '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Deskripsi</th>
                                    <td>{{ $otherProduct->description ?: '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Supplier</th>
                                    <td>{{ $otherProduct->supplier ?: '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">Stok Saat Ini</th>
                                    <td>{{ $otherProduct->current_stock }} {{ $otherProduct->unit }}</td>
                                </tr>
                                <tr>
                                    <th>Batas Minimum</th>
                                    <td>{{ $otherProduct->min_stock_threshold }} {{ $otherProduct->unit }}</td>
                                </tr>
                                <tr>
                                    <th>Harga Beli</th>
                                    <td>Rp {{ number_format($otherProduct->purchase_price, 0, ',', '.') }}</td>
                                </tr>
                                <tr>
                                    <th>Harga Jual</th>
                                    <td>Rp {{ number_format($otherProduct->selling_price, 0, ',', '.') }}</td>
                                </tr>
                                <tr>
                                    <th>Margin</th>
                                    <td>
                                        {{ number_format($otherProduct->getProfitMargin(), 1) }}%
                                        (Rp {{ number_format($otherProduct->getProfit(), 0, ',', '.') }} per {{ $otherProduct->unit }})
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($otherProduct->notes)
                    <div class="mt-4">
                        <h5>Catatan:</h5>
                        <p class="mb-0">{{ $otherProduct->notes }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 