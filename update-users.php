<?php

// Bootstrap Laravel
require __DIR__.'/vendor/autoload.php';
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

echo "Memperbarui kredensial login pengguna...\n";

// Periksa apa nilai enum yang valid untuk role
try {
    $roleInfo = DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
    $roleType = $roleInfo[0]->Type;
    $validRoles = [];

    if (strpos($roleType, 'enum') !== false) {
        preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
        $validRoles = explode("','", $matches[1]);
    }

    echo "Role yang valid dalam database: " . implode(', ', $validRoles) . "\n";
} catch (\Exception $e) {
    echo "Error saat memeriksa role: " . $e->getMessage() . "\n";
}

// Update akun admin
try {
    // Cari admin tanpa memperhatikan soft delete
    $admin = User::withTrashed()->where('email', '<EMAIL>')->first();
    
    if ($admin) {
        // Restore jika soft deleted
        if ($admin->trashed()) {
            $admin->restore();
            echo "Akun admin dipulihkan dari soft delete.\n";
        }
        
        // Update password
        $admin->password = Hash::make('admin123');
        $admin->role = 'admin'; // Pastikan role benar
        $admin->save();
        
        echo "Akun admin berhasil diperbarui:\n";
    } else {
        // Buat baru jika tidak ada
        $admin = new User();
        $admin->name = 'Admin';
        $admin->email = '<EMAIL>';
        $admin->password = Hash::make('admin123');
        $admin->role = 'admin';
        $admin->save();
        
        echo "Akun admin berhasil dibuat:\n";
    }
    
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n";
    echo "Role: admin\n\n";
} catch (\Exception $e) {
    echo "Error saat memperbarui akun admin: " . $e->getMessage() . "\n";
}

// Update akun karyawan
try {
    // Cari karyawan tanpa memperhatikan soft delete
    $karyawan = User::withTrashed()->where('email', '<EMAIL>')->first();
    
    if ($karyawan) {
        // Restore jika soft deleted
        if ($karyawan->trashed()) {
            $karyawan->restore();
            echo "Akun karyawan dipulihkan dari soft delete.\n";
        }
        
        // Update password
        $karyawan->password = Hash::make('karyawan123');
        $karyawan->role = 'employee'; // Pastikan role benar
        $karyawan->save();
        
        echo "Akun karyawan berhasil diperbarui:\n";
    } else {
        // Buat baru jika tidak ada
        $karyawan = new User();
        $karyawan->name = 'Karyawan';
        $karyawan->email = '<EMAIL>';
        $karyawan->password = Hash::make('karyawan123');
        $karyawan->role = 'employee';
        $karyawan->save();
        
        echo "Akun karyawan berhasil dibuat:\n";
    }
    
    echo "Email: <EMAIL>\n";
    echo "Password: karyawan123\n";
    echo "Role: employee\n\n";
} catch (\Exception $e) {
    echo "Error saat memperbarui akun karyawan: " . $e->getMessage() . "\n";
}

// Verifikasi user yang aktif
echo "Daftar user aktif di sistem:\n";
$users = User::all();
foreach ($users as $user) {
    echo "- {$user->name} ({$user->email}): Role {$user->role}\n";
}

echo "\nSelesai!\n";
