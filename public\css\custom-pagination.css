/* Custom Pagination Styles */
.pagination, .custom-pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
    margin-bottom: 0;
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease-in-out;
}

.page-link:hover {
    z-index: 2;
    color: #0056b3;
    text-decoration: none;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-link:focus {
    z-index: 3;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.page-item:last-child .page-link {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Enhanced Previous and Next buttons */
.pagination .page-item .page-link[rel="prev"],
.pagination .page-item .page-link[rel="next"] {
    font-weight: 600;
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border-color: #ddd;
}

.pagination .page-item .page-link[rel="prev"]:hover,
.pagination .page-item .page-link[rel="next"]:hover {
    background-color: #e2e6ea;
    border-color: #ddd;
}

/* Add shadow effect on hover */
.page-link:hover {
    box-shadow: 0 3px 5px rgba(0,0,0,0.1);
}

/* Rounded pagination */
.pagination-rounded .page-link {
    border-radius: 50px;
    margin: 0 3px;
}

/* Show in mobile too */
@media (max-width: 576px) {
    .d-sm-none {
        display: none !important;
    }
    .d-none.d-sm-flex {
        display: flex !important;
    }
}
