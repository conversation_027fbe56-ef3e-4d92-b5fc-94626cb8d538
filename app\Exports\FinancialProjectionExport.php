<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;

class FinancialProjectionExport implements FromCollection, WithHeadings, WithStyles, WithTitle
{
    protected $projections;
    protected $scenarios;
    protected $summary;
    protected $parameters;

    public function __construct($projections, $scenarios, $summary, $parameters)
    {
        $this->projections = $projections;
        $this->scenarios = $scenarios;
        $this->summary = $summary;
        $this->parameters = $parameters;
    }

    public function collection()
    {
        $collection = new Collection();
        
        // Add parameters section
        $collection->push(['Parameter Proyeksi']);
        $collection->push(['Periode Proyeksi', $this->parameters['months'] . ' bulan']);
        $collection->push(['Tingkat Pertumbuhan Pendapatan', $this->parameters['growth_rate'] . '%']);
        $collection->push(['Tingkat Kenaikan Biaya', $this->parameters['cost_increase_rate'] . '%']);
        $collection->push([]);
        
        // Add summary section
        $collection->push(['Ringkasan Proyeksi']);
        $collection->push(['Total Pendapatan Proyeksi', 'Rp ' . number_format($this->summary['total_projected_revenue'], 0, ',', '.')]);
        $collection->push(['Total Laba Proyeksi', 'Rp ' . number_format($this->summary['total_projected_profit'], 0, ',', '.')]);
        $collection->push(['Rata-rata Pendapatan Bulanan', 'Rp ' . number_format($this->summary['average_monthly_revenue'], 0, ',', '.')]);
        $collection->push(['Rata-rata Laba Bulanan', 'Rp ' . number_format($this->summary['average_monthly_profit'], 0, ',', '.')]);
        $collection->push(['Rata-rata Margin Laba', $this->summary['average_profit_margin'] . '%']);
        $collection->push([]);
        
        // Add scenarios section
        $collection->push(['Analisis Skenario (Total ' . $this->parameters['months'] . ' Bulan)']);
        $collection->push(['Skenario', 'Pertumbuhan Pendapatan', 'Kenaikan Biaya', 'Total Pendapatan', 'Total Laba']);
        
        foreach ($this->scenarios as $name => $scenario) {
            $collection->push([
                ucfirst($name),
                $scenario['revenue_growth'] . '%',
                $scenario['cost_increase'] . '%',
                'Rp ' . number_format($scenario['total_revenue'], 0, ',', '.'),
                'Rp ' . number_format($scenario['total_profit'], 0, ',', '.')
            ]);
        }
        $collection->push([]);
        
        // Add monthly projections
        $collection->push(['Proyeksi Bulanan']);
        $collection->push(['Bulan', 'Pendapatan', 'HPP', 'Laba Kotor', 'Biaya Operasional', 'Laba Bersih', 'Margin Laba']);
        
        foreach ($this->projections as $projection) {
            $collection->push([
                $projection['month'],
                'Rp ' . number_format($projection['revenue'], 0, ',', '.'),
                'Rp ' . number_format($projection['cogs'], 0, ',', '.'),
                'Rp ' . number_format($projection['gross_profit'], 0, ',', '.'),
                'Rp ' . number_format($projection['operating_expense'], 0, ',', '.'),
                'Rp ' . number_format($projection['net_profit'], 0, ',', '.'),
                $projection['profit_margin'] . '%'
            ]);
        }
        
        return $collection;
    }

    public function headings(): array
    {
        return []; // We handle this manually in collection()
    }

    public function styles(Worksheet $sheet)
    {
        // Style for title cells
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A6')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A13')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A18')->getFont()->setBold(true)->setSize(14);
        
        // Style for header rows
        $sheet->getStyle('A14:E14')->getFont()->setBold(true);
        $sheet->getStyle('A19:G19')->getFont()->setBold(true);
        
        // Auto-size columns
        for ($col = 'A'; $col <= 'G'; $col++) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        return [
            1 => ['font' => ['bold' => true, 'size' => 14]],
        ];
    }

    public function title(): string
    {
        return 'Proyeksi Keuangan';
    }
}
