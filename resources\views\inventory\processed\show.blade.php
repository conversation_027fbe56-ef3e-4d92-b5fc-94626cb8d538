@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-fire-alt"></i>
        <span>Detail Produk Ubi Matang</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Detail {{ $processedInventory->name }}</span>
                    <div>
                        <a href="{{ route('processed-inventory.edit', $processedInventory) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('processed-inventory.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            @if($processedInventory->current_stock <= ($processedInventory->min_stock_threshold ?? 0))
                            <div class="alert alert-warning mb-4">
                                <h5><i class="fas fa-exclamation-triangle"></i> Peringatan Stok Menipis</h5>
                                <p class="mb-0">Stok {{ $processedInventory->name }} hampir habis. Segera lakukan proses produksi baru.</p>
                            </div>
                            @endif
                            
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <th style="width: 250px">Nama Produk</th>
                                        <td>{{ $processedInventory->name }}</td>
                                    </tr>
                                    <tr>
                                        <th>Bahan Baku</th>
                                        <td>{{ $processedInventory->rawMaterial->name ?? '-' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Stok Saat Ini</th>
                                        <td>{{ $processedInventory->current_stock }} pcs</td>
                                    </tr>
                                    <tr>
                                        <th>Biaya per Item</th>
                                        <td>Rp {{ number_format($processedInventory->cost_per_item, 0, ',', '.') }}</td>
                                    </tr>
                                    <tr>
                                        <th>Harga Jual</th>
                                        <td>Rp {{ number_format($processedInventory->selling_price, 0, ',', '.') }}</td>
                                    </tr>
                                    <tr>
                                        <th>Margin Keuntungan</th>
                                        <td>
                                            @if($processedInventory->cost_per_item && $processedInventory->selling_price > 0)
                                            {{ number_format((($processedInventory->selling_price - $processedInventory->cost_per_item) / $processedInventory->selling_price) * 100, 1) }}%
                                            (Rp {{ number_format($processedInventory->selling_price - $processedInventory->cost_per_item, 0, ',', '.') }} per item)
                                            @else
                                            -
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Batas Minimum Stok</th>
                                        <td>{{ $processedInventory->min_stock_threshold ?? '-' }} pcs</td>
                                    </tr>
                                    <tr>
                                        <th>Berat Bahan Baku per Item</th>
                                        <td>{{ $processedInventory->raw_material_per_item ?? '-' }} kg</td>
                                    </tr>
                                    <tr>
                                        <th>Status</th>
                                        <td>
                                            @if($processedInventory->current_stock <= ($processedInventory->min_stock_threshold ?? 0))
                                            <span class="status-badge danger">Stok Menipis</span>
                                            @else
                                            <span class="status-badge success">Tersedia</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Total Nilai Stok</th>
                                        <td>Rp {{ number_format($processedInventory->current_stock * $processedInventory->selling_price, 0, ',', '.') }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 