<?php

namespace App\Models\Traits;

use App\Models\AuditLog;
use Illuminate\Database\Eloquent\Model;

trait Auditable
{
    /**
     * Boot the trait.
     */
    protected static function bootAuditable()
    {
        static::created(function (Model $model) {
            static::logAction('create', $model, null, $model->getAttributes());
        });

        static::updated(function (Model $model) {
            $oldValues = [];
            $newValues = [];
            
            foreach ($model->getDirty() as $key => $value) {
                $oldValues[$key] = $model->getOriginal($key);
                $newValues[$key] = $value;
            }
            
            static::logAction('update', $model, $oldValues, $newValues);
        });

        static::deleted(function (Model $model) {
            static::logAction('delete', $model, $model->getAttributes(), null);
        });

        if (method_exists(static::class, 'restored')) {
            static::restored(function (Model $model) {
                static::logAction('restore', $model, null, $model->getAttributes());
            });
        }
    }

    /**
     * Log an action to the audit log.
     *
     * @param string $action
     * @param Model $model
     * @param array|null $oldValues
     * @param array|null $newValues
     * @return void
     */
    protected static function logAction(string $action, Model $model, ?array $oldValues, ?array $newValues): void
    {
        // Jangan log perubahan pada kolom timestamp
        if ($oldValues) {
            foreach (['created_at', 'updated_at', 'deleted_at'] as $timestamp) {
                if (array_key_exists($timestamp, $oldValues)) {
                    unset($oldValues[$timestamp]);
                }
            }
        }

        if ($newValues) {
            foreach (['created_at', 'updated_at', 'deleted_at'] as $timestamp) {
                if (array_key_exists($timestamp, $newValues)) {
                    unset($newValues[$timestamp]);
                }
            }
        }

        // Jika tidak ada perubahan selain timestamp, jangan log
        if ($action === 'update' && empty($oldValues)) {
            return;
        }

        AuditLog::log($action, $model, $oldValues, $newValues);
    }
}
