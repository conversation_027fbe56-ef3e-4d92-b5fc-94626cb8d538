<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><PERSON>bi Bakar Cilembu - Sistem Manajemen Inventory & Keuangan</title>
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=poppins:400,500,600,700&display=swap" rel="stylesheet" />
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.css" />
    
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #FF8C00;
            --accent-color: #4CAF50;
            --light-color: #FFF8E1;
            --dark-color: #5D4037;
        }
        
        html, body {
            font-family: 'Poppins', sans-serif;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        .swiper-container {
            width: 100%;
            height: 100vh;
            position: relative;
        }
        
        .swiper-slide {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            background-size: cover;
            background-position: center;
        }
        
        .swiper-slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1;
        }
        
        .slide-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            padding: 0 20px;
        }
        
        .slide-content h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: white;
        }
        
        .slide-content p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .btn-custom {
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: none;
        }
        
        .btn-primary-custom {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-primary-custom:hover {
            background-color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .btn-outline-custom {
            background-color: transparent;
            color: white;
            border: 2px solid white;
        }
        
        .btn-outline-custom:hover {
            background-color: white;
            color: var(--dark-color);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            padding: 1.5rem 2rem;
            transition: all 0.5s ease;
        }
        
        .navbar.scrolled {
            background-color: var(--primary-color);
            padding: 1rem 2rem;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }
        
        .navbar-brand {
            font-size: 1.8rem;
            font-weight: 700;
            color: white;
        }
        
        .navbar-brand span {
            color: var(--secondary-color);
        }
        
        .navbar-nav .nav-item {
            margin: 0 0.5rem;
        }
        
        .navbar-nav .nav-link {
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            position: relative;
        }
        
        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: var(--secondary-color);
        }
        
        .navbar-nav .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 1rem;
            right: 1rem;
            height: 2px;
            background-color: var(--secondary-color);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover::after,
        .navbar-nav .nav-link.active::after {
            transform: scaleX(1);
        }
        
        .navbar-toggler {
            border: none;
            color: white;
        }
        
        .navbar-toggler:focus {
            box-shadow: none;
        }
        
        .auth-buttons .btn {
            margin-left: 0.5rem;
        }
        
        .pagination-container {
            position: fixed;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }
        
        .pagination-bullet {
            width: 12px;
            height: 12px;
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            margin: 8px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .pagination-bullet.active {
            background-color: white;
            width: 15px;
            height: 15px;
        }
        
        .scroll-down {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            color: white;
            text-align: center;
            animation: bounce 2s infinite;
        }
        
        .scroll-down span {
            display: block;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .scroll-down i {
            font-size: 1.5rem;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }
        
        .dark-mode-toggle {
            position: fixed;
            bottom: 2rem;
            left: 2rem;
            z-index: 1000;
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .dark-mode-toggle:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        /* Responsive styles */
        @media (max-width: 991px) {
            .navbar-collapse {
                background-color: var(--primary-color);
                border-radius: 10px;
                padding: 1rem;
                margin-top: 1rem;
            }
            
            .auth-buttons .btn {
                margin: 0.5rem 0;
                display: block;
                width: 100%;
            }
            
            .pagination-container {
                right: 1rem;
            }
            
            .dark-mode-toggle {
                bottom: 1rem;
                left: 1rem;
                width: 40px;
                height: 40px;
            }
            
            .slide-content h1 {
                font-size: 2.5rem;
            }
            
            .slide-content p {
                font-size: 1rem;
            }
        }
        
        @media (max-width: 767px) {
            .slide-content h1 {
                font-size: 2rem;
            }
        }

        /* Fade animations for slide content */
        .fade-in-up {
            animation: fadeInUp 1s ease forwards;
            opacity: 0;
            transform: translateY(50px);
        }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .delay-1 {
            animation-delay: 0.3s;
        }
        
        .delay-2 {
            animation-delay: 0.6s;
        }
        
        .delay-3 {
            animation-delay: 0.9s;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-fire me-2"></i>Ubi<span>Bakar</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto me-lg-4">
                    <li class="nav-item">
                        <a class="nav-link active" data-slide="0" href="#home">Beranda</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-slide="1" href="#about">Tentang</a>
                    </li>
                </ul>
                <div class="auth-buttons d-flex flex-column flex-lg-row">
                    @if (Route::has('login'))
                        @auth
                            <a href="{{ url('/dashboard') }}" class="btn btn-primary-custom">Dashboard</a>
                        @else
                            <a href="{{ route('login') }}" class="btn btn-outline-custom">Masuk</a>
                            @if (Route::has('register'))
                                <a href="{{ route('register') }}" class="btn btn-primary-custom">Daftar</a>
                            @endif
                        @endauth
                    @endif
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Page Navigation Bullets -->
    <div class="pagination-container">
        <div class="pagination-bullet active" data-slide="0"></div>
        <div class="pagination-bullet" data-slide="1"></div>
    </div>
    
    <!-- Scroll Down Indicator (only on first slide) -->
    <div class="scroll-down">
        <span>Geser Kebawah</span>
        <i class="fas fa-chevron-down"></i>
    </div>
    
    <!-- Dark Mode Toggle -->
    <div class="dark-mode-toggle">
        <i class="fas fa-moon"></i>
    </div>
    
    <!-- Full Page Slider -->
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <!-- Home Slide -->
            <div class="swiper-slide" id="home" style="background-image: url('https://images.unsplash.com/photo-1603052875302-d376b78fc60c?ixlib=rb-1.2.1&auto=format&fit=crop');">
                <div class="slide-content">
                    <h1 class="fade-in-up">Ubi Bakar Cilembu</h1>
                    <p class="fade-in-up delay-1">
                        Sistem manajemen inventori dan keuangan untuk membantu Anda mengelola bisnis ubi bakar dengan lebih efisien dan profesional.
                    </p>
                    <div class="btn-group fade-in-up delay-2">
                        <a href="{{ route('login') }}" class="btn btn-primary-custom me-3">Masuk</a>
                        <a href="#about" class="btn btn-outline-custom page-scroll" data-slide="1">Pelajari Lebih Lanjut</a>
                    </div>
                </div>
            </div>
            
            <!-- About Slide -->
            <div class="swiper-slide" id="about" style="background-image: url('https://images.unsplash.com/photo-1595436252086-7496fb8acb68?ixlib=rb-1.2.1&auto=format&fit=crop');">
                <div class="slide-content">
                    <h1 class="fade-in-up">Tentang Sistem</h1>
                    <p class="fade-in-up delay-1">
                        Kami menyediakan solusi lengkap untuk pengelolaan bisnis ubi bakar Anda. Dari monitoring stok, pencatatan penjualan, hingga laporan keuangan yang komprehensif, semuanya terintegrasi dalam satu platform yang mudah digunakan.
                    </p>
                    <p class="fade-in-up delay-2">
                        Dikembangkan khusus untuk bisnis ubi bakar Cilembu, sistem ini memahami keunikan dan kebutuhan spesifik industri Anda.
                    </p>
                    <div class="btn-group fade-in-up delay-3">
                        <a href="{{ route('login') }}" class="btn btn-primary-custom">Mulai Sekarang</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@9/swiper-bundle.min.js"></script>
    <script>
        // Initialize Swiper
        const swiper = new Swiper('.swiper-container', {
            direction: 'vertical',
            slidesPerView: 1,
            mousewheel: true,
            speed: 800,
            effect: 'fade',
            fadeEffect: {
                crossFade: true
            },
            on: {
                slideChange: function () {
                    updateActiveLinks();
                    
                    // Show scroll down indicator only on first slide
                    if (this.activeIndex === 0) {
                        document.querySelector('.scroll-down').style.display = 'block';
                    } else {
                        document.querySelector('.scroll-down').style.display = 'none';
                    }
                    
                    // Activate animations for current slide
                    const currentSlide = document.querySelector('.swiper-slide-active');
                    const animations = currentSlide.querySelectorAll('.fade-in-up');
                    animations.forEach(element => {
                        element.style.animation = 'none';
                        setTimeout(() => {
                            element.style.animation = '';
                        }, 10);
                    });
                }
            }
        });
        
        // Update navigation links
        function updateActiveLinks() {
            const index = swiper.activeIndex;
            
            // Update navigation links
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            const activeLink = document.querySelector(`.navbar-nav .nav-link[data-slide="${index}"]`);
            if (activeLink) activeLink.classList.add('active');
            
            // Update pagination bullets
            document.querySelectorAll('.pagination-bullet').forEach(bullet => {
                bullet.classList.remove('active');
            });
            
            const activeBullet = document.querySelector(`.pagination-bullet[data-slide="${index}"]`);
            if (activeBullet) activeBullet.classList.add('active');
        }
        
        // Navigation link click events
        document.querySelectorAll('.nav-link, .page-scroll').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const slideIndex = parseInt(link.getAttribute('data-slide'));
                swiper.slideTo(slideIndex);
            });
        });
        
        // Pagination bullets click events
        document.querySelectorAll('.pagination-bullet').forEach(bullet => {
            bullet.addEventListener('click', () => {
                const slideIndex = parseInt(bullet.getAttribute('data-slide'));
                swiper.slideTo(slideIndex);
            });
        });
        
        // Navbar background change on scroll
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                document.querySelector('.navbar').classList.add('scrolled');
            } else {
                document.querySelector('.navbar').classList.remove('scrolled');
            }
        });
        
        // Dark mode toggle functionality
        const darkModeToggle = document.querySelector('.dark-mode-toggle');
        darkModeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-mode');
            const icon = darkModeToggle.querySelector('i');
            
            if (document.body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        });
    </script>
</body>
</html>
