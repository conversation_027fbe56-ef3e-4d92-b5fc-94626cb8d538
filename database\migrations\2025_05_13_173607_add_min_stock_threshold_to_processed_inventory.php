<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('processed_inventory', function (Blueprint $table) {
            $table->integer('min_stock_threshold')->nullable()->default(5);
            // Also add additional columns that might be missing but used in views
            $table->string('name')->nullable()->after('id');
            $table->decimal('cost_per_item', 10, 2)->nullable()->after('cost_per_unit');
            $table->decimal('raw_material_per_item', 10, 2)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('processed_inventory', function (Blueprint $table) {
            $table->dropColumn(['min_stock_threshold', 'name', 'cost_per_item', 'raw_material_per_item']);
        });
    }
};
