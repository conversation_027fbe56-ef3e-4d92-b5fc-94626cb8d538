<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class LogUserActivity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Catat aktivitas pengguna sebelum request diproses
        if (Auth::check()) {
            $user = Auth::user();
            $path = $request->path();
            $method = $request->method();
            $ip = $request->ip();
            $userAgent = $request->userAgent();

            // Log aktivitas ke file log
            Log::info("User Activity: {$user->name} ({$user->id}) accessed {$method} {$path} from {$ip} using {$userAgent}");

            // Simpan waktu aktivitas terakhir pengguna
            $user->last_activity = now();
            $user->save();
        }

        // Proses request
        $response = $next($request);

        // Kembalikan response
        return $response;
    }
}
