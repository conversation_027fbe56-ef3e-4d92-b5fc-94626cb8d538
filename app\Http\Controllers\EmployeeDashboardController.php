<?php

namespace App\Http\Controllers;

use App\Models\OtherProduct;
use App\Models\ProcessedInventory;
use App\Models\RawInventory;
use App\Models\Transaction;
use App\Models\TransactionItem;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class EmployeeDashboardController extends Controller
{
    /**
     * Show the employee dashboard.
     */
    public function index()
    {
        // Middleware employee sudah memastikan hanya karyawan yang bisa mengakses

        // Data untuk ringkasan hari ini
        $today = Carbon::today();
        $todaySales = Transaction::whereDate('created_at', $today)
            ->where('status', 'completed')
            ->sum('total_amount');
        $todayTransactions = Transaction::whereDate('created_at', $today)
            ->where('status', 'completed')
            ->count();

        // Data untuk grafik penjualan 7 hari terakhir
        $lastWeek = collect();
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::today()->subDays($i);
            $revenue = Transaction::whereDate('created_at', $date)
                ->where('status', 'completed')
                ->sum('total_amount');
            $transactions = Transaction::whereDate('created_at', $date)
                ->where('status', 'completed')
                ->count();

            $lastWeek->push([
                'date' => $date,
                'label' => $date->format('d M'),
                'revenue' => $revenue,
                'transactions' => $transactions
            ]);
        }

        // Data untuk produk terlaris
        $topProducts = TransactionItem::select('product_name', DB::raw('SUM(quantity) as total_sold'))
            ->whereHas('transaction', function ($query) {
                $query->where('status', 'completed');
            })
            ->whereDate('created_at', '>=', Carbon::now()->subDays(30))
            ->groupBy('product_name')
            ->orderByDesc('total_sold')
            ->limit(5)
            ->get();

        // Data untuk stok umbi bakar
        $processedInventory = ProcessedInventory::select('id', 'name', 'current_stock', 'min_stock_threshold', 'selling_price')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        // Data untuk stok produk lain
        $otherProducts = OtherProduct::select('id', 'name', 'current_stock', 'min_stock_threshold', 'selling_price')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        // Stok yang hampir habis
        $lowStockProcessed = ProcessedInventory::whereRaw('current_stock <= min_stock_threshold')
            ->where('is_active', true)
            ->get();
        $lowStockOther = OtherProduct::whereRaw('current_stock <= min_stock_threshold')
            ->where('is_active', true)
            ->get();
        $lowStockItems = $lowStockProcessed->concat($lowStockOther);

        // Transaksi terbaru hari ini
        $recentTransactions = Transaction::whereDate('created_at', $today)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Data untuk grafik
        $salesChartData = [
            'labels' => $lastWeek->pluck('label')->toArray(),
            'revenue' => $lastWeek->pluck('revenue')->toArray(),
            'transactions' => $lastWeek->pluck('transactions')->toArray()
        ];

        // Ringkasan untuk dashboard
        $summary = [
            'today_sales_count' => $todayTransactions,
            'today_sales_amount' => $todaySales,
            'processed_inventory_count' => $processedInventory->count(),
            'other_products_count' => $otherProducts->count(),
            'low_stock_count' => $lowStockItems->count()
        ];

        return view('employee.dashboard', compact(
            'summary',
            'lowStockItems',
            'recentTransactions',
            'topProducts',
            'salesChartData',
            'processedInventory',
            'otherProducts'
        ));
    }
}
