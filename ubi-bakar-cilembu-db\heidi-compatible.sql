-- --------------------------------------------------------
-- Host:                         127.0.0.1
-- Server version:               10.4.28-MariaDB - mariadb.org binary distribution
-- Server OS:                    Win64
-- HeidiSQL Version:             12.5.0.6677
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- Dumping database structure for sim_ubi_cilembu
DROP DATABASE IF EXISTS `sim_ubi_cilembu`;
CREATE DATABASE IF NOT EXISTS `sim_ubi_cilembu` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */;
USE `sim_ubi_cilembu`;

-- Dumping structure for table sim_ubi_cilembu.audit_logs
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE IF NOT EXISTS `audit_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `action` varchar(255) NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) unsigned NOT NULL,
  `old_values` text DEFAULT NULL,
  `new_values` text DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `audit_logs_user_id_foreign` (`user_id`),
  KEY `audit_logs_model_type_model_id_index` (`model_type`,`model_id`),
  KEY `audit_logs_action_index` (`action`),
  KEY `audit_logs_created_at_index` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping structure for table sim_ubi_cilembu.migrations
DROP TABLE IF EXISTS `migrations`;
CREATE TABLE IF NOT EXISTS `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table sim_ubi_cilembu.migrations
DELETE FROM `migrations`;
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
	(1, '2014_10_12_000000_create_users_table', 1),
	(2, '2023_05_01_000001_create_raw_inventory_table', 1),
	(3, '2023_05_01_000002_create_processed_inventory_table', 1),
	(4, '2023_05_01_000003_create_other_products_table', 1),
	(5, '2023_05_01_000004_create_transactions_table', 1),
	(6, '2023_05_01_000005_create_transaction_items_table', 1),
	(7, '2023_05_01_000006_create_production_logs_table', 1),
	(8, '2023_05_01_000007_add_foreign_keys', 1),
	(9, '2023_05_01_000008_add_product_type_to_processed_inventory', 1),
	(10, '2023_05_01_000009_add_min_stock_threshold_to_inventory_tables', 1),
	(11, '2023_05_01_000010_add_is_active_to_inventory_tables', 1),
	(12, '2023_05_01_000011_add_cost_per_item_to_processed_inventory', 1),
	(13, '2025_05_17_012205_add_indexes_to_improve_performance', 2),
	(14, '2025_05_17_012215_add_constraints_for_data_integrity', 2),
	(15, '2025_05_17_012225_add_soft_deletes_to_important_tables', 2),
	(16, '2025_05_17_012234_create_audit_logs_table', 2),
	(17, '2025_05_17_012929_add_last_activity_to_users_table', 3),
	(18, '2025_05_17_013228_add_soft_delete_to_users_table', 4),
	(19, '2025_05_17_020120_remove_unused_tables_and_columns', 5);

-- Dumping structure for table sim_ubi_cilembu.other_products
DROP TABLE IF EXISTS `other_products`;
CREATE TABLE IF NOT EXISTS `other_products` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `sku` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `purchase_price` decimal(10,2) NOT NULL,
  `selling_price` decimal(10,2) NOT NULL,
  `current_stock` int(11) NOT NULL DEFAULT 0,
  `min_stock_threshold` int(11) DEFAULT 5,
  `category` varchar(255) DEFAULT NULL,
  `unit` varchar(255) DEFAULT NULL,
  `supplier` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `other_products_sku_unique` (`sku`),
  KEY `idx_other_prod_sku` (`sku`),
  KEY `idx_other_prod_category` (`category`),
  KEY `idx_other_prod_stock` (`current_stock`),
  KEY `idx_other_prod_active_stock` (`is_active`,`current_stock`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping structure for table sim_ubi_cilembu.processed_inventory
DROP TABLE IF EXISTS `processed_inventory`;
CREATE TABLE IF NOT EXISTS `processed_inventory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `batch_number` varchar(255) NOT NULL,
  `raw_inventory_id` bigint(20) unsigned NOT NULL,
  `quantity_processed_kg` decimal(10,2) NOT NULL,
  `quantity_produced` int(11) NOT NULL,
  `cost_per_unit` decimal(10,2) NOT NULL,
  `selling_price` decimal(10,2) NOT NULL,
  `current_stock` int(11) NOT NULL DEFAULT 0,
  `production_date` date NOT NULL,
  `expiry_date` date NOT NULL,
  `product_type` enum('Original','Premium','Special') NOT NULL DEFAULT 'Original',
  `min_stock_threshold` int(11) DEFAULT 10,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `cost_per_item` decimal(10,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `processed_inventory_batch_number_unique` (`batch_number`),
  KEY `processed_inventory_raw_inventory_id_foreign` (`raw_inventory_id`),
  KEY `idx_proc_batch_number` (`batch_number`),
  KEY `idx_proc_production_date` (`production_date`),
  KEY `idx_proc_expiry_date` (`expiry_date`),
  KEY `idx_proc_current_stock` (`current_stock`),
  KEY `idx_proc_active_stock` (`is_active`,`current_stock`),
  KEY `idx_proc_product_type` (`product_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping structure for table sim_ubi_cilembu.production_logs
DROP TABLE IF EXISTS `production_logs`;
CREATE TABLE IF NOT EXISTS `production_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `raw_inventory_id` bigint(20) unsigned NOT NULL,
  `processed_inventory_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `raw_amount_used` decimal(10,2) NOT NULL,
  `produced_amount` int(11) NOT NULL,
  `raw_cost` decimal(10,2) NOT NULL,
  `additional_cost` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_cost` decimal(10,2) NOT NULL,
  `cost_per_item` decimal(10,2) NOT NULL,
  `raw_name` varchar(255) DEFAULT NULL,
  `processed_name` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `production_logs_raw_inventory_id_foreign` (`raw_inventory_id`),
  KEY `production_logs_processed_inventory_id_foreign` (`processed_inventory_id`),
  KEY `production_logs_user_id_foreign` (`user_id`),
  KEY `idx_prod_logs_created` (`created_at`),
  KEY `idx_prod_logs_raw_proc` (`raw_inventory_id`,`processed_inventory_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping structure for table sim_ubi_cilembu.raw_inventory
DROP TABLE IF EXISTS `raw_inventory`;
CREATE TABLE IF NOT EXISTS `raw_inventory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `batch_number` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `supplier` varchar(255) NOT NULL,
  `quantity_kg` decimal(10,2) NOT NULL,
  `cost_per_kg` decimal(10,2) NOT NULL,
  `total_cost` decimal(10,2) NOT NULL,
  `current_stock` decimal(10,2) NOT NULL DEFAULT 0.00,
  `purchase_date` date NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `quality` enum('A','B','C') NOT NULL DEFAULT 'A',
  `min_stock_threshold` decimal(10,2) DEFAULT 50.00,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `raw_inventory_batch_number_unique` (`batch_number`),
  KEY `idx_raw_batch_number` (`batch_number`),
  KEY `idx_raw_purchase_date` (`purchase_date`),
  KEY `idx_raw_expiry_date` (`expiry_date`),
  KEY `idx_raw_current_stock` (`current_stock`),
  KEY `idx_raw_active_stock` (`is_active`,`current_stock`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping structure for table sim_ubi_cilembu.transaction_items
DROP TABLE IF EXISTS `transaction_items`;
CREATE TABLE IF NOT EXISTS `transaction_items` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `transaction_id` bigint(20) unsigned NOT NULL,
  `product_id` bigint(20) unsigned DEFAULT NULL,
  `processed_inventory_id` bigint(20) unsigned DEFAULT NULL,
  `product_name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `discount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `subtotal` decimal(10,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `transaction_items_transaction_id_foreign` (`transaction_id`),
  KEY `transaction_items_product_id_foreign` (`product_id`),
  KEY `transaction_items_processed_inventory_id_foreign` (`processed_inventory_id`),
  KEY `idx_trans_items_trans_prod` (`transaction_id`,`product_id`),
  KEY `idx_trans_items_trans_proc` (`transaction_id`,`processed_inventory_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping structure for table sim_ubi_cilembu.transactions
DROP TABLE IF EXISTS `transactions`;
CREATE TABLE IF NOT EXISTS `transactions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `customer_name` varchar(255) DEFAULT NULL,
  `customer_phone` varchar(255) DEFAULT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `tax` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `change_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` enum('cash','transfer','qris','debit','credit') NOT NULL DEFAULT 'cash',
  `status` enum('completed','cancelled','refunded') NOT NULL DEFAULT 'completed',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transactions_invoice_number_unique` (`invoice_number`),
  KEY `transactions_user_id_foreign` (`user_id`),
  KEY `idx_trans_invoice` (`invoice_number`),
  KEY `idx_trans_created_at` (`created_at`),
  KEY `idx_trans_status` (`status`),
  KEY `idx_trans_payment` (`payment_method`),
  KEY `idx_trans_user_date` (`user_id`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping structure for table sim_ubi_cilembu.users
DROP TABLE IF EXISTS `users`;
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','employee') NOT NULL DEFAULT 'employee',
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_activity` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table sim_ubi_cilembu.users
DELETE FROM `users`;
INSERT INTO `users` (`id`, `name`, `email`, `password`, `role`, `remember_token`, `created_at`, `updated_at`, `last_activity`, `deleted_at`) VALUES
	(1, 'Admin UBI', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', NULL, '2023-05-13 14:01:00', '2023-05-13 14:01:00', NULL, NULL),
	(2, 'Karyawan Toko', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'employee', NULL, '2023-05-13 14:01:00', '2023-05-13 14:01:00', NULL, NULL);

-- Add foreign key constraints
ALTER TABLE `processed_inventory` ADD CONSTRAINT `processed_inventory_raw_inventory_id_foreign` FOREIGN KEY (`raw_inventory_id`) REFERENCES `raw_inventory` (`id`);
ALTER TABLE `production_logs` ADD CONSTRAINT `production_logs_processed_inventory_id_foreign` FOREIGN KEY (`processed_inventory_id`) REFERENCES `processed_inventory` (`id`);
ALTER TABLE `production_logs` ADD CONSTRAINT `production_logs_raw_inventory_id_foreign` FOREIGN KEY (`raw_inventory_id`) REFERENCES `raw_inventory` (`id`);
ALTER TABLE `production_logs` ADD CONSTRAINT `production_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
ALTER TABLE `transaction_items` ADD CONSTRAINT `transaction_items_processed_inventory_id_foreign` FOREIGN KEY (`processed_inventory_id`) REFERENCES `processed_inventory` (`id`);
ALTER TABLE `transaction_items` ADD CONSTRAINT `transaction_items_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `other_products` (`id`);
ALTER TABLE `transaction_items` ADD CONSTRAINT `transaction_items_transaction_id_foreign` FOREIGN KEY (`transaction_id`) REFERENCES `transactions` (`id`) ON DELETE CASCADE;
ALTER TABLE `transactions` ADD CONSTRAINT `transactions_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
ALTER TABLE `audit_logs` ADD CONSTRAINT `audit_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
