<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Menambahkan kolom raw_material_per_item ke tabel processed_inventory jika belum ada
        if (!Schema::hasColumn('processed_inventory', 'raw_material_per_item')) {
            Schema::table('processed_inventory', function (Blueprint $table) {
                $table->decimal('raw_material_per_item', 10, 2)->nullable()->after('selling_price');
            });
        }

        // Memperbaiki tabel other_products jika kolom notes tidak ada
        if (!Schema::hasColumn('other_products', 'notes')) {
            Schema::table('other_products', function (Blueprint $table) {
                $table->text('notes')->nullable()->after('is_active');
            });
        }

        // Memperbaiki kolom notes pada raw_inventory jika perlu
        if (!Schema::hasColumn('raw_inventory', 'notes')) {
            Schema::table('raw_inventory', function (Blueprint $table) {
                $table->text('notes')->nullable()->after('quality');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Menghapus kolom raw_material_per_item dari tabel processed_inventory jika ada
        if (Schema::hasColumn('processed_inventory', 'raw_material_per_item')) {
            Schema::table('processed_inventory', function (Blueprint $table) {
                $table->dropColumn('raw_material_per_item');
            });
        }

        // Menghapus kolom notes dari tabel other_products jika ada
        if (Schema::hasColumn('other_products', 'notes')) {
            Schema::table('other_products', function (Blueprint $table) {
                $table->dropColumn('notes');
            });
        }

        // Tidak menghapus kolom notes dari raw_inventory karena sudah ada di migrasi asli
    }
};
