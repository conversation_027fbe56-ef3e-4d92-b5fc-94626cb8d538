@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-user me-2"></i> Detail User: {{ $user->name }}</h1>
        <div>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Information -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user me-2"></i> Informasi User</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>Nama Lengkap:</strong></td>
                                    <td>{{ $user->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ $user->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Role:</strong></td>
                                    <td>
                                        @if($user->role === 'admin')
                                            <span class="badge bg-danger">Admin</span>
                                        @elseif($user->role === 'employee')
                                            <span class="badge bg-primary">Karyawan</span>
                                        @elseif($user->role === 'cashier')
                                            <span class="badge bg-success">Kasir</span>
                                        @elseif($user->role === 'warehouse')
                                            <span class="badge bg-warning">Gudang</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if($user->status === 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @elseif($user->status === 'approved')
                                            <span class="badge bg-success">Approved</span>
                                        @elseif($user->status === 'rejected')
                                            <span class="badge bg-danger">Rejected</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>Tanggal Daftar:</strong></td>
                                    <td>{{ $user->created_at->format('d/m/Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Terakhir Update:</strong></td>
                                    <td>{{ $user->updated_at->format('d/m/Y H:i') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Terakhir Aktif:</strong></td>
                                    <td>
                                        @if($user->last_activity)
                                            {{ $user->last_activity->format('d/m/Y H:i') }}
                                        @else
                                            <span class="text-muted">Belum pernah login</span>
                                        @endif
                                    </td>
                                </tr>
                                @if($user->approver)
                                <tr>
                                    <td><strong>Disetujui Oleh:</strong></td>
                                    <td>
                                        {{ $user->approver->name }}
                                        <br><small class="text-muted">{{ $user->approved_at?->format('d/m/Y H:i') }}</small>
                                    </td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs me-2"></i> Aksi</h5>
                </div>
                <div class="card-body">
                    @if($user->status === 'pending')
                        <div class="d-grid gap-2">
                            <form method="POST" action="{{ route('admin.users.approve', $user) }}">
                                @csrf
                                <button type="submit" class="btn btn-success w-100" 
                                        onclick="return confirm('Setujui user ini?')">
                                    <i class="fas fa-check"></i> Setujui User
                                </button>
                            </form>
                            
                            <form method="POST" action="{{ route('admin.users.reject', $user) }}">
                                @csrf
                                <button type="submit" class="btn btn-danger w-100" 
                                        onclick="return confirm('Tolak user ini?')">
                                    <i class="fas fa-times"></i> Tolak User
                                </button>
                            </form>
                        </div>
                        <hr>
                    @endif
                    
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit User
                        </a>
                        
                        @if($user->id !== auth()->id())
                            <form method="POST" action="{{ route('admin.users.destroy', $user) }}">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger w-100" 
                                        onclick="return confirm('Hapus user ini? Aksi ini tidak dapat dibatalkan!')">
                                    <i class="fas fa-trash"></i> Hapus User
                                </button>
                            </form>
                        @else
                            <button class="btn btn-danger w-100" disabled>
                                <i class="fas fa-trash"></i> Tidak dapat menghapus akun sendiri
                            </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar me-2"></i> Statistik</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <div class="border rounded p-2">
                                <h4 class="text-primary mb-0">{{ $user->approvedUsers->count() }}</h4>
                                <small class="text-muted">User yang Disetujui</small>
                            </div>
                        </div>
                    </div>
                    
                    @if($user->approvedUsers->count() > 0)
                        <hr>
                        <h6>User yang Pernah Disetujui:</h6>
                        <ul class="list-unstyled">
                            @foreach($user->approvedUsers->take(5) as $approvedUser)
                                <li class="mb-1">
                                    <small>
                                        <i class="fas fa-user me-1"></i>
                                        {{ $approvedUser->name }}
                                        <span class="text-muted">({{ $approvedUser->approved_at?->format('d/m/Y') }})</span>
                                    </small>
                                </li>
                            @endforeach
                            @if($user->approvedUsers->count() > 5)
                                <li><small class="text-muted">... dan {{ $user->approvedUsers->count() - 5 }} lainnya</small></li>
                            @endif
                        </ul>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
