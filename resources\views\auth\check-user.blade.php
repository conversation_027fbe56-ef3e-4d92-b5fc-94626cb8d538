<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check User Database - Ubi Bakar Cilembu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 30px;
            font-family: Arial, sans-serif;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: 1px solid #e9ecef;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .user-info {
            background-color: #e9f7ef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .password-info {
            background-color: #f8f9fa;
            padding: 10px;
            font-family: monospace;
        }
        .btn-fix {
            background-color: #8B4513;
            color: white;
            border: none;
        }
        .btn-fix:hover {
            background-color: #6B3010;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Database User Check</h1>
        
        <div class="card">
            <div class="card-header">Database Connection</div>
            <div class="card-body">
                @php
                try {
                    $pdo = DB::connection()->getPdo();
                    $databaseName = DB::connection()->getDatabaseName();
                    $connectionStatus = "Connected successfully to database: <strong>" . $databaseName . "</strong>";
                    $connectionClass = "text-success";
                } catch (\Exception $e) {
                    $connectionStatus = "Failed to connect to database: " . $e->getMessage();
                    $connectionClass = "text-danger";
                }
                @endphp
                
                <p class="{{ $connectionClass }}">{!! $connectionStatus !!}</p>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">Users Table</div>
            <div class="card-body">
                @php
                try {
                    $users = DB::table('users')->get();
                    $usersCount = count($users);
                } catch (\Exception $e) {
                    $usersCount = "Error: " . $e->getMessage();
                    $users = [];
                }
                @endphp
                
                <p>Total users in database: <strong>{{ $usersCount }}</strong></p>
                
                @if(is_numeric($usersCount) && $usersCount > 0)
                    @foreach($users as $user)
                        <div class="user-info">
                            <h5>{{ $user->name }} @if($user->role == 'karyawan')<span class="badge bg-primary">Karyawan</span>@else<span class="badge bg-danger">Admin</span>@endif</h5>
                            <p><strong>Email:</strong> {{ $user->email }}</p>
                            <p><strong>Role:</strong> {{ $user->role }}</p>
                            <div class="password-info">
                                <p class="mb-0"><strong>Password Hash:</strong> {{ substr($user->password, 0, 30) }}...</p>
                            </div>
                        </div>
                    @endforeach
                @endif
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">Create Test Users</div>
            <div class="card-body">
                <form method="POST" action="/create-test-users">
                    @csrf
                    <button type="submit" class="btn btn-fix">Create/Update Test Users</button>
                </form>
                
                <div class="mt-3">
                    <p><strong>This will create:</strong></p>
                    <ul>
                        <li>Admin user (<EMAIL> / password)</li>
                        <li>Karyawan user (<EMAIL> / karyawan123)</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="{{ route('karyawan.login.form') }}" class="btn btn-primary">Go to Karyawan Login Page</a>
            <a href="{{ route('login') }}" class="btn btn-secondary ms-2">Go to Main Login Page</a>
        </div>
    </div>
</body>
</html> 