# 📊 DIAGRAM COLLECTION - SISTEM UBI BAKAR CILEMBU

## 🎯 **Overview**

Koleksi diagram lengkap untuk Sistem Informasi Manajemen Ubi Bakar Cilembu yang mencakup semua aspek sistem dari use case hingga implementasi database. Semua diagram dibuat berdasarkan analisis mendalam terhadap kode Laravel yang sudah diimplementasi.

---

## 📋 **Daftar Diagram Lengkap**

### **1. 🎭 Use Case Diagram**
- **File**: `usecase-diagram.md`
- **Deskripsi**: Menggambarkan interaksi antara aktor (Admin, Karyawan) dengan sistem
- **Aktor**: Admin, Karyawan
- **Use Cases**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Dashboard, Akses Transaksi, <PERSON><PERSON><PERSON> Inventory, <PERSON><PERSON><PERSON>, Lo<PERSON>
- **Format**: Mermaid diagram

### **2. 🔄 Activity Diagram**
- **File**: `activity-diagram.md` + `activity-diagram.bpmn`
- **Format**: BPMN.io compatible XML + Mermaid
- **Deskripsi**: <PERSON><PERSON> proses bisnis dalam sistem dengan 3 swimlanes
- **Proses**: Authentication flow, Role-based access, Menu navigation
- **Import**: Dapat diimport langsung ke https://bpmn.io/

### **3. 🔄 Sequence Diagram**
- **File**: `sequence-diagram.md`
- **Deskripsi**: Interaksi antar objek dalam sistem dengan detail komunikasi
- **Skenario**:
  - Authentication & Login Process
  - Transaction Creation (POS System)
  - Inventory Management
  - Report Generation
  - Logout Process
- **Format**: Mermaid sequence diagrams

### **4. 🏗️ Class Diagram**
- **File**: `class-diagram.md`
- **Deskripsi**: Struktur kelas dan relasi berdasarkan Laravel Models
- **Classes**:
  - User Management: User
  - Transaction: Transaction, TransactionItem
  - Inventory: RawInventory, ProcessedInventory, OtherProduct
  - Production: ProductionProcess, ProductionLog
  - Distribution: Distribution, DistributionItem
  - Supporting: Supplier, Expense, AuditLog
- **Relationships**: Detailed dengan cardinality
- **Format**: Mermaid class diagram

### **5. 🗄️ ERD (Entity Relationship Diagram)**
- **File**: `erd-diagram.md`
- **Deskripsi**: Struktur database lengkap dengan semua tabel dan relasi
- **Tables**: 15+ tables dengan complete schema
- **Features**:
  - Primary Keys & Foreign Keys
  - Unique Constraints
  - Data Types (ENUM, JSON, DECIMAL, etc.)
  - Indexes & Performance optimization
  - Soft Deletes & Timestamps
- **Database**: MySQL/MariaDB compatible
- **Format**: Mermaid ERD

### **6. 🏛️ System Architecture Diagram**
- **File**: `system-architecture-diagram.md`
- **Deskripsi**: Arsitektur sistem secara keseluruhan
- **Layers**: User, Presentation, Application, Service, External, Data, Infrastructure
- **Format**: Mermaid architecture diagram

---

## 🛠️ **Tools & Format**

### **Mermaid Diagrams**
- Semua diagram menggunakan format Mermaid
- Compatible dengan GitHub, GitLab, dan platform lainnya
- Dapat di-render secara real-time
- Support untuk VS Code dengan Mermaid extension

### **BPMN Format**
- Activity diagram tersedia dalam format BPMN.io
- File: `activity-diagram.bpmn`
- Standard BPMN 2.0 XML format
- Dapat diimport langsung ke BPMN.io editor
- Support untuk Camunda Modeler

---

## 📖 **Cara Penggunaan**

### **1. Viewing Diagrams**
```bash
# Navigate to diagram folder
cd "new diagram yang baru saya buat"

# Open any .md file to view diagrams
```

### **2. Editing BPMN Diagram**
1. Buka https://bpmn.io/
2. Klik "Open File" atau drag & drop
3. Upload file `activity-diagram.bpmn`
4. Edit sesuai kebutuhan
5. Export kembali dalam format .bpmn

### **3. Rendering Mermaid**
```markdown
# Dalam file markdown
```mermaid
[diagram code here]
```
```

---

## 🎯 **Fitur Sistem yang Dicakup**

### **🔐 Authentication & Authorization**
- Multi-role system (Admin, Karyawan)
- Laravel Auth dengan custom middleware
- Session management dengan last activity tracking
- Role-based access control

### **💰 Transaction Management**
- POS System dengan real-time interface
- Payment Gateway Integration (Midtrans)
- Multiple payment methods (Cash, Transfer, QRIS, Gateway)
- Auto invoice generation
- Receipt generation & printing

### **📦 Inventory Management**
- **Raw Inventory**: Ubi mentah dengan supplier tracking
- **Processed Inventory**: Ubi bakar dengan expiry management
- **Other Products**: Minuman, snack dengan category management
- Stock monitoring & low stock alerts

### **🏭 Production System**
- Production process tracking
- Raw material to processed conversion
- Cost calculation per unit
- Quality grade management

### **🚚 Distribution System**
- Market distribution management
- Return tracking & sales monitoring
- Distribution item details

### **📊 Reporting System**
- Sales reports dengan date range filtering
- Financial reports
- Inventory reports
- Export functionality (PDF, Excel)

---

## 📝 **Technical Specifications**

### **Backend**
- **Framework**: Laravel 11
- **Database**: MySQL/MariaDB
- **Authentication**: Laravel Auth dengan custom middleware
- **Payment**: Midtrans Snap Integration
- **Architecture**: MVC Pattern

### **Frontend**
- **Framework**: Blade Templates
- **CSS**: Bootstrap 5 dengan custom styling
- **JavaScript**: Vanilla JS + jQuery
- **Charts**: Chart.js untuk reporting
- **POS**: Real-time interface dengan cart management

### **Database**
- **Engine**: MySQL/MariaDB
- **Features**: Foreign Keys, Indexes, Soft Deletes
- **Data Types**: ENUM, JSON, DECIMAL untuk precision
- **Performance**: Query optimization dengan proper indexing

---

## 🔗 **Key Relationships**

### **User Relationships**
- User → Transactions (1:N)
- User → Production Processes (1:N)
- User → Distributions (1:N)
- User → Expenses (1:N)

### **Transaction Relationships**
- Transaction → Transaction Items (1:N)
- Processed Inventory → Transaction Items (1:N)
- Other Products → Transaction Items (1:N)

### **Inventory Relationships**
- Raw Inventory → Processed Inventory (1:N)
- Suppliers → Raw Inventory (1:N)

---

## 📊 **Database Statistics**

- **Total Tables**: 15+ tables
- **Primary Keys**: Auto-increment bigint
- **Foreign Keys**: Proper referential integrity
- **Indexes**: Optimized untuk performance
- **Features**: Soft deletes, timestamps, audit logging

---

## 🎯 **Implementation Guidelines**

### **1. Development Process**
1. **Use Case Analysis**: Gunakan use case diagram sebagai requirement
2. **Database Design**: Implement ERD sebagai database schema
3. **Class Structure**: Follow class diagram untuk model relationships
4. **Business Logic**: Implement activity diagram flows
5. **API Design**: Use sequence diagram untuk interaction patterns

### **2. Testing Strategy**
- **Unit Tests**: Test individual model methods
- **Feature Tests**: Test complete use case flows
- **Integration Tests**: Test payment gateway integration
- **Performance Tests**: Test database query optimization

---

## 📚 **Documentation Standards**

### **Code Documentation**
- PHPDoc untuk semua methods
- Inline comments untuk complex logic
- README untuk setiap module

### **Database Documentation**
- Table descriptions
- Column specifications
- Relationship explanations

---

## 🎯 **Next Steps**

1. **Implementation**: Use diagrams sebagai reference untuk development
2. **Testing**: Create comprehensive test cases berdasarkan use cases
3. **Documentation**: Maintain diagrams seiring evolusi sistem
4. **Deployment**: Use architecture diagram untuk infrastructure setup

---

**📊 Diagram Status:** Complete & Production Ready
**🔄 Last Updated:** December 2024
**📱 Compatibility:** All modern browsers & development tools
**🔐 Security:** Production ready dengan comprehensive security measures
**🎯 Coverage:** 100% system functionality covered
