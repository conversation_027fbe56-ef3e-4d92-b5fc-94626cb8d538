@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-truck me-2"></i> Laporan Distribusi</h1>
        <div>
            <a href="{{ route('expiry-recommendations.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter me-2"></i> Filter Laporan</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('distributions.report') }}">
                <div class="row">
                    <div class="col-md-4">
                        <label for="start_date" class="form-label"><PERSON><PERSON></label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ $startDate }}">
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">Tanggal Akhir</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" 
                               value="{{ $endDate }}">
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Distribusi</h6>
                            <h3>{{ $totalDistributed }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-truck fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Item</h6>
                            <h3>{{ number_format($totalItems) }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Nilai</h6>
                            <h3>Rp {{ number_format($totalValue, 0, ',', '.') }}</h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Periode</h6>
                            <h6>{{ \Carbon\Carbon::parse($startDate)->format('d/m/Y') }} - {{ \Carbon\Carbon::parse($endDate)->format('d/m/Y') }}</h6>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Distribution Table -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-table me-2"></i> Pergerakan Distribusi Produk</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>No</th>
                            <th>Tanggal</th>
                            <th>No. Distribusi</th>
                            <th>Tujuan/Pasar</th>
                            <th>Produk</th>
                            <th>Jumlah</th>
                            <th>Status</th>
                            <th>Penanggung Jawab</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($distributions as $index => $distribution)
                            @foreach($distribution->items as $itemIndex => $item)
                                <tr>
                                    <td>{{ $loop->parent->iteration }}.{{ $loop->iteration }}</td>
                                    <td>{{ $distribution->distribution_date->format('d/m/Y') }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $distribution->distribution_number }}</span>
                                    </td>
                                    <td>
                                        <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                        <strong>{{ $distribution->market_name ?? $distribution->destination ?? 'Pasar Tradisional' }}</strong>
                                    </td>
                                    <td>
                                        @if($item->processedInventory)
                                            {{ $item->processedInventory->name }}
                                            <br><small class="text-muted">Batch: {{ $item->processedInventory->batch_number }}</small>
                                        @elseif($item->otherProduct)
                                            {{ $item->otherProduct->name }}
                                        @else
                                            {{ $item->product_name ?? 'Produk Ubi' }}
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-success">{{ number_format($item->quantity) }} unit</span>
                                    </td>
                                    <td>
                                        @if($distribution->status === 'planned')
                                            <span class="badge bg-info">Direncanakan</span>
                                        @elseif($distribution->status === 'in_transit')
                                            <span class="badge bg-warning">Dalam Perjalanan</span>
                                        @elseif($distribution->status === 'delivered')
                                            <span class="badge bg-success">Terkirim</span>
                                        @else
                                            <span class="badge bg-secondary">{{ ucfirst($distribution->status) }}</span>
                                        @endif
                                    </td>
                                    <td>{{ $distribution->user->name ?? 'Admin' }}</td>
                                </tr>
                            @endforeach
                        @empty
                            <tr>
                                <td colspan="8" class="text-center text-muted">
                                    <i class="fas fa-inbox fa-2x mb-2"></i>
                                    <br>Tidak ada data distribusi pada periode ini
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @if($distributions->count() > 0)
    <!-- Chart Section -->
    <div class="card mt-4">
        <div class="card-header">
            <h5><i class="fas fa-chart-line me-2"></i> Grafik Distribusi Harian</h5>
        </div>
        <div class="card-body">
            <canvas id="distributionChart" height="100"></canvas>
        </div>
    </div>
    @endif
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if($distributions->count() > 0)
    // Distribution Chart
    const ctx = document.getElementById('distributionChart').getContext('2d');
    const labels = @json($labels);
    const data = @json($data);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Jumlah Distribusi',
                data: data,
                backgroundColor: 'rgba(139, 69, 19, 0.2)',
                borderColor: 'rgba(139, 69, 19, 1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Distribusi: ' + context.parsed.y + ' kali';
                        }
                    }
                }
            }
        }
    });
    @endif
});
</script>
@endpush
@endsection
