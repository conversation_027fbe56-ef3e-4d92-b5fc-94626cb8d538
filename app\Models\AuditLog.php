<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AuditLog extends Model
{
    use HasFactory;

    protected $table = 'audit_logs';

    protected $fillable = [
        'user_id',
        'action',
        'model_type',
        'model_id',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent'
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
    ];

    /**
     * Get the user that performed the action.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the model that was changed.
     */
    public function auditable()
    {
        return $this->morphTo('model');
    }

    /**
     * Create a new audit log entry.
     *
     * @param string $action The action performed (create, update, delete, restore)
     * @param Model $model The model that was changed
     * @param array|null $oldValues The old values (for update, delete)
     * @param array|null $newValues The new values (for create, update)
     * @return AuditLog
     */
    public static function log(string $action, Model $model, ?array $oldValues = null, ?array $newValues = null): AuditLog
    {
        $userId = auth()->id();
        $request = request();

        return static::create([
            'user_id' => $userId,
            'action' => $action,
            'model_type' => get_class($model),
            'model_id' => $model->getKey(),
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'ip_address' => $request?->ip(),
            'user_agent' => $request?->userAgent(),
        ]);
    }
}
