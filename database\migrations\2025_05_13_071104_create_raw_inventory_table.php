<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('raw_inventory', function (Blueprint $table) {
            $table->id();
            $table->string('batch_number')->unique(); // Nomor batch stok masuk
            $table->string('supplier_name')->nullable(); // Nama pemasok
            $table->decimal('quantity_kg', 10, 2); // Jumlah stok dalam kg
            $table->decimal('cost_per_kg', 10, 2); // Harga per kg
            $table->decimal('total_cost', 12, 2); // Total biaya
            $table->date('purchase_date'); // Tanggal pembelian
            $table->date('expiry_date')->nullable(); // Tanggal kadaluarsa
            $table->enum('quality', ['A', 'B', 'C']); // Kualitas ubi
            $table->text('notes')->nullable(); // Catatan tambahan
            $table->decimal('current_stock', 10, 2); // Sisa stok saat ini
            $table->boolean('is_active')->default(true); // Status aktif
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('raw_inventory');
    }
};
