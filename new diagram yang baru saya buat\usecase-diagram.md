# 🎯 USE CASE DIAGRAM - SISTEM UBI BAKAR CILEMBU

## Diagram Mermaid

```mermaid
graph TB
    %% Actors
    Admin[👨‍💼 Admin]
    Employee[👨‍💻 Employee/<PERSON><PERSON><PERSON>]
    Customer[👤 Customer]
    PaymentGateway[💳 Payment Gateway]
    
    %% Use Cases - Authentication
    subgraph "🔐 Authentication System"
        UC1[Login ke Sistem]
        UC2[Logout dari Sistem]
        UC3[Kelola User Account]
        UC4[Role Management]
    end
    
    %% Use Cases - Inventory Management
    subgraph "📦 Inventory Management"
        UC5[Kelola Raw Inventory]
        UC6[Kelola Processed Inventory]
        UC7[Kelola Other Products]
        UC8[Monitor Low Stock Alert]
        UC9[Process Raw to Processed]
        UC10[View Inventory Reports]
        UC11[Manage Suppliers]
        UC12[Track Expiry Dates]
    end
    
    %% Use Cases - Transaction/POS
    subgraph "💰 Transaction & POS System"
        UC13[Create New Transaction]
        UC14[Process Payment]
        UC15[Print Digital Receipt]
        UC16[View Transaction History]
        UC17[Cancel Transaction]
        UC18[Export Transaction Data]
        UC19[Handle Multiple Payment Methods]
    end
    
    %% Use Cases - Production
    subgraph "🏭 Production Management"
        UC20[Plan Production Batch]
        UC21[Monitor Production Process]
        UC22[Quality Control Check]
        UC23[Production Cost Calculation]
        UC24[Generate Production Reports]
    end
    
    %% Use Cases - Distribution
    subgraph "🚚 Distribution Management"
        UC25[Plan Market Distribution]
        UC26[Track Delivery Status]
        UC27[Record Sales per Market]
        UC28[Handle Product Returns]
        UC29[Distribution Analytics]
    end
    
    %% Use Cases - Financial Reports & Analytics
    subgraph "📊 Financial & Analytics"
        UC30[View Admin Dashboard]
        UC31[View Employee Dashboard]
        UC32[Generate Financial Reports]
        UC33[View Sales Analytics]
        UC34[Monitor KPI Metrics]
        UC35[Export Reports PDF/Excel]
        UC36[Financial Projections]
    end
    
    %% Use Cases - Payment Gateway Integration
    subgraph "💳 Payment Gateway"
        UC37[Process Online Payment]
        UC38[Handle Payment Callback]
        UC39[Verify Payment Status]
        UC40[Handle Payment Failure]
    end
    
    %% Admin Relationships (Full Access)
    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Admin --> UC4
    Admin --> UC5
    Admin --> UC6
    Admin --> UC7
    Admin --> UC8
    Admin --> UC9
    Admin --> UC10
    Admin --> UC11
    Admin --> UC12
    Admin --> UC13
    Admin --> UC14
    Admin --> UC15
    Admin --> UC16
    Admin --> UC17
    Admin --> UC18
    Admin --> UC19
    Admin --> UC20
    Admin --> UC21
    Admin --> UC22
    Admin --> UC23
    Admin --> UC24
    Admin --> UC25
    Admin --> UC26
    Admin --> UC27
    Admin --> UC28
    Admin --> UC29
    Admin --> UC30
    Admin --> UC32
    Admin --> UC33
    Admin --> UC34
    Admin --> UC35
    Admin --> UC36
    Admin --> UC37
    Admin --> UC38
    Admin --> UC39
    Admin --> UC40
    
    %% Employee Relationships (Limited Access)
    Employee --> UC1
    Employee --> UC2
    Employee --> UC8
    Employee --> UC10
    Employee --> UC13
    Employee --> UC14
    Employee --> UC15
    Employee --> UC16
    Employee --> UC19
    Employee --> UC31
    Employee --> UC33
    Employee --> UC37
    
    %% Customer Relationships (Indirect through Employee/Admin)
    Customer -.-> UC13
    Customer -.-> UC14
    Customer -.-> UC37
    
    %% Payment Gateway Relationships (System Integration)
    PaymentGateway --> UC38
    PaymentGateway --> UC39
    PaymentGateway --> UC40
    
    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef usecase fill:#f3e5f5,stroke:#4a148c,stroke-width:1px,color:#000
    classDef system fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000
    
    class Admin,Employee,Customer,PaymentGateway actor
```

## 📋 Deskripsi Use Cases

### 🔐 **Authentication System**
- **UC1-UC4**: Sistem login multi-role dengan keamanan tinggi

### 📦 **Inventory Management** 
- **UC5-UC12**: Manajemen lengkap inventory dari raw material hingga finished goods

### 💰 **Transaction & POS**
- **UC13-UC19**: Sistem POS modern dengan multiple payment methods

### 🏭 **Production Management**
- **UC20-UC24**: Manajemen produksi dari planning hingga quality control

### 🚚 **Distribution Management**
- **UC25-UC29**: Distribusi ke multiple market dengan tracking lengkap

### 📊 **Financial & Analytics**
- **UC30-UC36**: Dashboard dan reporting comprehensive

### 💳 **Payment Gateway**
- **UC37-UC40**: Integrasi Midtrans dengan 15+ payment methods

## 👥 **Actor Descriptions**

### **Admin** 
- Full system access
- Dapat mengakses semua fitur
- Mengelola user dan system configuration

### **Employee/Karyawan**
- Limited operational access  
- Fokus pada POS dan basic inventory
- Tidak dapat mengakses financial reports detail

### **Customer**
- Indirect interaction melalui POS
- Menerima receipt dan melakukan pembayaran

### **Payment Gateway**
- External system integration
- Handle payment processing dan callbacks
