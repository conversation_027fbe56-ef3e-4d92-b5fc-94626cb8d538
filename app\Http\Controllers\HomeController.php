<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard based on user role.
     *
     * @return \Illuminate\Contracts\Support\Renderable|\Illuminate\Http\RedirectResponse
     */
    public function index()
    {
        $user = Auth::user();

        // Display welcome message
        session()->flash('success', "Selamat datang, {$user->name}!");

        // Handle redirection based on role
        if ($user->isAdmin()) {
            return redirect()->route('dashboard');
        } elseif ($user->isEmployee()) {
            return redirect()->route('employee.dashboard');
        }

        // Default dashboard for any other role
        return view('home');
    }
}
