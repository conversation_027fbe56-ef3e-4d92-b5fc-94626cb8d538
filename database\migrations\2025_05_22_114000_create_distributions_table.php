<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('distributions', function (Blueprint $table) {
            $table->id();
            $table->string('distribution_number')->unique();
            $table->foreignId('user_id')->constrained();
            $table->string('market_name');
            $table->date('distribution_date');
            $table->text('notes')->nullable();
            $table->enum('status', ['planned', 'in_transit', 'delivered', 'returned'])->default('planned');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('distribution_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('distribution_id')->constrained();
            $table->foreignId('processed_inventory_id')->nullable();
            $table->foreignId('other_product_id')->nullable();
            $table->integer('quantity');
            $table->decimal('price_per_item', 10, 2);
            $table->decimal('total_price', 12, 2);
            $table->timestamps();
            $table->softDeletes();

            // Ensure either processed_inventory_id or other_product_id is set, but not both
            $table->foreign('processed_inventory_id')->references('id')->on('processed_inventory')->onDelete('set null');
            $table->foreign('other_product_id')->references('id')->on('other_products')->onDelete('set null');
            
            // Note: Check constraint is not supported in some Laravel versions
            // We'll enforce this in the application logic instead
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('distribution_items');
        Schema::dropIfExists('distributions');
    }
};
