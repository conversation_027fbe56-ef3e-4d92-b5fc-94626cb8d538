<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Distribution;
use App\Models\DistributionItem;
use App\Models\ProcessedInventory;
use Carbon\Carbon;

class SampleDistributionSeeder extends Seeder
{
    public function run(): void
    {
        // Get some processed inventory items
        $processedItems = ProcessedInventory::take(3)->get();

        if ($processedItems->isEmpty()) {
            $this->command->info('No processed inventory found. Creating sample data...');
            return;
        }

        // Create sample distributions
        $markets = [
            'Pasar Induk Kramat Jati',
            'Pasar <PERSON>en',
            'Pasar <PERSON>bang',
            '<PERSON><PERSON>',
            'Pasar <PERSON>'
        ];

        for ($i = 0; $i < 10; $i++) {
            $distribution = Distribution::create([
                'distribution_number' => 'DIST-' . date('Ymd') . str_pad($i + 1, 3, '0', STR_PAD_LEFT),
                'user_id' => 1,
                'destination' => $markets[array_rand($markets)],
                'market_name' => $markets[array_rand($markets)],
                'distribution_date' => Carbon::now()->subDays(rand(0, 30)),
                'notes' => 'Sample distribusi ubi bakar ke pasar',
                'status' => ['planned', 'in_transit', 'delivered'][array_rand(['planned', 'in_transit', 'delivered'])],
                'is_urgent' => rand(0, 1) ? true : false
            ]);

            // Create distribution items
            foreach ($processedItems->random(rand(1, 2)) as $item) {
                $quantity = rand(20, 100);
                $pricePerItem = rand(12000, 18000);

                DistributionItem::create([
                    'distribution_id' => $distribution->id,
                    'processed_inventory_id' => $item->id,
                    'quantity' => $quantity,
                    'price_per_item' => $pricePerItem,
                    'total_price' => $quantity * $pricePerItem
                ]);
            }
        }

        $this->command->info('Sample distribution data created successfully!');
    }
}
