<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class FinancialReportExport implements FromCollection, WithHeadings, WithTitle, WithStyles
{
    protected $startDate;
    protected $endDate;
    protected $revenue;
    protected $cogs;
    protected $expenses;
    protected $totalExpenses;
    protected $grossProfit;
    protected $netProfit;

    /**
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param float $revenue
     * @param float $cogs
     * @param Collection $expenses
     * @param float $totalExpenses
     * @param float $grossProfit
     * @param float $netProfit
     */
    public function __construct(
        $startDate,
        $endDate,
        $revenue,
        $cogs,
        $expenses,
        $totalExpenses,
        $grossProfit,
        $netProfit
    ) {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->revenue = $revenue;
        $this->cogs = $cogs;
        $this->expenses = $expenses;
        $this->totalExpenses = $totalExpenses;
        $this->grossProfit = $grossProfit;
        $this->netProfit = $netProfit;
    }

    /**
     * @return Collection
     */
    public function collection()
    {
        $data = new Collection();

        // Header information
        $data->push([
            'Laporan Laba Rugi',
            '',
            '',
        ]);
        
        $data->push([
            'Periode',
            $this->startDate->format('d/m/Y') . ' - ' . $this->endDate->format('d/m/Y'),
            '',
        ]);
        
        $data->push(['', '', '']);

        // Revenue section
        $data->push([
            'Pendapatan',
            '',
            number_format($this->revenue, 0, ',', '.'),
        ]);
        
        // COGS section
        $data->push([
            'Harga Pokok Penjualan',
            '',
            number_format($this->cogs, 0, ',', '.'),
        ]);
        
        // Gross profit
        $data->push([
            'Laba Kotor',
            '',
            number_format($this->grossProfit, 0, ',', '.'),
        ]);
        
        $data->push(['', '', '']);
        
        // Expenses section
        $data->push([
            'Beban Operasional',
            '',
            '',
        ]);
        
        foreach ($this->expenses as $expense) {
            $data->push([
                $expense->expense_category,
                number_format($expense->total, 0, ',', '.'),
                '',
            ]);
        }
        
        $data->push([
            'Total Beban Operasional',
            '',
            number_format($this->totalExpenses, 0, ',', '.'),
        ]);
        
        $data->push(['', '', '']);
        
        // Net profit
        $data->push([
            'Laba Bersih',
            '',
            number_format($this->netProfit, 0, ',', '.'),
        ]);

        return $data;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Keterangan',
            'Subtotal',
            'Total',
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Laporan Laba Rugi';
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => ['font' => ['bold' => true, 'size' => 16]],
            2 => ['font' => ['bold' => true]],
            4 => ['font' => ['bold' => true]],
            6 => ['font' => ['bold' => true]],
            9 => ['font' => ['bold' => true]],
            // Set last row as bold
            $this->expenses->count() + 13 => ['font' => ['bold' => true]],
            $this->expenses->count() + 15 => ['font' => ['bold' => true]],
        ];
    }
}
