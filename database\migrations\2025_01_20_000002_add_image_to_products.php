<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add image column to processed_inventory table
        Schema::table('processed_inventory', function (Blueprint $table) {
            $table->string('image')->nullable();
        });

        // Add image column to other_products table
        Schema::table('other_products', function (Blueprint $table) {
            $table->string('image')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('processed_inventory', function (Blueprint $table) {
            $table->dropColumn('image');
        });

        Schema::table('other_products', function (Blueprint $table) {
            $table->dropColumn('image');
        });
    }
};
