<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Payment Gateway Button</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bug me-2"></i>Test Payment Gateway Button
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Testing Instructions:</strong>
                            <ol class="mb-0 mt-2">
                                <li>Open POS system: <a href="http://localhost:8000/pos" target="_blank">http://localhost:8000/pos</a></li>
                                <li>Add products to cart</li>
                                <li>Click "Proses Pembayaran"</li>
                                <li>Select "Payment Gateway" tab</li>
                                <li>Click "Lanjutkan ke Payment Gateway" button</li>
                                <li>Check browser console for logs</li>
                            </ol>
                        </div>

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="card border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">✅ Expected Behavior</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled mb-0">
                                            <li><i class="fas fa-check text-success me-2"></i>Button click detected</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Console logs appear</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Button shows loading state</li>
                                            <li><i class="fas fa-check text-success me-2"></i>AJAX request sent</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Redirect to payment page</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-danger">
                                    <div class="card-header bg-danger text-white">
                                        <h6 class="mb-0">❌ Common Issues</h6>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled mb-0">
                                            <li><i class="fas fa-times text-danger me-2"></i>No console logs</li>
                                            <li><i class="fas fa-times text-danger me-2"></i>JavaScript errors</li>
                                            <li><i class="fas fa-times text-danger me-2"></i>Button not responding</li>
                                            <li><i class="fas fa-times text-danger me-2"></i>AJAX request fails</li>
                                            <li><i class="fas fa-times text-danger me-2"></i>No redirect happens</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h6>🔍 Debug Console Commands:</h6>
                            <div class="bg-dark text-light p-3 rounded">
                                <code>
                                    // Check if button exists<br>
                                    console.log(document.getElementById('complete-gateway-payment'));<br><br>
                                    
                                    // Check if event listener is attached<br>
                                    document.getElementById('complete-gateway-payment').click();<br><br>
                                    
                                    // Check cart data<br>
                                    console.log('Cart:', cart);<br><br>
                                    
                                    // Check current payment method<br>
                                    console.log('Payment method:', currentPaymentMethod);
                                </code>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h6>📋 Test Checklist:</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check1">
                                <label class="form-check-label" for="check1">
                                    POS page loads without errors
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check2">
                                <label class="form-check-label" for="check2">
                                    Products can be added to cart
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check3">
                                <label class="form-check-label" for="check3">
                                    Payment Gateway tab is selectable
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check4">
                                <label class="form-check-label" for="check4">
                                    Button click shows console logs
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check5">
                                <label class="form-check-label" for="check5">
                                    Button shows loading state
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check6">
                                <label class="form-check-label" for="check6">
                                    AJAX request is sent successfully
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check7">
                                <label class="form-check-label" for="check7">
                                    Payment page opens in new tab
                                </label>
                            </div>
                        </div>

                        <div class="mt-4 text-center">
                            <a href="http://localhost:8000/pos" class="btn btn-primary btn-lg" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Open POS System
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
