@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-seedling"></i>
        <span>Detail Stok Ubi Mentah</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Detail {{ $rawInventory->name }}</span>
                    <div>
                        <a href="{{ route('raw-inventory.edit', $rawInventory) }}" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <a href="{{ route('raw-inventory.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="35%"><PERSON><PERSON></th>
                                    <td>{{ $rawInventory->name }}</td>
                                </tr>
                                <tr>
                                    <th>Nomor Batch</th>
                                    <td>{{ $rawInventory->batch_number }}</td>
                                </tr>
                                <tr>
                                    <th>Supplier</th>
                                    <td>{{ $rawInventory->supplier_name ?: '-' }}</td>
                                </tr>
                                <tr>
                                    <th>Kualitas</th>
                                    <td>
                                        @if($rawInventory->quality == 'A')
                                            <span class="badge bg-success">A (Premium)</span>
                                        @elseif($rawInventory->quality == 'B')
                                            <span class="badge bg-info">B (Standard)</span>
                                        @else
                                            <span class="badge bg-warning">C (Economy)</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Tanggal Pembelian</th>
                                    <td>{{ $rawInventory->purchase_date->format('d/m/Y') }}</td>
                                </tr>
                                <tr>
                                    <th>Tanggal Kadaluarsa</th>
                                    <td>{{ $rawInventory->expiry_date ? $rawInventory->expiry_date->format('d/m/Y') : '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="35%">Stok Saat Ini</th>
                                    <td>
                                        {{ number_format($rawInventory->current_stock, 2, ',', '.') }} kg
                                        @if($rawInventory->current_stock <= $rawInventory->min_stock_threshold)
                                            <span class="badge bg-danger ms-2">Stok Menipis</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <th>Batas Minimum</th>
                                    <td>{{ number_format($rawInventory->min_stock_threshold, 2, ',', '.') }} kg</td>
                                </tr>
                                <tr>
                                    <th>Harga per kg</th>
                                    <td>Rp {{ number_format($rawInventory->cost_per_kg, 0, ',', '.') }}</td>
                                </tr>
                                <tr>
                                    <th>Total Nilai Stok</th>
                                    <td>Rp {{ number_format($rawInventory->total_cost, 0, ',', '.') }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>
                                        @if($rawInventory->is_active)
                                            <span class="badge bg-success">Aktif</span>
                                        @else
                                            <span class="badge bg-danger">Tidak Aktif</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    @if($rawInventory->notes)
                    <div class="mt-4">
                        <h5>Catatan:</h5>
                        <p class="mb-0">{{ $rawInventory->notes }}</p>
                    </div>
                    @endif

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>Tambah Stok</h5>
                            <form action="{{ route('raw-inventory.add-stock', $rawInventory) }}" method="POST" class="border rounded p-4">
                                @csrf
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="additional_stock" class="form-label">Jumlah Tambahan (kg) <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control @error('additional_stock') is-invalid @enderror" id="additional_stock" name="additional_stock" value="{{ old('additional_stock') }}" step="0.1" min="0.1" required>
                                        @error('additional_stock')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="cost_per_kg" class="form-label">Harga per kg (Rp) <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control @error('cost_per_kg') is-invalid @enderror" id="cost_per_kg" name="cost_per_kg" value="{{ old('cost_per_kg', $rawInventory->cost_per_kg) }}" min="0" required>
                                        @error('cost_per_kg')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="text-end">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-plus-circle"></i> Tambah Stok
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 