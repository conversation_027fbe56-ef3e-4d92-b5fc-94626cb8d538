@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-seedling"></i>
        <span>Tambah Stok Ubi Mentah</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Form Tambah Stok Ubi Mentah</span>
                    <a href="{{ route('raw-inventory.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <form action="{{ route('raw-inventory.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label"><PERSON><PERSON> Ubi <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                                @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="supplier_name" class="form-label">Supplier</label>
                                <input type="text" class="form-control @error('supplier_name') is-invalid @enderror" id="supplier_name" name="supplier_name" value="{{ old('supplier_name') }}">
                                @error('supplier_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="quantity_kg" class="form-label">Stok Awal (kg) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('quantity_kg') is-invalid @enderror" id="quantity_kg" name="quantity_kg" value="{{ old('quantity_kg', 0) }}" step="0.1" min="0" required>
                                @error('quantity_kg')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="cost_per_kg" class="form-label">Harga per kg (Rp) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control @error('cost_per_kg') is-invalid @enderror" id="cost_per_kg" name="cost_per_kg" value="{{ old('cost_per_kg', 0) }}" min="0" required>
                                @error('cost_per_kg')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="min_stock_threshold" class="form-label">Batas Minimum Stok (kg)</label>
                                <input type="number" class="form-control @error('min_stock_threshold') is-invalid @enderror" id="min_stock_threshold" name="min_stock_threshold" value="{{ old('min_stock_threshold', 10) }}" step="0.1" min="0">
                                <small class="form-text text-muted">Sistem akan memberikan peringatan saat stok di bawah nilai ini.</small>
                                @error('min_stock_threshold')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="quality" class="form-label">Kualitas <span class="text-danger">*</span></label>
                                <select class="form-select @error('quality') is-invalid @enderror" id="quality" name="quality" required>
                                    <option value="A" {{ old('quality') == 'A' ? 'selected' : '' }}>A (Premium)</option>
                                    <option value="B" {{ old('quality') == 'B' ? 'selected' : '' }}>B (Standard)</option>
                                    <option value="C" {{ old('quality') == 'C' ? 'selected' : '' }}>C (Economy)</option>
                                </select>
                                @error('quality')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="purchase_date" class="form-label">Tanggal Pembelian <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('purchase_date') is-invalid @enderror" id="purchase_date" name="purchase_date" value="{{ old('purchase_date', date('Y-m-d')) }}" required>
                                @error('purchase_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-4 mb-3">
                                <label for="expiry_date" class="form-label">Tanggal Kadaluarsa</label>
                                <input type="date" class="form-control @error('expiry_date') is-invalid @enderror" id="expiry_date" name="expiry_date" value="{{ old('expiry_date') }}">
                                @error('expiry_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Catatan</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                            @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 