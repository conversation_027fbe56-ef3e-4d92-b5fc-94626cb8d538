# 📊 DASHBOARD & ANALYTICS SYSTEM

## 🎯 **OVERVIEW**

Sistem dashboard menyediakan real-time analytics dan business intelligence untuk monitoring performa bisnis. Terdapat 2 jenis dashboard: Admin Dashboard (lengkap) dan Employee Dashboard (terbatas).

---

## 🔑 **ADMIN DASHBOARD**

### **📍 Route & Access**
- **URL:** `/dashboard`
- **Controller:** `DashboardController@index`
- **Middleware:** `auth` (semua role dapat akses)
- **View:** `dashboard.index`

### **📊 KEY METRICS CARDS**

#### **1. 💰 Penjualan Hari Ini**
```php
$todaySales = Transaction::whereDate('created_at', $today)
    ->where('status', 'completed')
    ->sum('total_amount');

$todayTransactions = Transaction::whereDate('created_at', $today)
    ->where('status', 'completed')
    ->count();
```
- **Display:** Jumlah transaksi + total revenue
- **Format:** "15 transaksi - Rp 2,500,000"
- **Icon:** 🛒 Shopping cart
- **Color:** Primary blue

#### **2. 📈 Pendapatan Bulan Ini**
```php
$monthRevenue = Transaction::whereBetween('created_at', [$startOfMonth, $endOfMonth])
    ->where('status', 'completed')
    ->sum('total_amount');
```
- **Display:** Total revenue bulan berjalan
- **Trend:** Persentase perubahan vs bulan lalu
- **Icon:** 💰 Money bag
- **Color:** Success green

#### **3. 📦 Total Inventory**
```php
$rawInventoryTotal = RawInventory::sum('current_stock');
$processedInventoryTotal = ProcessedInventory::sum('current_stock');
```
- **Display:** Total stok raw + processed
- **Unit:** Kg untuk raw, buah untuk processed
- **Icon:** 📦 Box
- **Color:** Warning orange

#### **4. ⚠️ Stok Menipis**
```php
$lowStockRaw = RawInventory::whereRaw('current_stock <= min_stock_threshold')->count();
$lowStockProcessed = ProcessedInventory::whereRaw('current_stock <= min_stock_threshold')->count();
```
- **Display:** Jumlah item dengan stok rendah
- **Alert:** Red badge jika > 0
- **Icon:** ⚠️ Warning
- **Color:** Danger red

---

## 📈 **INTERACTIVE CHARTS**

### **1. 📊 Monthly Sales Chart**
```javascript
// Chart.js Line Chart
const monthlyChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', ...], // Dynamic dari database
        datasets: [{
            label: 'Pendapatan Bulanan',
            data: [2500000, 3200000, 2800000, ...], // Dari Transaction model
            backgroundColor: 'rgba(139, 69, 19, 0.2)',
            borderColor: 'rgba(139, 69, 19, 1)',
            tension: 0.3
        }]
    }
});
```

**Data Source:**
```php
$monthlyData = Transaction::select(
    DB::raw('SUM(total_amount) as total'),
    DB::raw("DATE_FORMAT(created_at, '%Y-%m') as month"),
    DB::raw("DATE_FORMAT(created_at, '%b %Y') as month_name")
)
->where('status', 'completed')
->whereYear('created_at', date('Y'))
->groupBy('month', 'month_name')
->orderBy('month')
->get();
```

### **2. 📊 Daily Sales Chart (30 Days)**
```javascript
// Chart.js Bar Chart
const dailyChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: ['1 Jun', '2 Jun', ...], // 30 hari terakhir
        datasets: [{
            label: 'Penjualan Harian',
            data: [150000, 200000, 180000, ...],
            backgroundColor: 'rgba(255, 140, 0, 0.8)'
        }]
    }
});
```

### **3. 🥧 Top Products Pie Chart**
```javascript
// Chart.js Doughnut Chart
const topProductsChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['Ubi Bakar Original', 'Ubi Bakar Madu', 'Teh Botol'],
        datasets: [{
            data: [45, 30, 25], // Persentase penjualan
            backgroundColor: ['#8B4513', '#FF8C00', '#32CD32']
        }]
    }
});
```

---

## 📋 **DATA TABLES**

### **1. 🔥 Recent Transactions**
```php
$recentTransactions = Transaction::with(['user', 'items'])
    ->whereDate('created_at', today())
    ->orderBy('created_at', 'desc')
    ->limit(5)
    ->get();
```

**Columns:**
- Invoice Number
- Customer Name
- Total Amount
- Payment Method
- Cashier Name
- Time
- Quick Actions (View, Print)

### **2. 📦 Low Stock Items**
```php
$lowStockItems = collect()
    ->merge(RawInventory::whereRaw('current_stock <= min_stock_threshold')->get())
    ->merge(ProcessedInventory::whereRaw('current_stock <= min_stock_threshold')->get())
    ->merge(OtherProduct::whereRaw('current_stock <= min_stock_threshold')->get());
```

**Columns:**
- Product Name
- Current Stock
- Minimum Threshold
- Status Badge
- Action (Restock)

### **3. 🏆 Top Selling Products**
```php
$topProducts = TransactionItem::select('product_name', DB::raw('SUM(quantity) as total_sold'))
    ->whereHas('transaction', function ($query) {
        $query->where('status', 'completed');
    })
    ->whereDate('created_at', '>=', Carbon::now()->subDays(30))
    ->groupBy('product_name')
    ->orderByDesc('total_sold')
    ->limit(5)
    ->get();
```

---

## 👨‍💼 **EMPLOYEE DASHBOARD**

### **📍 Route & Access**
- **URL:** `/employee/dashboard`
- **Controller:** `EmployeeDashboardController@index`
- **Middleware:** `auth` + role check
- **View:** `employee.dashboard`

### **🎯 Simplified Metrics**

#### **Employee-Specific Cards:**
1. **💰 Penjualan Hari Ini** - Same as admin
2. **📦 Stok Ubi Bakar** - Processed inventory count
3. **🥤 Produk Lain** - Other products count
4. **⚠️ Stok Menipis** - Low stock alerts

#### **Limited Charts:**
- ✅ **Weekly Sales Trend** - 7 hari terakhir
- ✅ **Top Products** - 5 produk terlaris
- ❌ **Monthly Financial** - Tidak ditampilkan
- ❌ **Profit Analysis** - Tidak ditampilkan

---

## 🔄 **REAL-TIME FEATURES**

### **1. Auto-Refresh Data**
```javascript
// Auto refresh setiap 5 menit
setInterval(function() {
    location.reload();
}, 300000);

// AJAX refresh untuk metrics cards
function refreshMetrics() {
    $.get('/api/dashboard/metrics', function(data) {
        $('#today-sales').text(data.today_sales);
        $('#month-revenue').text(data.month_revenue);
        // Update other metrics
    });
}
```

### **2. Live Notifications**
```javascript
// WebSocket untuk notifikasi real-time
Echo.channel('dashboard')
    .listen('NewTransaction', (e) => {
        // Update sales counter
        updateSalesMetrics();
        showNotification('Transaksi baru: ' + e.invoice);
    })
    .listen('LowStock', (e) => {
        // Show low stock alert
        showLowStockAlert(e.product);
    });
```

---

## 📱 **RESPONSIVE DESIGN**

### **Mobile Layout:**
- **Cards:** Stack vertically on mobile
- **Charts:** Responsive canvas sizing
- **Tables:** Horizontal scroll
- **Navigation:** Collapsible sidebar

### **Tablet Layout:**
- **Cards:** 2x2 grid
- **Charts:** Side-by-side
- **Tables:** Full width
- **Sidebar:** Always visible

---

## 🎨 **UI/UX FEATURES**

### **Visual Elements:**
- **Color Coding:** Status-based colors
- **Icons:** FontAwesome icons for clarity
- **Animations:** Smooth transitions
- **Loading States:** Skeleton loaders
- **Empty States:** Friendly messages

### **Interactive Elements:**
- **Hover Effects:** Card elevation
- **Click Actions:** Quick navigation
- **Tooltips:** Additional information
- **Modals:** Quick actions
- **Dropdowns:** Filter options

---

## 📊 **PERFORMANCE OPTIMIZATION**

### **Database Optimization:**
```php
// Eager loading untuk mengurangi N+1 queries
$transactions = Transaction::with(['user', 'items.product'])
    ->whereDate('created_at', today())
    ->get();

// Caching untuk data yang jarang berubah
$topProducts = Cache::remember('top_products', 3600, function () {
    return TransactionItem::getTopProducts();
});
```

### **Frontend Optimization:**
- **Lazy Loading:** Charts dimuat setelah DOM ready
- **Debouncing:** Search input dengan delay
- **Pagination:** Limit data per page
- **Compression:** Minified CSS/JS

---

## 🔧 **CUSTOMIZATION OPTIONS**

### **Dashboard Widgets:**
- **Draggable Cards:** Reorder dashboard layout
- **Widget Settings:** Show/hide specific metrics
- **Date Range:** Custom date filters
- **Export Options:** PDF/Excel export

### **Chart Customization:**
- **Chart Types:** Line, Bar, Pie, Doughnut
- **Color Themes:** Multiple color schemes
- **Data Filters:** Date range, product category
- **Animation:** Enable/disable animations

---

## 📈 **BUSINESS INTELLIGENCE**

### **Key Performance Indicators (KPIs):**
1. **Daily Sales Growth** - Pertumbuhan penjualan harian
2. **Average Transaction Value** - Nilai rata-rata transaksi
3. **Customer Retention** - Tingkat retensi pelanggan
4. **Inventory Turnover** - Perputaran inventory
5. **Profit Margin** - Margin keuntungan

### **Trend Analysis:**
- **Sales Patterns** - Pola penjualan harian/mingguan
- **Seasonal Trends** - Tren musiman
- **Product Performance** - Performa produk
- **Customer Behavior** - Perilaku pelanggan

---

**📊 Dashboard Performance:** Optimized  
**🔄 Real-time Updates:** Enabled  
**📱 Mobile Support:** Full Responsive
