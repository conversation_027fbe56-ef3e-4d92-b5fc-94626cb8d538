<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Transaction;
use App\Models\TransactionItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CleanDuplicateTransactionsSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('Starting duplicate transaction cleanup...');

        DB::beginTransaction();
        try {
            // Find potential duplicate transactions
            $duplicates = DB::select("
                SELECT 
                    t1.id as keep_id,
                    t2.id as duplicate_id,
                    t1.invoice_number as keep_invoice,
                    t2.invoice_number as duplicate_invoice,
                    t1.total_amount,
                    t1.created_at as keep_created,
                    t2.created_at as duplicate_created
                FROM transactions t1
                JOIN transactions t2 ON (
                    t1.user_id = t2.user_id 
                    AND t1.total_amount = t2.total_amount
                    AND t1.payment_method = t2.payment_method
                    AND ABS(TIMESTAMPDIFF(SECOND, t1.created_at, t2.created_at)) <= 60
                    AND t1.id < t2.id
                )
                WHERE t1.deleted_at IS NULL 
                AND t2.deleted_at IS NULL
                ORDER BY t1.created_at DESC
            ");

            $this->command->info('Found ' . count($duplicates) . ' potential duplicate transactions');

            $deletedCount = 0;
            $restoredStockCount = 0;

            foreach ($duplicates as $duplicate) {
                $this->command->info("Processing duplicate: {$duplicate->duplicate_invoice} (keeping {$duplicate->keep_invoice})");

                // Get the duplicate transaction
                $duplicateTransaction = Transaction::find($duplicate->duplicate_id);
                
                if ($duplicateTransaction) {
                    // Restore stock for duplicate transaction items
                    foreach ($duplicateTransaction->items as $item) {
                        if ($item->processed_inventory_id) {
                            $product = \App\Models\ProcessedInventory::find($item->processed_inventory_id);
                            if ($product) {
                                $product->increment('current_stock', $item->quantity);
                                $restoredStockCount++;
                                $this->command->info("  Restored {$item->quantity} stock for {$product->name}");
                            }
                        } elseif ($item->product_id) {
                            $product = \App\Models\OtherProduct::find($item->product_id);
                            if ($product) {
                                $product->increment('current_stock', $item->quantity);
                                $restoredStockCount++;
                                $this->command->info("  Restored {$item->quantity} stock for {$product->name}");
                            }
                        }
                    }

                    // Soft delete the duplicate transaction
                    $duplicateTransaction->delete();
                    $deletedCount++;
                    
                    $this->command->info("  Deleted duplicate transaction: {$duplicate->duplicate_invoice}");
                }
            }

            // Also clean up transactions with identical invoice numbers (should not happen)
            $identicalInvoices = DB::select("
                SELECT invoice_number, COUNT(*) as count
                FROM transactions 
                WHERE deleted_at IS NULL
                GROUP BY invoice_number 
                HAVING COUNT(*) > 1
            ");

            foreach ($identicalInvoices as $invoice) {
                $this->command->warn("Found {$invoice->count} transactions with same invoice: {$invoice->invoice_number}");
                
                // Keep the first one, delete the rest
                $transactions = Transaction::where('invoice_number', $invoice->invoice_number)
                    ->orderBy('created_at')
                    ->get();

                for ($i = 1; $i < $transactions->count(); $i++) {
                    $transaction = $transactions[$i];
                    
                    // Restore stock
                    foreach ($transaction->items as $item) {
                        if ($item->processed_inventory_id) {
                            $product = \App\Models\ProcessedInventory::find($item->processed_inventory_id);
                            if ($product) {
                                $product->increment('current_stock', $item->quantity);
                                $restoredStockCount++;
                            }
                        } elseif ($item->product_id) {
                            $product = \App\Models\OtherProduct::find($item->product_id);
                            if ($product) {
                                $product->increment('current_stock', $item->quantity);
                                $restoredStockCount++;
                            }
                        }
                    }

                    $transaction->delete();
                    $deletedCount++;
                    $this->command->info("  Deleted duplicate invoice: {$transaction->invoice_number}");
                }
            }

            DB::commit();

            $this->command->info("Cleanup completed!");
            $this->command->info("- Deleted {$deletedCount} duplicate transactions");
            $this->command->info("- Restored stock for {$restoredStockCount} items");

            // Log the cleanup
            Log::info('Duplicate transaction cleanup completed', [
                'deleted_transactions' => $deletedCount,
                'restored_stock_items' => $restoredStockCount,
                'duplicates_found' => count($duplicates),
                'identical_invoices' => count($identicalInvoices)
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            $this->command->error('Error during cleanup: ' . $e->getMessage());
            Log::error('Duplicate transaction cleanup failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
