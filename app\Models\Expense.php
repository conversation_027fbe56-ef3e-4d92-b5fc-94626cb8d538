<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Auditable;

class Expense extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $fillable = [
        'expense_number',
        'user_id',
        'expense_category',
        'expense_name',
        'amount',
        'expense_date',
        'description',
        'receipt_image'
    ];

    protected $casts = [
        'expense_date' => 'date',
        'amount' => 'decimal:2'
    ];

    /**
     * Generate a unique expense number
     * Format: EXP-YYYYMMDD-XXXX
     */
    public static function generateExpenseNumber()
    {
        $date = now()->format('Ymd');
        $latestExpense = static::whereDate('created_at', now()->format('Y-m-d'))
            ->latest()
            ->first();

        $sequence = $latestExpense ? (int)substr($latestExpense->expense_number, -4) + 1 : 1;
        return 'EXP-' . $date . '-' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get the user who created this expense.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get common expense categories.
     */
    public static function getCategories()
    {
        return [
            'operational' => 'Operasional',
            'utilities' => 'Utilitas',
            'inventory' => 'Inventaris',
            'salary' => 'Gaji',
            'marketing' => 'Marketing',
            'maintenance' => 'Pemeliharaan',
            'other' => 'Lainnya'
        ];
    }
}
