<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UpdatePasswordController extends Controller
{
    public function updatePasswords()
    {
        try {
            // Update Admin password
            $admin = User::where('email', '<EMAIL>')->first();
            if ($admin) {
                $admin->password = Hash::make('admin123');
                $admin->save();
            }

            // Update Karyawan password
            $karyawan = User::where('email', '<EMAIL>')->first();
            if ($karyawan) {
                $karyawan->password = Hash::make('karyawan123');
                $karyawan->save();
            }

            return "Passwords updated successfully!";
        } catch (\Exception $e) {
            return "Error: " . $e->getMessage();
        }
    }
} 