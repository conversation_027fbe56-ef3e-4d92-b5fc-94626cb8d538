/**
 * Notifications JS - Untuk menangani fungsionalitas notifikasi
 */
document.addEventListener('DOMContentLoaded', function() {
    // Notifikasi dropdown toggle
    const notificationDropdown = document.getElementById('notificationDropdown');
    const markAllAsRead = document.getElementById('markAllAsRead');
    const notificationBadge = document.getElementById('notification-badge');
    const notificationItems = document.querySelectorAll('.notification-item');
    
    // Toggle dropdown saat ikon notifikasi diklik
    if (notificationDropdown) {
        notificationDropdown.addEventListener('click', function(e) {
            e.preventDefault();
            const dropdown = document.querySelector('.notification-dropdown');
            if (dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
            } else {
                dropdown.classList.add('show');
            }
        });
    }
    
    // Tutup dropdown jika klik di luar
    document.addEventListener('click', function(e) {
        if (notificationDropdown && !notificationDropdown.contains(e.target)) {
            const dropdown = document.querySelector('.notification-dropdown');
            if (dropdown && dropdown.classList.contains('show')) {
                dropdown.classList.remove('show');
            }
        }
    });
    
    // Mark all as read
    if (markAllAsRead) {
        markAllAsRead.addEventListener('click', function(e) {
            e.preventDefault();
            // Update UI
            if (notificationBadge) {
                notificationBadge.style.display = 'none';
            }
        });
    }
    
    // Klik pada item notifikasi
    notificationItems.forEach(item => {
        item.addEventListener('click', function() {
            // Notifikasi diklik akan menuju ke halaman terkait
            const url = this.getAttribute('href');
            if (url && url !== '#') {
                window.location.href = url;
            }
        });
    });
});
