<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Hapus tabel yang tidak digunakan
        if (Schema::hasTable('password_reset_tokens')) {
            Schema::dropIfExists('password_reset_tokens');
        }

        if (Schema::hasTable('personal_access_tokens')) {
            Schema::dropIfExists('personal_access_tokens');
        }

        if (Schema::hasTable('failed_jobs')) {
            Schema::dropIfExists('failed_jobs');
        }

        // Hapus kolom yang tidak digunakan atau redundan dari tabel raw_inventory
        if (Schema::hasTable('raw_inventory')) {
            Schema::table('raw_inventory', function (Blueprint $table) {
                // Kolom supplier_name dan supplier redundan, cukup gunakan supplier saja
                if (Schema::hasColumn('raw_inventory', 'supplier_name') && Schema::hasColumn('raw_inventory', 'supplier')) {
                    $table->dropColumn('supplier_name');
                }

                // Kolom notes tidak digunakan dalam aplikasi
                if (Schema::hasColumn('raw_inventory', 'notes')) {
                    $table->dropColumn('notes');
                }
            });
        }

        // Hapus kolom yang tidak digunakan atau redundan dari tabel processed_inventory
        if (Schema::hasTable('processed_inventory')) {
            Schema::table('processed_inventory', function (Blueprint $table) {
                // Kolom notes tidak digunakan dalam aplikasi
                if (Schema::hasColumn('processed_inventory', 'notes')) {
                    $table->dropColumn('notes');
                }

                // Kolom raw_material_per_item redundan dengan quantity_processed_kg dan quantity_produced
                if (Schema::hasColumn('processed_inventory', 'raw_material_per_item')) {
                    $table->dropColumn('raw_material_per_item');
                }
            });
        }

        // Hapus kolom yang tidak digunakan atau redundan dari tabel other_products
        if (Schema::hasTable('other_products')) {
            Schema::table('other_products', function (Blueprint $table) {
                // Kolom notes tidak digunakan dalam aplikasi
                if (Schema::hasColumn('other_products', 'notes')) {
                    $table->dropColumn('notes');
                }
            });
        }

        // Hapus kolom yang tidak digunakan dari tabel users
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                // Kolom email_verified_at tidak digunakan karena tidak ada fitur verifikasi email
                if (Schema::hasColumn('users', 'email_verified_at')) {
                    $table->dropColumn('email_verified_at');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Mengembalikan tabel yang dihapus
        if (!Schema::hasTable('password_reset_tokens')) {
            Schema::create('password_reset_tokens', function (Blueprint $table) {
                $table->string('email')->primary();
                $table->string('token');
                $table->timestamp('created_at')->nullable();
            });
        }

        if (!Schema::hasTable('personal_access_tokens')) {
            Schema::create('personal_access_tokens', function (Blueprint $table) {
                $table->id();
                $table->morphs('tokenable');
                $table->string('name');
                $table->string('token', 64)->unique();
                $table->text('abilities')->nullable();
                $table->timestamp('last_used_at')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->timestamps();
            });
        }

        if (!Schema::hasTable('failed_jobs')) {
            Schema::create('failed_jobs', function (Blueprint $table) {
                $table->id();
                $table->string('uuid')->unique();
                $table->text('connection');
                $table->text('queue');
                $table->longText('payload');
                $table->longText('exception');
                $table->timestamp('failed_at')->useCurrent();
            });
        }

        // Mengembalikan kolom yang dihapus dari tabel raw_inventory
        if (Schema::hasTable('raw_inventory')) {
            Schema::table('raw_inventory', function (Blueprint $table) {
                if (!Schema::hasColumn('raw_inventory', 'supplier_name')) {
                    $table->string('supplier_name')->after('name');
                }

                if (!Schema::hasColumn('raw_inventory', 'notes')) {
                    $table->text('notes')->nullable()->after('quality');
                }
            });
        }

        // Mengembalikan kolom yang dihapus dari tabel processed_inventory
        if (Schema::hasTable('processed_inventory')) {
            Schema::table('processed_inventory', function (Blueprint $table) {
                if (!Schema::hasColumn('processed_inventory', 'notes')) {
                    $table->text('notes')->nullable()->after('is_active');
                }

                if (!Schema::hasColumn('processed_inventory', 'raw_material_per_item')) {
                    $table->decimal('raw_material_per_item', 10, 2)->nullable()->after('cost_per_item');
                }
            });
        }

        // Mengembalikan kolom yang dihapus dari tabel other_products
        if (Schema::hasTable('other_products')) {
            Schema::table('other_products', function (Blueprint $table) {
                if (!Schema::hasColumn('other_products', 'notes')) {
                    $table->text('notes')->nullable()->after('is_active');
                }
            });
        }

        // Mengembalikan kolom yang dihapus dari tabel users
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (!Schema::hasColumn('users', 'email_verified_at')) {
                    $table->timestamp('email_verified_at')->nullable()->after('email');
                }
            });
        }
    }
};
