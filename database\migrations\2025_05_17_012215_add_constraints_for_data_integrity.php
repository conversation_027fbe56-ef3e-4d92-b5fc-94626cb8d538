<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Menambahkan constraint pada tabel raw_inventory
        // Memastikan quantity_kg, cost_per_kg, total_cost, dan current_stock tidak negatif
        DB::statement('ALTER TABLE raw_inventory ADD CONSTRAINT chk_raw_quantity_positive CHECK (quantity_kg >= 0)');
        DB::statement('ALTER TABLE raw_inventory ADD CONSTRAINT chk_raw_cost_per_kg_positive CHECK (cost_per_kg >= 0)');
        DB::statement('ALTER TABLE raw_inventory ADD CONSTRAINT chk_raw_total_cost_positive CHECK (total_cost >= 0)');
        DB::statement('ALTER TABLE raw_inventory ADD CONSTRAINT chk_raw_current_stock_positive CHECK (current_stock >= 0)');

        // Memastikan expiry_date lebih besar dari purchase_date
        DB::statement('ALTER TABLE raw_inventory ADD CONSTRAINT chk_raw_dates_valid CHECK (expiry_date IS NULL OR expiry_date > purchase_date)');

        // Menambahkan constraint pada tabel processed_inventory
        // Memastikan quantity_processed_kg, quantity_produced, cost_per_unit, selling_price, dan current_stock tidak negatif
        DB::statement('ALTER TABLE processed_inventory ADD CONSTRAINT chk_proc_quantity_processed_positive CHECK (quantity_processed_kg >= 0)');
        DB::statement('ALTER TABLE processed_inventory ADD CONSTRAINT chk_proc_quantity_produced_positive CHECK (quantity_produced >= 0)');
        DB::statement('ALTER TABLE processed_inventory ADD CONSTRAINT chk_proc_cost_per_unit_positive CHECK (cost_per_unit >= 0)');
        DB::statement('ALTER TABLE processed_inventory ADD CONSTRAINT chk_proc_selling_price_positive CHECK (selling_price >= 0)');
        DB::statement('ALTER TABLE processed_inventory ADD CONSTRAINT chk_proc_current_stock_positive CHECK (current_stock >= 0)');

        // Memastikan expiry_date lebih besar dari production_date
        DB::statement('ALTER TABLE processed_inventory ADD CONSTRAINT chk_proc_dates_valid CHECK (expiry_date > production_date)');

        // Menambahkan constraint pada tabel transactions
        // Memastikan subtotal, tax, discount, total_amount, amount_paid, dan change_amount tidak negatif
        DB::statement('ALTER TABLE transactions ADD CONSTRAINT chk_trans_subtotal_positive CHECK (subtotal >= 0)');
        DB::statement('ALTER TABLE transactions ADD CONSTRAINT chk_trans_tax_positive CHECK (tax >= 0)');
        DB::statement('ALTER TABLE transactions ADD CONSTRAINT chk_trans_discount_positive CHECK (discount >= 0)');
        DB::statement('ALTER TABLE transactions ADD CONSTRAINT chk_trans_total_amount_positive CHECK (total_amount >= 0)');
        DB::statement('ALTER TABLE transactions ADD CONSTRAINT chk_trans_amount_paid_positive CHECK (amount_paid >= 0)');
        DB::statement('ALTER TABLE transactions ADD CONSTRAINT chk_trans_change_amount_positive CHECK (change_amount >= 0)');

        // Menambahkan constraint pada tabel transaction_items
        // Memastikan price, quantity, discount, dan subtotal tidak negatif
        DB::statement('ALTER TABLE transaction_items ADD CONSTRAINT chk_trans_items_price_positive CHECK (price >= 0)');
        DB::statement('ALTER TABLE transaction_items ADD CONSTRAINT chk_trans_items_quantity_positive CHECK (quantity >= 0)');
        DB::statement('ALTER TABLE transaction_items ADD CONSTRAINT chk_trans_items_discount_positive CHECK (discount >= 0)');
        DB::statement('ALTER TABLE transaction_items ADD CONSTRAINT chk_trans_items_subtotal_positive CHECK (subtotal >= 0)');

        // Menambahkan constraint pada tabel other_products
        // Memastikan purchase_price, selling_price, dan current_stock tidak negatif
        DB::statement('ALTER TABLE other_products ADD CONSTRAINT chk_other_prod_purchase_price_positive CHECK (purchase_price >= 0)');
        DB::statement('ALTER TABLE other_products ADD CONSTRAINT chk_other_prod_selling_price_positive CHECK (selling_price >= 0)');
        DB::statement('ALTER TABLE other_products ADD CONSTRAINT chk_other_prod_current_stock_positive CHECK (current_stock >= 0)');

        // Menambahkan constraint pada tabel production_logs
        // Memastikan raw_amount_used, produced_amount, raw_cost, additional_cost, total_cost, dan cost_per_item tidak negatif
        DB::statement('ALTER TABLE production_logs ADD CONSTRAINT chk_prod_logs_raw_amount_positive CHECK (raw_amount_used >= 0)');
        DB::statement('ALTER TABLE production_logs ADD CONSTRAINT chk_prod_logs_produced_amount_positive CHECK (produced_amount >= 0)');
        DB::statement('ALTER TABLE production_logs ADD CONSTRAINT chk_prod_logs_raw_cost_positive CHECK (raw_cost >= 0)');
        DB::statement('ALTER TABLE production_logs ADD CONSTRAINT chk_prod_logs_additional_cost_positive CHECK (additional_cost >= 0)');
        DB::statement('ALTER TABLE production_logs ADD CONSTRAINT chk_prod_logs_total_cost_positive CHECK (total_cost >= 0)');
        DB::statement('ALTER TABLE production_logs ADD CONSTRAINT chk_prod_logs_cost_per_item_positive CHECK (cost_per_item >= 0)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Menghapus constraint dari tabel raw_inventory
        DB::statement('ALTER TABLE raw_inventory DROP CONSTRAINT IF EXISTS chk_raw_quantity_positive');
        DB::statement('ALTER TABLE raw_inventory DROP CONSTRAINT IF EXISTS chk_raw_cost_per_kg_positive');
        DB::statement('ALTER TABLE raw_inventory DROP CONSTRAINT IF EXISTS chk_raw_total_cost_positive');
        DB::statement('ALTER TABLE raw_inventory DROP CONSTRAINT IF EXISTS chk_raw_current_stock_positive');
        DB::statement('ALTER TABLE raw_inventory DROP CONSTRAINT IF EXISTS chk_raw_dates_valid');

        // Menghapus constraint dari tabel processed_inventory
        DB::statement('ALTER TABLE processed_inventory DROP CONSTRAINT IF EXISTS chk_proc_quantity_processed_positive');
        DB::statement('ALTER TABLE processed_inventory DROP CONSTRAINT IF EXISTS chk_proc_quantity_produced_positive');
        DB::statement('ALTER TABLE processed_inventory DROP CONSTRAINT IF EXISTS chk_proc_cost_per_unit_positive');
        DB::statement('ALTER TABLE processed_inventory DROP CONSTRAINT IF EXISTS chk_proc_selling_price_positive');
        DB::statement('ALTER TABLE processed_inventory DROP CONSTRAINT IF EXISTS chk_proc_current_stock_positive');
        DB::statement('ALTER TABLE processed_inventory DROP CONSTRAINT IF EXISTS chk_proc_dates_valid');

        // Menghapus constraint dari tabel transactions
        DB::statement('ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_trans_subtotal_positive');
        DB::statement('ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_trans_tax_positive');
        DB::statement('ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_trans_discount_positive');
        DB::statement('ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_trans_total_amount_positive');
        DB::statement('ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_trans_amount_paid_positive');
        DB::statement('ALTER TABLE transactions DROP CONSTRAINT IF EXISTS chk_trans_change_amount_positive');

        // Menghapus constraint dari tabel transaction_items
        DB::statement('ALTER TABLE transaction_items DROP CONSTRAINT IF EXISTS chk_trans_items_price_positive');
        DB::statement('ALTER TABLE transaction_items DROP CONSTRAINT IF EXISTS chk_trans_items_quantity_positive');
        DB::statement('ALTER TABLE transaction_items DROP CONSTRAINT IF EXISTS chk_trans_items_discount_positive');
        DB::statement('ALTER TABLE transaction_items DROP CONSTRAINT IF EXISTS chk_trans_items_subtotal_positive');

        // Menghapus constraint dari tabel other_products
        DB::statement('ALTER TABLE other_products DROP CONSTRAINT IF EXISTS chk_other_prod_purchase_price_positive');
        DB::statement('ALTER TABLE other_products DROP CONSTRAINT IF EXISTS chk_other_prod_selling_price_positive');
        DB::statement('ALTER TABLE other_products DROP CONSTRAINT IF EXISTS chk_other_prod_current_stock_positive');

        // Menghapus constraint dari tabel production_logs
        DB::statement('ALTER TABLE production_logs DROP CONSTRAINT IF EXISTS chk_prod_logs_raw_amount_positive');
        DB::statement('ALTER TABLE production_logs DROP CONSTRAINT IF EXISTS chk_prod_logs_produced_amount_positive');
        DB::statement('ALTER TABLE production_logs DROP CONSTRAINT IF EXISTS chk_prod_logs_raw_cost_positive');
        DB::statement('ALTER TABLE production_logs DROP CONSTRAINT IF EXISTS chk_prod_logs_additional_cost_positive');
        DB::statement('ALTER TABLE production_logs DROP CONSTRAINT IF EXISTS chk_prod_logs_total_cost_positive');
        DB::statement('ALTER TABLE production_logs DROP CONSTRAINT IF EXISTS chk_prod_logs_cost_per_item_positive');
    }
};
