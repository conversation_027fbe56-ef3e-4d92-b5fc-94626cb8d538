@extends('layouts.app')

@section('title', 'Buat Distribusi Baru')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-truck me-2"></i>
            Buat Distribusi Baru
        </h1>
        <a href="{{ route('distributions.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-plus-circle me-2"></i>Form Distribusi
                    </h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('distributions.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="destination" class="form-label">Tujuan Distribusi <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('destination') is-invalid @enderror" 
                                           id="destination" name="destination" value="{{ old('destination') }}" required>
                                    @error('destination')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="distribution_date" class="form-label">Tanggal Distribusi <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('distribution_date') is-invalid @enderror" 
                                           id="distribution_date" name="distribution_date" value="{{ old('distribution_date', date('Y-m-d')) }}" required>
                                    @error('distribution_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicle_info" class="form-label">Informasi Kendaraan</label>
                                    <input type="text" class="form-control @error('vehicle_info') is-invalid @enderror" 
                                           id="vehicle_info" name="vehicle_info" value="{{ old('vehicle_info') }}" 
                                           placeholder="Contoh: Truck B 1234 XYZ">
                                    @error('vehicle_info')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="driver_name" class="form-label">Nama Sopir</label>
                                    <input type="text" class="form-control @error('driver_name') is-invalid @enderror" 
                                           id="driver_name" name="driver_name" value="{{ old('driver_name') }}">
                                    @error('driver_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Catatan</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <hr>

                        <h5 class="mb-3">
                            <i class="fas fa-box me-2"></i>Produk yang Didistribusikan
                        </h5>

                        <div id="distribution-items">
                            <div class="distribution-item border rounded p-3 mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Produk <span class="text-danger">*</span></label>
                                            <select name="items[0][processed_inventory_id]" class="form-select product-select" required>
                                                <option value="">Pilih Produk</option>
                                                @foreach($processedInventory as $item)
                                                <option value="{{ $item->id }}" data-stock="{{ $item->current_stock }}">
                                                    {{ $item->name }} (Stok: {{ $item->current_stock }})
                                                </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">Jumlah <span class="text-danger">*</span></label>
                                            <input type="number" name="items[0][quantity]" class="form-control quantity-input" 
                                                   min="1" required>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="mb-3">
                                            <label class="form-label">&nbsp;</label>
                                            <button type="button" class="btn btn-danger w-100 remove-item" disabled>
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="button" id="add-item" class="btn btn-success mb-3">
                            <i class="fas fa-plus"></i> Tambah Produk
                        </button>

                        <hr>

                        <div class="d-flex justify-content-end">
                            <a href="{{ route('distributions.index') }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan Distribusi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">
                        <i class="fas fa-info-circle me-2"></i>Informasi
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>Tips:</h6>
                        <ul class="mb-0">
                            <li>Pastikan stok produk mencukupi sebelum distribusi</li>
                            <li>Isi informasi kendaraan dan sopir untuk tracking</li>
                            <li>Tanggal distribusi tidak boleh mundur dari hari ini</li>
                            <li>Catatan dapat berisi informasi tambahan seperti alamat detail</li>
                        </ul>
                    </div>

                    <div class="mt-3">
                        <h6>Stok Tersedia:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Produk</th>
                                        <th>Stok</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($processedInventory as $item)
                                    <tr>
                                        <td>{{ $item->name }}</td>
                                        <td>
                                            <span class="badge {{ $item->current_stock > 10 ? 'bg-success' : ($item->current_stock > 0 ? 'bg-warning' : 'bg-danger') }}">
                                                {{ $item->current_stock }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let itemIndex = 1;
    
    // Add new item
    document.getElementById('add-item').addEventListener('click', function() {
        const container = document.getElementById('distribution-items');
        const newItem = document.querySelector('.distribution-item').cloneNode(true);
        
        // Update names and clear values
        newItem.querySelectorAll('select, input').forEach(element => {
            if (element.name) {
                element.name = element.name.replace('[0]', `[${itemIndex}]`);
            }
            element.value = '';
        });
        
        // Enable remove button
        newItem.querySelector('.remove-item').disabled = false;
        
        container.appendChild(newItem);
        itemIndex++;
        
        updateRemoveButtons();
    });
    
    // Remove item
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-item')) {
            e.target.closest('.distribution-item').remove();
            updateRemoveButtons();
        }
    });
    
    // Update remove buttons
    function updateRemoveButtons() {
        const items = document.querySelectorAll('.distribution-item');
        items.forEach((item, index) => {
            const removeBtn = item.querySelector('.remove-item');
            removeBtn.disabled = items.length === 1;
        });
    }
    
    // Validate stock
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('product-select') || e.target.classList.contains('quantity-input')) {
            const item = e.target.closest('.distribution-item');
            const select = item.querySelector('.product-select');
            const quantityInput = item.querySelector('.quantity-input');
            
            if (select.value && quantityInput.value) {
                const selectedOption = select.options[select.selectedIndex];
                const availableStock = parseInt(selectedOption.dataset.stock);
                const requestedQuantity = parseInt(quantityInput.value);
                
                if (requestedQuantity > availableStock) {
                    alert(`Stok tidak mencukupi! Stok tersedia: ${availableStock}`);
                    quantityInput.value = availableStock;
                }
            }
        }
    });
});
</script>
@endsection
