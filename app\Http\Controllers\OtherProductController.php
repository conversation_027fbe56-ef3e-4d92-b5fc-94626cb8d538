<?php

namespace App\Http\Controllers;

use App\Models\OtherProduct;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class OtherProductController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $otherProducts = OtherProduct::where('is_active', true)->get();
        $lowStock = $otherProducts->filter(function($product) {
            return $product->isLowStock();
        });

        return view('inventory.other-products.index', compact('otherProducts', 'lowStock'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('inventory.other-products.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'purchase_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'current_stock' => 'required|integer|min:0',
            'min_stock_threshold' => 'nullable|integer|min:0',
            'category' => 'nullable|string|max:255',
            'unit' => 'required|string|max:50',
            'supplier' => 'nullable|string|max:255',
            'notes' => 'nullable|string'
        ]);

        // Generate SKU
        $validated['sku'] = 'OTH-' . Str::upper(Str::random(8));
        
        OtherProduct::create($validated);

        return redirect()->route('other-products.index')
            ->with('success', 'Produk berhasil ditambahkan');
    }

    /**
     * Display the specified resource.
     */
    public function show(OtherProduct $otherProduct)
    {
        return view('inventory.other-products.show', compact('otherProduct'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(OtherProduct $otherProduct)
    {
        return view('inventory.other-products.edit', compact('otherProduct'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, OtherProduct $otherProduct)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'purchase_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'current_stock' => 'required|integer|min:0',
            'min_stock_threshold' => 'nullable|integer|min:0',
            'category' => 'nullable|string|max:255',
            'unit' => 'required|string|max:50',
            'supplier' => 'nullable|string|max:255',
            'notes' => 'nullable|string'
        ]);

        $otherProduct->update($validated);

        return redirect()->route('other-products.index')
            ->with('success', 'Produk berhasil diperbarui');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(OtherProduct $otherProduct)
    {
        // Soft delete dengan mengubah is_active menjadi false
        $otherProduct->update(['is_active' => false]);

        return redirect()->route('other-products.index')
            ->with('success', 'Produk berhasil dihapus');
    }
}
