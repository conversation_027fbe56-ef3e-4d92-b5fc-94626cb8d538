<?php

namespace App\Http\Requests\Auth;

use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
            'role' => ['required', 'string', 'in:admin,karyawan'],
        ];
    }

    /**
     * Attempt to authenticate the request's credentials.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authenticate(): void
    {
        $this->ensureIsNotRateLimited();

        $credentials = $this->only('email', 'password');
        $credentials['role'] = $this->input('role');

        // Langsung login tanpa cek role dulu untuk debugging
        if (! Auth::attempt(['email' => $credentials['email'], 'password' => $credentials['password']], $this->boolean('remember'))) {
            RateLimiter::hit($this->throttleKey());

            throw ValidationException::withMessages([
                'email' => 'Email atau password salah.',
            ]);
        }

        // Setelah berhasil login, cek apakah role sesuai dan status approved
        $user = Auth::user();

        // Cek status approval
        if (!$user->isApproved()) {
            Auth::logout();
            if ($user->isPending()) {
                throw ValidationException::withMessages([
                    'email' => 'Akun Anda masih menunggu persetujuan admin. Silakan hubungi admin untuk aktivasi akun.',
                ]);
            } elseif ($user->isRejected()) {
                throw ValidationException::withMessages([
                    'email' => 'Akun Anda telah ditolak oleh admin. Silakan hubungi admin untuk informasi lebih lanjut.',
                ]);
            } else {
                throw ValidationException::withMessages([
                    'email' => 'Status akun Anda tidak valid. Silakan hubungi admin.',
                ]);
            }
        }

        if ($user->role !== $credentials['role']) {
            Auth::logout();
            throw ValidationException::withMessages([
                'email' => 'Anda tidak memiliki akses login sebagai ' . $credentials['role'],
            ]);
        }

        RateLimiter::clear($this->throttleKey());
    }

    /**
     * Ensure the login request is not rate limited.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'email' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    /**
     * Get the rate limiting throttle key for the request.
     */
    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->input('email')).'|'.$this->ip());
    }
} 