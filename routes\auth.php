<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\RegisterController;
use Illuminate\Support\Facades\Route;

Route::middleware('guest')->group(function () {
    Route::get('login', [AuthenticatedSessionController::class, 'create'])
        ->name('login');

    Route::post('login', [AuthenticatedSessionController::class, 'store']);

    // Nonaktifkan registrasi publik - hanya super admin yang bisa menambah user
    // Route::get('register', [RegisterController::class, 'showRegistrationForm'])
    //     ->name('register');
    // Route::post('register', [RegisterController::class, 'register']);


});

Route::middleware('auth')->group(function () {
    Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
        ->name('logout');
}); 