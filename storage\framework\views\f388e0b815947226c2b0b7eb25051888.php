<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-users me-2"></i> Manajemen User</h1>
        <div>
            <a href="<?php echo e(route('admin.users.create')); ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah User
            </a>
        </div>
    </div>



    <!-- Filter Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5><i class="fas fa-filter me-2"></i> Filter & Pencarian</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.users.index')); ?>">
                <div class="row">
                    <div class="col-md-4">
                        <label for="role" class="form-label">Role</label>
                        <select class="form-select" id="role" name="role">
                            <option value="">Semua Role</option>
                            <option value="admin" <?php echo e(request('role') === 'admin' ? 'selected' : ''); ?>>Admin</option>
                            <option value="employee" <?php echo e(request('role') === 'employee' ? 'selected' : ''); ?>>Karyawan</option>
                            <option value="cashier" <?php echo e(request('role') === 'cashier' ? 'selected' : ''); ?>>Kasir</option>
                            <option value="warehouse" <?php echo e(request('role') === 'warehouse' ? 'selected' : ''); ?>>Gudang</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="search" class="form-label">Pencarian</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="<?php echo e(request('search')); ?>" placeholder="Nama atau email...">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>



    <!-- Users Table -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-table me-2"></i> Daftar User</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Nama</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Tanggal Dibuat</th>
                            <th>Dibuat Oleh</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <strong><?php echo e($user->name); ?></strong>
                                    <?php if($user->id === auth()->id()): ?>
                                        <span class="badge bg-info ms-1">Anda</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($user->email); ?></td>
                                <td>
                                    <?php if($user->role === 'admin'): ?>
                                        <span class="badge bg-danger">Admin</span>
                                    <?php elseif($user->role === 'employee'): ?>
                                        <span class="badge bg-primary">Karyawan</span>
                                    <?php elseif($user->role === 'cashier'): ?>
                                        <span class="badge bg-success">Kasir</span>
                                    <?php elseif($user->role === 'warehouse'): ?>
                                        <span class="badge bg-warning">Gudang</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-success">Aktif</span>
                                </td>
                                <td><?php echo e($user->created_at->format('d/m/Y H:i')); ?></td>
                                <td>
                                    <?php if($user->approver): ?>
                                        <?php echo e($user->approver->name); ?>

                                        <br><small class="text-muted"><?php echo e($user->approved_at?->format('d/m/Y H:i')); ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">System</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.users.show', $user)); ?>" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.users.edit', $user)); ?>" class="btn btn-warning btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <?php if($user->id !== auth()->id()): ?>
                                            <form method="POST" action="<?php echo e(route('admin.users.destroy', $user)); ?>" class="d-inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="btn btn-danger btn-sm"
                                                        onclick="return confirm('Hapus user ini?')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center text-muted">
                                    <i class="fas fa-users fa-2x mb-2"></i>
                                    <br>Tidak ada data user
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if($users->hasPages()): ?>
                <div class="d-flex justify-content-center mt-3">
                    <?php echo e($users->appends(request()->query())->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\ubi-bakar-cilembu - Copy\resources\views/admin/users/index.blade.php ENDPATH**/ ?>