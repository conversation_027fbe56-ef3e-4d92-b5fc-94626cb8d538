# 💰 POINT OF SALE (POS) SYSTEM

## 🎯 **OVERVIEW**

Sistem POS modern dengan interface touch-friendly untuk memproses transaksi penjualan. Mendukung multiple payment methods termasuk payment gateway Midtrans, dengan fitur real-time inventory update dan receipt printing.

---

## 🖥️ **POS INTERFACE**

### **📍 Route & Access**
- **URL:** `/pos`
- **Controller:** `TransactionController@pos`
- **Middleware:** `auth` (semua role dapat akses)
- **View:** `transactions.pos`

### **🎨 UI/UX Design**

#### **Layout Structure:**
```html
<!-- POS Layout -->
<div class="pos-container">
    <!-- Left Panel: Product Selection -->
    <div class="products-panel">
        <div class="product-search">
            <input type="text" placeholder="Cari produk...">
        </div>
        <div class="product-categories">
            <button class="category-btn active">Semua</button>
            <button class="category-btn">U<PERSON></button>
            <button class="category-btn">Minuman</button>
            <button class="category-btn">Snack</button>
        </div>
        <div class="products-grid">
            <!-- Product cards -->
        </div>
    </div>
    
    <!-- Right Panel: Cart & Checkout -->
    <div class="cart-panel">
        <div class="cart-items"></div>
        <div class="cart-summary"></div>
        <div class="payment-section"></div>
    </div>
</div>
```

#### **Product Cards:**
```html
<!-- Product Card Template -->
<div class="product-card" data-id="{{ $product->id }}" data-type="processed">
    <div class="product-image">
        @if($product->image)
            <img src="{{ asset('storage/products/' . $product->image) }}" alt="{{ $product->name }}">
        @else
            <div class="no-image">📦</div>
        @endif
    </div>
    <div class="product-info">
        <h5 class="product-name">{{ $product->name }}</h5>
        <p class="product-price">Rp {{ number_format($product->selling_price, 0, ',', '.') }}</p>
        <p class="product-stock">Stok: {{ $product->current_stock }}</p>
    </div>
    <div class="product-actions">
        <button class="btn-add-to-cart">Tambah</button>
    </div>
</div>
```

---

## 🛒 **CART MANAGEMENT**

### **JavaScript Cart System:**
```javascript
class POSCart {
    constructor() {
        this.items = [];
        this.total = 0;
        this.init();
    }

    init() {
        this.loadFromStorage();
        this.bindEvents();
        this.updateDisplay();
    }

    addItem(productId, productName, price, maxStock, productType) {
        // Validate product data
        if (!productId || !productName || isNaN(price) || isNaN(maxStock)) {
            alert('Data produk tidak lengkap. Silakan refresh halaman.');
            return;
        }

        const existingItem = this.items.find(item => 
            item.id === productId && item.type === productType
        );

        if (existingItem) {
            if (existingItem.quantity >= maxStock) {
                alert(`Stok ${productName} tidak mencukupi!`);
                return;
            }
            existingItem.quantity++;
        } else {
            this.items.push({
                id: productId,
                name: productName,
                price: parseFloat(price),
                quantity: 1,
                maxStock: parseInt(maxStock),
                type: productType
            });
        }

        this.updateDisplay();
        this.saveToStorage();
    }

    removeItem(productId, productType) {
        this.items = this.items.filter(item => 
            !(item.id === productId && item.type === productType)
        );
        this.updateDisplay();
        this.saveToStorage();
    }

    updateQuantity(productId, productType, newQuantity) {
        const item = this.items.find(item => 
            item.id === productId && item.type === productType
        );
        
        if (item) {
            if (newQuantity > item.maxStock) {
                alert(`Stok ${item.name} tidak mencukupi!`);
                return;
            }
            
            if (newQuantity <= 0) {
                this.removeItem(productId, productType);
            } else {
                item.quantity = newQuantity;
                this.updateDisplay();
                this.saveToStorage();
            }
        }
    }

    calculateTotal() {
        return this.items.reduce((total, item) => {
            return total + (item.price * item.quantity);
        }, 0);
    }

    updateDisplay() {
        const cartContainer = document.getElementById('cart-items');
        const totalElement = document.getElementById('cart-total');
        
        // Update cart items display
        cartContainer.innerHTML = this.items.map(item => `
            <div class="cart-item" data-id="${item.id}" data-type="${item.type}">
                <div class="item-info">
                    <h6>${item.name}</h6>
                    <p>Rp ${item.price.toLocaleString('id-ID')}</p>
                </div>
                <div class="item-controls">
                    <button class="btn-decrease" onclick="cart.updateQuantity('${item.id}', '${item.type}', ${item.quantity - 1})">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="btn-increase" onclick="cart.updateQuantity('${item.id}', '${item.type}', ${item.quantity + 1})">+</button>
                </div>
                <div class="item-total">
                    Rp ${(item.price * item.quantity).toLocaleString('id-ID')}
                </div>
                <button class="btn-remove" onclick="cart.removeItem('${item.id}', '${item.type}')">×</button>
            </div>
        `).join('');

        // Update total
        this.total = this.calculateTotal();
        totalElement.textContent = `Rp ${this.total.toLocaleString('id-ID')}`;
        
        // Update checkout button state
        const checkoutBtn = document.getElementById('checkout-btn');
        checkoutBtn.disabled = this.items.length === 0;
    }

    saveToStorage() {
        localStorage.setItem('pos_cart', JSON.stringify(this.items));
    }

    loadFromStorage() {
        const saved = localStorage.getItem('pos_cart');
        if (saved) {
            this.items = JSON.parse(saved);
        }
    }

    clear() {
        this.items = [];
        this.updateDisplay();
        localStorage.removeItem('pos_cart');
    }
}

// Initialize cart
const cart = new POSCart();
```

---

## 💳 **PAYMENT PROCESSING**

### **Payment Methods:**
1. **💵 Cash** - Pembayaran tunai
2. **🏦 Transfer** - Transfer bank
3. **📱 QRIS** - QR Code payment
4. **💳 Debit Card** - Kartu debit
5. **💳 Credit Card** - Kartu kredit
6. **🌐 Payment Gateway** - Midtrans integration

### **Payment Form:**
```html
<!-- Payment Section -->
<div class="payment-section">
    <div class="customer-info">
        <input type="text" id="customer-name" placeholder="Nama Pelanggan" required>
        <input type="text" id="customer-phone" placeholder="No. Telepon (Opsional)">
    </div>
    
    <div class="payment-methods">
        <div class="payment-tabs">
            <button class="payment-tab active" data-method="cash">💵 Tunai</button>
            <button class="payment-tab" data-method="gateway">💳 Midtrans</button>
            <button class="payment-tab" data-method="transfer">🏦 Transfer</button>
            <button class="payment-tab" data-method="qris">📱 QRIS</button>
        </div>
        
        <div class="payment-content">
            <!-- Cash Payment -->
            <div class="payment-panel active" id="cash-panel">
                <div class="amount-input">
                    <label>Jumlah Bayar:</label>
                    <input type="number" id="amount-paid" placeholder="0" min="0">
                </div>
                <div class="change-display">
                    <label>Kembalian:</label>
                    <span id="change-amount">Rp 0</span>
                </div>
            </div>
            
            <!-- Gateway Payment -->
            <div class="payment-panel" id="gateway-panel">
                <p>Pembayaran akan diproses melalui Midtrans</p>
                <small>Mendukung: Credit Card, Bank Transfer, E-Wallet, QRIS</small>
            </div>
        </div>
    </div>
    
    <div class="checkout-actions">
        <button id="checkout-btn" class="btn-checkout" onclick="processCheckout()">
            Proses Pembayaran
        </button>
        <button class="btn-clear" onclick="cart.clear()">
            Bersihkan Keranjang
        </button>
    </div>
</div>
```

### **Checkout Process:**
```javascript
function processCheckout() {
    // Validate cart
    if (cart.items.length === 0) {
        alert('Keranjang kosong!');
        return;
    }

    // Validate customer name
    const customerName = document.getElementById('customer-name').value.trim();
    if (!customerName) {
        alert('Nama pelanggan harus diisi!');
        return;
    }

    // Get payment method
    const activeTab = document.querySelector('.payment-tab.active');
    const paymentMethod = activeTab.dataset.method;

    // Prepare transaction data
    const transactionData = {
        items: cart.items,
        customer_name: customerName,
        customer_phone: document.getElementById('customer-phone').value.trim(),
        payment_method: paymentMethod,
        use_payment_gateway: paymentMethod === 'gateway',
        _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    };

    // Add cash-specific data
    if (paymentMethod === 'cash') {
        const amountPaid = parseFloat(document.getElementById('amount-paid').value) || 0;
        if (amountPaid < cart.total) {
            alert('Jumlah bayar kurang dari total!');
            return;
        }
        transactionData.amount_paid = amountPaid;
    }

    // Show loading
    showLoading('Memproses transaksi...');

    // Submit transaction
    fetch('/transactions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': transactionData._token
        },
        body: JSON.stringify(transactionData)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            if (data.redirect_url) {
                // Redirect to payment gateway
                window.location.href = data.redirect_url;
            } else {
                // Show success and redirect to receipt
                alert('Transaksi berhasil!');
                window.location.href = data.receipt_url;
            }
            cart.clear();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        alert('Terjadi kesalahan saat memproses transaksi');
    });
}
```

---

## 🧾 **RECEIPT SYSTEM**

### **Receipt Generation:**
```php
// TransactionController@receipt
public function receipt(Transaction $transaction)
{
    $transaction->load(['items', 'user']);

    // Ensure all required data is available
    if (!$transaction->items || $transaction->items->isEmpty()) {
        return redirect()->route('transactions.index')
            ->with('error', 'Data transaksi tidak lengkap untuk dicetak');
    }

    return view('transactions.receipt', compact('transaction'));
}
```

### **Receipt Template:**
```html
<!-- Receipt Template -->
<div class="receipt-container">
    <div class="receipt-header">
        <h3>UBI BAKAR CILEMBU</h3>
        <p>Jl. Contoh No. 123, Kota, Indonesia</p>
        <p>Telp: (021) 1234-5678</p>
        <hr>
    </div>
    
    <div class="receipt-info">
        <p>No. Invoice: {{ $transaction->invoice_number }}</p>
        <p>Tanggal: {{ $transaction->created_at->format('d/m/Y H:i') }}</p>
        <p>Kasir: {{ $transaction->user->name }}</p>
        @if($transaction->customer_name)
            <p>Pelanggan: {{ $transaction->customer_name }}</p>
        @endif
        <hr>
    </div>
    
    <div class="receipt-items">
        @foreach($transaction->items as $item)
        <div class="receipt-item">
            <span class="item-name">{{ $item->product_name }}</span>
            <span class="item-qty">{{ $item->quantity }}x</span>
            <span class="item-price">{{ number_format($item->price, 0, ',', '.') }}</span>
            <span class="item-total">{{ number_format($item->subtotal, 0, ',', '.') }}</span>
        </div>
        @endforeach
        <hr>
    </div>
    
    <div class="receipt-summary">
        <div class="summary-line">
            <span>Subtotal:</span>
            <span>Rp {{ number_format($transaction->subtotal, 0, ',', '.') }}</span>
        </div>
        @if($transaction->discount > 0)
        <div class="summary-line">
            <span>Diskon:</span>
            <span>Rp {{ number_format($transaction->discount, 0, ',', '.') }}</span>
        </div>
        @endif
        @if($transaction->tax > 0)
        <div class="summary-line">
            <span>Pajak:</span>
            <span>Rp {{ number_format($transaction->tax, 0, ',', '.') }}</span>
        </div>
        @endif
        <div class="summary-line total">
            <span><strong>TOTAL:</strong></span>
            <span><strong>Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</strong></span>
        </div>
        
        @if($transaction->payment_method === 'cash')
        <div class="summary-line">
            <span>Bayar:</span>
            <span>Rp {{ number_format($transaction->amount_paid, 0, ',', '.') }}</span>
        </div>
        <div class="summary-line">
            <span>Kembalian:</span>
            <span>Rp {{ number_format($transaction->change_amount, 0, ',', '.') }}</span>
        </div>
        @endif
    </div>
    
    <div class="receipt-footer">
        <hr>
        <p class="text-center">Terima kasih atas kunjungan Anda!</p>
        <p class="text-center">Barang yang sudah dibeli tidak dapat dikembalikan</p>
    </div>
</div>

<!-- Print Actions -->
<div class="receipt-actions no-print">
    <button onclick="window.print()" class="btn btn-primary">
        <i class="fas fa-print"></i> Cetak Struk
    </button>
    <a href="{{ route('transactions.show', $transaction) }}" class="btn btn-secondary">
        <i class="fas fa-eye"></i> Lihat Detail
    </a>
    <a href="{{ route('pos') }}" class="btn btn-success">
        <i class="fas fa-plus"></i> Transaksi Baru
    </a>
</div>
```

---

## 📱 **MOBILE OPTIMIZATION**

### **Touch-Friendly Interface:**
- ✅ **Large buttons** untuk easy tapping
- ✅ **Swipe gestures** untuk navigation
- ✅ **Responsive grid** untuk product cards
- ✅ **Auto-zoom prevention** pada input fields

### **Mobile-Specific Features:**
```css
/* Mobile POS Styles */
@media (max-width: 768px) {
    .pos-container {
        flex-direction: column;
    }
    
    .products-panel {
        height: 60vh;
        overflow-y: auto;
    }
    
    .cart-panel {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #ddd;
        max-height: 40vh;
    }
    
    .product-card {
        min-height: 120px;
        touch-action: manipulation;
    }
    
    .btn-add-to-cart {
        min-height: 44px; /* iOS minimum touch target */
        min-width: 44px;
    }
}
```

---

## 🔄 **REAL-TIME FEATURES**

### **Stock Validation:**
```javascript
// Real-time stock checking
function validateStock(productId, productType, requestedQuantity) {
    return fetch(`/api/products/${productType}/${productId}/stock`)
        .then(response => response.json())
        .then(data => {
            if (data.current_stock < requestedQuantity) {
                throw new Error(`Stok tidak mencukupi. Tersedia: ${data.current_stock}`);
            }
            return true;
        });
}
```

### **Live Price Updates:**
```javascript
// Auto-update prices if changed
function checkPriceUpdates() {
    const productIds = cart.items.map(item => ({id: item.id, type: item.type}));
    
    fetch('/api/products/prices', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({products: productIds})
    })
    .then(response => response.json())
    .then(prices => {
        let hasChanges = false;
        
        cart.items.forEach(item => {
            const newPrice = prices[`${item.type}_${item.id}`];
            if (newPrice && newPrice !== item.price) {
                item.price = newPrice;
                hasChanges = true;
            }
        });
        
        if (hasChanges) {
            cart.updateDisplay();
            alert('Harga beberapa produk telah diperbarui');
        }
    });
}

// Check every 5 minutes
setInterval(checkPriceUpdates, 300000);
```

---

## 🎯 **PERFORMANCE OPTIMIZATION**

### **Frontend Optimization:**
- ✅ **Lazy loading** untuk product images
- ✅ **Virtual scrolling** untuk large product lists
- ✅ **Debounced search** untuk product filtering
- ✅ **Local storage** untuk cart persistence

### **Backend Optimization:**
- ✅ **Eager loading** untuk related data
- ✅ **Database indexing** pada frequently queried fields
- ✅ **Caching** untuk product data
- ✅ **Query optimization** untuk better performance

---

---

## 🔧 **TROUBLESHOOTING**

### **Common Issues:**
1. **Cart not updating** - Check localStorage permissions
2. **Payment gateway timeout** - Verify Midtrans configuration
3. **Stock validation errors** - Refresh product data
4. **Receipt printing issues** - Check browser print settings

### **Debug Commands:**
```javascript
// Clear cart data
localStorage.removeItem('pos_cart');

// Check cart state
console.log(cart.items);

// Validate product data
fetch('/api/products/validate').then(r => r.json()).then(console.log);
```

---

**💰 POS Performance:** Optimized
**📱 Mobile Support:** Full Touch-Friendly
**💳 Payment Methods:** 6 Options Available
**🧾 Receipt System:** Digital & Print Ready
