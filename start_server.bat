@echo off
echo 🚀 Starting Ubi Bakar Cilembu Development Server...
echo.

echo 📋 Checking requirements...
php --version
echo.

echo 🔧 Clearing cache...
php artisan config:clear
php artisan cache:clear
php artisan route:clear
echo.

echo 🌐 Starting Laravel development server...
echo 📱 POS System: http://localhost:8000/pos
echo 🏠 Dashboard: http://localhost:8000/dashboard
echo 💳 Payment Gateway: Ready with Midtrans Sandbox
echo.
echo Press Ctrl+C to stop the server
echo.

php artisan serve --host=0.0.0.0 --port=8000
