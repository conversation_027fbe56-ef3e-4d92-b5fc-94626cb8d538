<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('action'); // create, update, delete, restore
            $table->string('model_type'); // nama model yang diubah (RawInventory, ProcessedInventory, dll)
            $table->unsignedBigInteger('model_id'); // ID dari model yang diubah
            $table->text('old_values')->nullable(); // nilai lama dalam format JSON
            $table->text('new_values')->nullable(); // nilai baru dalam format JSON
            $table->string('ip_address')->nullable(); // alamat IP pengguna
            $table->string('user_agent')->nullable(); // user agent pengguna
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->index(['model_type', 'model_id']);
            $table->index('action');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audit_logs');
    }
};
