<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pembayaran Selesai - Ubi Bakar Cilembu</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Immediate loading - no flash */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            overflow-x: hidden;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            /* Prevent flash of unstyled content */
            opacity: 0;
            animation: fadeIn 0.3s ease-out forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
            }
        }

        /* Loading overlay for smooth transition */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 1;
            transition: opacity 0.5s ease-out;
        }

        .loading-overlay.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .payment-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
            opacity: 0;
            animation-fill-mode: forwards;
            animation-delay: 0.3s;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .success-icon {
            color: #28a745;
            font-size: 5rem;
            animation: bounce 1s ease-in-out;
            animation-delay: 0.6s;
            animation-fill-mode: forwards;
        }

        @keyframes bounce {
            0%, 20%, 60%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            80% {
                transform: translateY(-5px);
            }
        }

        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* Hide content initially */
        .main-content {
            opacity: 0;
            animation: showContent 0.5s ease-out forwards;
            animation-delay: 0.5s;
        }

        @keyframes showContent {
            to {
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <!-- Loading overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center text-white">
            <div class="loading-spinner mb-3"></div>
            <h5>Memproses pembayaran...</h5>
            <p class="mb-0">Mohon tunggu sebentar</p>
        </div>
    </div>

    <!-- Main content -->
    <div class="container main-content">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="payment-card">
                    <div class="card-body text-center p-5">
                        @if(isset($success) && $success)
                            <!-- Success State -->
                            <div class="mb-4">
                                <i class="fas fa-check-circle success-icon"></i>
                            </div>

                            <h2 class="text-success mb-3 fw-bold">Pembayaran Berhasil!</h2>

                            @if(isset($transaction))
                                <div class="mb-4">
                                    <div class="bg-light rounded p-3 mb-3">
                                        <small class="text-muted d-block">Invoice Number</small>
                                        <strong class="text-dark">{{ $transaction->invoice_number }}</strong>
                                    </div>
                                    <div class="bg-light rounded p-3">
                                        <small class="text-muted d-block">Total Pembayaran</small>
                                        <strong class="text-dark">Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</strong>
                                    </div>
                                </div>
                            @endif

                            <p class="text-muted mb-4">
                                Terima kasih! Pembayaran Anda telah berhasil diproses dan transaksi telah diperbarui.
                            </p>
                        @else
                            <!-- Other States -->
                            <div class="mb-4">
                                <i class="fas fa-info-circle text-warning" style="font-size: 4rem;"></i>
                            </div>

                            <h3 class="text-warning mb-3">Pembayaran Diproses</h3>

                            @if(isset($order_id))
                                <p class="mb-3">
                                    <strong>Order ID:</strong> {{ $order_id }}
                                </p>
                            @endif

                            <p class="text-muted mb-4">
                                Pembayaran Anda sedang diproses. Silakan tunggu konfirmasi lebih lanjut.
                            </p>
                        @endif

                        <div class="d-grid gap-3">
                            <a href="{{ route('pos') }}" class="btn btn-primary btn-custom">
                                <i class="fas fa-cash-register me-2"></i>
                                Kembali ke POS
                            </a>
                            <a href="{{ route('transactions.index') }}" class="btn btn-outline-secondary btn-custom">
                                <i class="fas fa-list me-2"></i>
                                Lihat Transaksi
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Hide loading overlay immediately when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.classList.add('hidden');
                }
            }, 800); // Small delay for smooth transition
        });

        // Also hide on window load as backup
        window.addEventListener('load', function() {
            setTimeout(() => {
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (loadingOverlay) {
                    loadingOverlay.classList.add('hidden');
                }
            }, 500);
        });

        // Force hide after 2 seconds maximum
        setTimeout(() => {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        }, 2000);
    </script>

    <!-- Auto redirect to POS after 10 seconds for successful payments -->
    @if(isset($success) && $success)
    <script>
        let countdown = 10;
        const countdownElement = document.createElement('div');
        countdownElement.className = 'mt-3 text-muted small';
        countdownElement.innerHTML = `Otomatis kembali ke POS dalam <span id="countdown">${countdown}</span> detik`;
        document.querySelector('.d-grid').appendChild(countdownElement);

        const timer = setInterval(() => {
            countdown--;
            document.getElementById('countdown').textContent = countdown;

            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = "{{ route('pos') }}";
            }
        }, 1000);
    </script>
    @endif
</body>
</html>
