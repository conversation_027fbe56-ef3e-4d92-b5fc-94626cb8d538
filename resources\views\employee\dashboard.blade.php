@extends('layouts.app')

@section('content')
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-tachometer-alt"></i> Dashboard Karyawan</h1>
        <div>
            <a href="{{ route('pos') }}" class="btn btn-primary">
                <i class="fas fa-cash-register"></i> Kasir (POS)
            </a>
        </div>
    </div>

    <!-- <PERSON><PERSON><PERSON> -->
    <div class="row mb-4">
        <div class="col-md-4 mb-4">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Penjualan Hari Ini</div>
                    <h3 class="stats-value">{{ $summary['today_sales_count'] }}</h3>
                    <div class="stats-desc">Rp {{ number_format($summary['today_sales_amount'], 0, ',', '.') }}</div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="stats-card">
                <div class="stats-icon warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Stok Hampir Habis</div>
                    <h3 class="stats-value">{{ $summary['low_stock_count'] }}</h3>
                    <div class="stats-desc">Produk perlu diisi ulang</div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="stats-card">
                <div class="stats-icon info">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Total Produk</div>
                    <h3 class="stats-value">{{ $summary['processed_inventory_count'] + $summary['other_products_count'] }}</h3>
                    <div class="stats-desc">{{ $summary['processed_inventory_count'] }} Ubi & {{ $summary['other_products_count'] }} Produk Lain</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Grafik dan Produk Terlaris -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Grafik Penjualan 7 Hari Terakhir</span>
                </div>
                <div class="card-body">
                    <canvas id="weeklySalesChart" height="250"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <span>Produk Terlaris</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>Produk</th>
                                    <th>Terjual</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($topProducts as $product)
                                <tr>
                                    <td>{{ $product->product_name }}</td>
                                    <td>{{ $product->total_sold }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="2" class="text-center">Belum ada data penjualan</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stok Umbi Bakar -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Stok Umbi Bakar</span>
                    <a href="{{ route('processed-inventory.index') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-list"></i> Lihat Semua
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>Nama Produk</th>
                                    <th>Stok</th>
                                    <th>Status</th>
                                    <th>Harga</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($processedInventory as $item)
                                <tr>
                                    <td>{{ $item->name }}</td>
                                    <td>{{ $item->current_stock }}</td>
                                    <td>
                                        @if($item->current_stock <= $item->min_stock_threshold)
                                            <span class="badge bg-danger">Stok Rendah</span>
                                        @else
                                            <span class="badge bg-success">Stok Cukup</span>
                                        @endif
                                    </td>
                                    <td>Rp {{ number_format($item->selling_price, 0, ',', '.') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center">Belum ada data produk</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stok Produk Lain -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Stok Produk Lain</span>
                    <a href="{{ route('other-products.index') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-list"></i> Lihat Semua
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>Nama Produk</th>
                                    <th>Stok</th>
                                    <th>Status</th>
                                    <th>Harga</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($otherProducts as $item)
                                <tr>
                                    <td>{{ $item->name }}</td>
                                    <td>{{ $item->current_stock }}</td>
                                    <td>
                                        @if($item->current_stock <= $item->min_stock_threshold)
                                            <span class="badge bg-danger">Stok Rendah</span>
                                        @else
                                            <span class="badge bg-success">Stok Cukup</span>
                                        @endif
                                    </td>
                                    <td>Rp {{ number_format($item->selling_price, 0, ',', '.') }}</td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center">Belum ada data produk</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaksi Terbaru dan Aksi Cepat -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <span>Transaksi Terbaru Hari Ini</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>No. Invoice</th>
                                    <th>Waktu</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentTransactions as $transaction)
                                <tr>
                                    <td>
                                        <a href="{{ route('transactions.show', $transaction->id) }}">
                                            {{ $transaction->invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ $transaction->created_at->format('H:i') }}</td>
                                    <td>Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</td>
                                    <td>
                                        @if($transaction->status == 'completed')
                                            <span class="badge bg-success">Selesai</span>
                                        @elseif($transaction->status == 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @else
                                            <span class="badge bg-danger">{{ ucfirst($transaction->status) }}</span>
                                        @endif
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="4" class="text-center">Belum ada transaksi hari ini</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <span>Aksi Cepat</span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="{{ route('pos') }}" class="btn btn-primary w-100 p-3">
                                <i class="fas fa-cash-register fa-2x mb-2"></i>
                                <div>Kasir (POS)</div>
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{{ route('transactions.index') }}" class="btn btn-info w-100 p-3">
                                <i class="fas fa-receipt fa-2x mb-2"></i>
                                <div>Riwayat Transaksi</div>
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{{ route('processed-inventory.index') }}" class="btn btn-success w-100 p-3">
                                <i class="fas fa-boxes fa-2x mb-2"></i>
                                <div>Stok Umbi Bakar</div>
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <a href="{{ route('other-products.index') }}" class="btn btn-warning w-100 p-3">
                                <i class="fas fa-box-open fa-2x mb-2"></i>
                                <div>Produk Lainnya</div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Weekly Sales Chart
        const salesChartData = @json($salesChartData);
        const salesChart = new Chart(document.getElementById('weeklySalesChart'), {
            type: 'line',
            data: {
                labels: salesChartData.labels,
                datasets: [{
                    label: 'Pendapatan',
                    data: salesChartData.revenue,
                    backgroundColor: 'rgba(139, 69, 19, 0.2)',
                    borderColor: 'rgba(139, 69, 19, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }, {
                    label: 'Transaksi',
                    data: salesChartData.transactions,
                    backgroundColor: 'rgba(255, 140, 0, 0.2)',
                    borderColor: 'rgba(255, 140, 0, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Pendapatan (Rp)'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + value.toLocaleString('id-ID');
                            }
                        }
                    },
                    y1: {
                        beginAtZero: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Jumlah Transaksi'
                        },
                        grid: {
                            drawOnChartArea: false
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.dataset.label === 'Pendapatan') {
                                    label += 'Rp ' + context.parsed.y.toLocaleString('id-ID');
                                } else {
                                    label += context.parsed.y;
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
