@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-user-edit me-2"></i> Edit User: {{ $user->name }}</h1>
        <div>
            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user-edit me-2"></i> Form Edit User</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.users.update', $user) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label"><PERSON>a <PERSON> <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                    <select class="form-select @error('role') is-invalid @enderror" id="role" name="role" required>
                                        <option value="">Pilih Role</option>
                                        <option value="admin" {{ old('role', $user->role) === 'admin' ? 'selected' : '' }}>Admin</option>
                                        <option value="employee" {{ old('role', $user->role) === 'employee' ? 'selected' : '' }}>Karyawan</option>
                                        <option value="cashier" {{ old('role', $user->role) === 'cashier' ? 'selected' : '' }}>Kasir</option>
                                        <option value="warehouse" {{ old('role', $user->role) === 'warehouse' ? 'selected' : '' }}>Gudang</option>
                                    </select>
                                    @error('role')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="current_status" class="form-label">Status Saat Ini</label>
                                    <div class="form-control-plaintext">
                                        @if($user->status === 'pending')
                                            <span class="badge bg-warning">Pending</span>
                                        @elseif($user->status === 'approved')
                                            <span class="badge bg-success">Approved</span>
                                        @elseif($user->status === 'rejected')
                                            <span class="badge bg-danger">Rejected</span>
                                        @endif
                                        
                                        @if($user->approver)
                                            <br><small class="text-muted">
                                                Oleh: {{ $user->approver->name }} 
                                                ({{ $user->approved_at?->format('d/m/Y H:i') }})
                                            </small>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password Baru</label>
                                    <div class="position-relative">
                                        <input type="password" class="form-control pe-5 @error('password') is-invalid @enderror"
                                               id="password" name="password">
                                        <span class="position-absolute top-50 end-0 translate-middle-y me-3 cursor-pointer text-muted"
                                              onclick="togglePasswordVisibility('password', this)" style="cursor: pointer;">
                                            <i class="fas fa-eye"></i>
                                        </span>
                                    </div>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Kosongkan jika tidak ingin mengubah password</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">Konfirmasi Password Baru</label>
                                    <div class="position-relative">
                                        <input type="password" class="form-control pe-5"
                                               id="password_confirmation" name="password_confirmation">
                                        <span class="position-absolute top-50 end-0 translate-middle-y me-3 cursor-pointer text-muted"
                                              onclick="togglePasswordVisibility('password_confirmation', this)" style="cursor: pointer;">
                                            <i class="fas fa-eye"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- User Info -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Informasi User:</strong>
                            <ul class="mb-0 mt-2">
                                <li>Terdaftar: {{ $user->created_at->format('d/m/Y H:i') }}</li>
                                <li>Terakhir aktif: {{ $user->last_activity ? $user->last_activity->format('d/m/Y H:i') : 'Belum pernah login' }}</li>
                                @if($user->id === auth()->id())
                                    <li><strong>Ini adalah akun Anda sendiri</strong></li>
                                @endif
                            </ul>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function togglePasswordVisibility(inputId, toggleElement) {
    const passwordInput = document.getElementById(inputId);
    const icon = toggleElement.querySelector('i');

    if (passwordInput && icon) {
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }
}
</script>
@endpush
@endsection
