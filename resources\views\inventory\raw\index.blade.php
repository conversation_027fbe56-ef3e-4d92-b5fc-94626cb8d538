@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-seedling"></i>
        <span>Stok Ubi Mentah</span>
    </div>

    @if($lowStock->count() > 0)
    <div class="alert alert-warning mb-4">
        <h5><i class="fas fa-exclamation-triangle"></i> Peringatan Stok Menipis</h5>
        <p class="mb-0">
            Beberapa item stok ubi mentah sudah hampir habis:
            <ul class="mb-0">
                @foreach($lowStock as $item)
                <li>{{ $item->name }} ({{ number_format($item->current_stock, 2, ',', '.') }} kg)</li>
                @endforeach
            </ul>
        </p>
    </div>
    @endif

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Daftar Stok Ubi Mentah</span>
                    <a href="{{ route('raw-inventory.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah Stok Baru
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>No.</th>
                                    <th>Nama Ubi</th>
                                    <th>Batch</th>
                                    <th>Supplier</th>
                                    <th>Kualitas</th>
                                    <th>Stok (kg)</th>
                                    <th>Harga/kg</th>
                                    <th>Total Nilai</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($rawInventory as $item)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $item->name }}</td>
                                    <td>{{ $item->batch_number }}</td>
                                    <td>{{ $item->supplier_name ?: '-' }}</td>
                                    <td>
                                        @if($item->quality == 'A')
                                            <span class="badge bg-success">A</span>
                                        @elseif($item->quality == 'B')
                                            <span class="badge bg-info">B</span>
                                        @else
                                            <span class="badge bg-warning">C</span>
                                        @endif
                                    </td>
                                    <td>
                                        {{ number_format($item->current_stock, 2, ',', '.') }}
                                        @if($item->current_stock <= $item->min_stock_threshold)
                                            <span class="badge bg-danger ms-1">!</span>
                                        @endif
                                    </td>
                                    <td>Rp {{ number_format($item->cost_per_kg, 0, ',', '.') }}</td>
                                    <td>Rp {{ number_format($item->total_cost, 0, ',', '.') }}</td>
                                    <td>
                                        @if($item->is_active)
                                            <span class="badge bg-success">Aktif</span>
                                        @else
                                            <span class="badge bg-danger">Tidak Aktif</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('raw-inventory.show', $item) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('raw-inventory.edit', $item) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addStockModal{{ $item->id }}">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete('{{ $item->id }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal Tambah Stok -->
                                        <div class="modal fade" id="addStockModal{{ $item->id }}" tabindex="-1" aria-labelledby="addStockModalLabel{{ $item->id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="addStockModalLabel{{ $item->id }}">Tambah Stok - {{ $item->name }}</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <form action="{{ route('raw-inventory.add-stock', $item) }}" method="POST">
                                                        @csrf
                                                        <div class="modal-body">
                                                            <div class="mb-3">
                                                                <label for="additional_stock" class="form-label">Jumlah Tambahan (kg) <span class="text-danger">*</span></label>
                                                                <input type="number" class="form-control" id="additional_stock" name="additional_stock" step="0.1" min="0.1" required>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label for="cost_per_kg" class="form-label">Harga per kg (Rp) <span class="text-danger">*</span></label>
                                                                <input type="number" class="form-control" id="cost_per_kg" name="cost_per_kg" value="{{ $item->cost_per_kg }}" min="0" required>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                            <button type="submit" class="btn btn-success">Tambah Stok</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Form Delete -->
                                        <form id="delete-form-{{ $item->id }}" action="{{ route('raw-inventory.destroy', $item) }}" method="POST" class="d-none">
                                            @csrf
                                            @method('DELETE')
                                        </form>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="10" class="text-center">Tidak ada data stok ubi mentah.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function confirmDelete(id) {
    if (confirm('Apakah Anda yakin ingin menghapus data ini?')) {
        document.getElementById('delete-form-' + id).submit();
    }
}
</script>
@endpush
@endsection 