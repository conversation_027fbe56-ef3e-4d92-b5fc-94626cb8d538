<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_processes', function (Blueprint $table) {
            $table->id();
            $table->string('batch_number')->unique();
            $table->foreignId('user_id')->constrained();
            $table->foreignId('raw_inventory_id')->constrained('raw_inventory');
            $table->decimal('raw_quantity_used', 10, 2);
            $table->integer('processed_quantity_produced');
            $table->decimal('production_cost', 12, 2);
            $table->date('production_date');
            $table->enum('status', ['planned', 'in_progress', 'completed', 'failed'])->default('planned');
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            // Add indexes for performance
            $table->index('production_date');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_processes');
    }
};
