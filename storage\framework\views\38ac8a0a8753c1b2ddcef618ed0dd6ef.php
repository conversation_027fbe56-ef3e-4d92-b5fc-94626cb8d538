<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-user me-2"></i> Detail User: <?php echo e($user->name); ?></h1>
        <div>
            <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
            <a href="<?php echo e(route('admin.users.edit', $user)); ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
        </div>
    </div>

    <div class="row">
        <!-- User Information -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-user me-2"></i> Informasi User</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>Nama Lengkap:</strong></td>
                                    <td><?php echo e($user->name); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td><?php echo e($user->email); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Role:</strong></td>
                                    <td>
                                        <?php if($user->role === 'admin'): ?>
                                            <span class="badge bg-danger">Admin</span>
                                        <?php elseif($user->role === 'employee'): ?>
                                            <span class="badge bg-primary">Karyawan</span>
                                        <?php elseif($user->role === 'cashier'): ?>
                                            <span class="badge bg-success">Kasir</span>
                                        <?php elseif($user->role === 'warehouse'): ?>
                                            <span class="badge bg-warning">Gudang</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge bg-success">Aktif</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td width="150"><strong>Tanggal Daftar:</strong></td>
                                    <td><?php echo e($user->created_at->format('d/m/Y H:i')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Terakhir Update:</strong></td>
                                    <td><?php echo e($user->updated_at->format('d/m/Y H:i')); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Terakhir Aktif:</strong></td>
                                    <td>
                                        <?php if($user->last_activity): ?>
                                            <?php echo e($user->last_activity->format('d/m/Y H:i')); ?>

                                        <?php else: ?>
                                            <span class="text-muted">Belum pernah login</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php if($user->approver): ?>
                                <tr>
                                    <td><strong>Dibuat Oleh:</strong></td>
                                    <td>
                                        <?php echo e($user->approver->name); ?>

                                        <br><small class="text-muted"><?php echo e($user->approved_at?->format('d/m/Y H:i')); ?></small>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs me-2"></i> Aksi</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.users.edit', $user)); ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit User
                        </a>
                        
                        <?php if($user->id !== auth()->id()): ?>
                            <form method="POST" action="<?php echo e(route('admin.users.destroy', $user)); ?>">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" class="btn btn-danger w-100" 
                                        onclick="return confirm('Hapus user ini? Aksi ini tidak dapat dibatalkan!')">
                                    <i class="fas fa-trash"></i> Hapus User
                                </button>
                            </form>
                        <?php else: ?>
                            <button class="btn btn-danger w-100" disabled>
                                <i class="fas fa-trash"></i> Tidak dapat menghapus akun sendiri
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar me-2"></i> Statistik</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-12 mb-3">
                            <div class="border rounded p-2">
                                <h4 class="text-primary mb-0"><?php echo e($user->approvedUsers->count()); ?></h4>
                                <small class="text-muted">User yang Disetujui</small>
                            </div>
                        </div>
                    </div>
                    
                    <?php if($user->approvedUsers->count() > 0): ?>
                        <hr>
                        <h6>User yang Pernah Disetujui:</h6>
                        <ul class="list-unstyled">
                            <?php $__currentLoopData = $user->approvedUsers->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $approvedUser): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="mb-1">
                                    <small>
                                        <i class="fas fa-user me-1"></i>
                                        <?php echo e($approvedUser->name); ?>

                                        <span class="text-muted">(<?php echo e($approvedUser->approved_at?->format('d/m/Y')); ?>)</span>
                                    </small>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($user->approvedUsers->count() > 5): ?>
                                <li><small class="text-muted">... dan <?php echo e($user->approvedUsers->count() - 5); ?> lainnya</small></li>
                            <?php endif; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\ubi-bakar-cilembu - Copy\resources\views/admin/users/show.blade.php ENDPATH**/ ?>