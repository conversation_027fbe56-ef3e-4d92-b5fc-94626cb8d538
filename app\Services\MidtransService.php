<?php

namespace App\Services;

use App\Models\Transaction;
use Illuminate\Support\Facades\Log;
use Midtrans\Config;
use Midtrans\Snap;

class MidtransService
{
    public function __construct()
    {
        // Configuration will be set in createSnapToken method to prevent conflicts
    }

    /**
     * Create Snap token for payment
     */
    public function createSnapToken(Transaction $transaction)
    {
        try {
            Log::info('Creating Midtrans Snap token', [
                'transaction_id' => $transaction->id,
                'total_amount' => $transaction->total_amount
            ]);

            // Configure Midtrans first
            Config::$serverKey = config('midtrans.server_key');
            Config::$isProduction = config('midtrans.is_production');
            Config::$isSanitized = config('midtrans.is_sanitized');
            Config::$is3ds = config('midtrans.is_3ds');

            // Initialize curlOptions properly to prevent array key errors
            Config::$curlOptions = [
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_HTTPHEADER => [] // Initialize empty array to prevent undefined key error
            ];

            // Validate Midtrans configuration
            if (empty(Config::$serverKey)) {
                throw new \Exception('Midtrans server key is not configured');
            }

            // Build transaction parameters
            $params = $this->buildTransactionParams($transaction);

            // Create snap token with error handling
            Log::info('Calling Midtrans Snap::getSnapToken', [
                'params_structure' => [
                    'order_id' => $params['transaction_details']['order_id'],
                    'gross_amount' => $params['transaction_details']['gross_amount'],
                    'items_count' => count($params['item_details']),
                    'customer_name' => $params['customer_details']['first_name']
                ]
            ]);

            // Try to create snap token with retry mechanism
            $maxRetries = 3;
            $snapToken = null;
            $lastError = null;

            for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
                try {
                    Log::info("Creating Snap token attempt {$attempt}", [
                        'transaction_id' => $transaction->id,
                        'order_id' => $params['transaction_details']['order_id']
                    ]);

                    $snapToken = Snap::getSnapToken($params);
                    break; // Success, exit retry loop

                } catch (\Exception $e) {
                    $lastError = $e;
                    Log::warning("Snap token creation attempt {$attempt} failed", [
                        'transaction_id' => $transaction->id,
                        'error' => $e->getMessage(),
                        'attempt' => $attempt
                    ]);

                    if ($attempt < $maxRetries) {
                        sleep(1); // Wait 1 second before retry
                    }
                }
            }

            if (!$snapToken) {
                throw new \Exception("Failed to create Snap token after {$maxRetries} attempts: " . $lastError->getMessage());
            }

            $redirectUrl = $this->getSnapRedirectUrl($snapToken);

            // Update transaction with all gateway information
            $transaction->update([
                'payment_gateway' => 'midtrans',
                'payment_gateway_order_id' => $params['transaction_details']['order_id'],
                'snap_token' => $snapToken,
                'snap_redirect_url' => $redirectUrl,
                'status' => 'pending',
                'payment_status' => 'pending'
            ]);

            Log::info('Snap token created successfully', [
                'transaction_id' => $transaction->id,
                'order_id' => $params['transaction_details']['order_id'],
                'snap_token' => substr($snapToken, 0, 20) . '...',
                'redirect_url' => $redirectUrl
            ]);

            return [
                'success' => true,
                'snap_token' => $snapToken,
                'redirect_url' => $redirectUrl
            ];

        } catch (\Exception $e) {
            Log::error('Failed to create Snap token', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'error_class' => get_class($e),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString()
            ]);

            // Handle specific error cases
            $errorMessage = $e->getMessage();

            if (strpos($errorMessage, 'Undefined array key') !== false) {
                $errorMessage = 'Terjadi kesalahan dalam data transaksi. Silakan coba lagi dengan produk yang berbeda.';
            } elseif (strpos($errorMessage, 'ServerKey') !== false) {
                $errorMessage = 'Konfigurasi payment gateway bermasalah. Silakan hubungi administrator.';
            } elseif (strpos($errorMessage, 'network') !== false || strpos($errorMessage, 'timeout') !== false) {
                $errorMessage = 'Koneksi ke payment gateway bermasalah. Silakan coba lagi.';
            }

            return [
                'success' => false,
                'message' => 'Gagal membuat token pembayaran: ' . $errorMessage
            ];
        }
    }

    /**
     * Build transaction parameters for Midtrans
     */
    private function buildTransactionParams(Transaction $transaction)
    {
        try {
            $orderId = 'UBI-' . $transaction->id . '-' . time();

            Log::info('Building transaction params', [
                'transaction_id' => $transaction->id,
                'order_id' => $orderId,
                'items_count' => $transaction->items->count()
            ]);

            // Build item details with validation
            $itemDetails = [];
            foreach ($transaction->items as $index => $item) {
                try {
                    // Validate item data
                    if (!$item->product_name || !$item->price || !$item->quantity) {
                        Log::warning('Invalid item data', [
                            'item_id' => $item->id,
                            'product_name' => $item->product_name,
                            'price' => $item->price,
                            'quantity' => $item->quantity
                        ]);
                        continue;
                    }

                    // Clean product name (remove special characters that might cause issues)
                    $productName = preg_replace('/[^a-zA-Z0-9\s\-\.]/', '', $item->product_name);
                    $productName = trim($productName) ?: 'Product';

                    $itemDetails[] = [
                        'id' => 'ITEM-' . $item->id,
                        'price' => (int) $item->price,
                        'quantity' => (int) $item->quantity,
                        'name' => substr($productName, 0, 50), // Limit name length and use cleaned name
                        'category' => 'Food'
                    ];

                    Log::debug('Item processed for Midtrans', [
                        'item_id' => $item->id,
                        'name' => $item->product_name,
                        'price' => $item->price,
                        'quantity' => $item->quantity
                    ]);

                } catch (\Exception $e) {
                    Log::error('Error processing item for Midtrans', [
                        'item_id' => $item->id ?? 'unknown',
                        'index' => $index,
                        'error' => $e->getMessage()
                    ]);
                    continue;
                }
            }

            if (empty($itemDetails)) {
                throw new \Exception('No valid items found for transaction');
            }

            // FIXED: Handle empty/invalid customer data that causes Midtrans 500 error
            $customerName = $transaction->customer_name;
            $customerPhone = $transaction->customer_phone;

            // Fix empty or invalid customer name
            if (empty($customerName) || strlen(trim($customerName)) < 2) {
                $customerName = 'Customer';
            } else {
                // Remove special characters that cause Midtrans issues
                $customerName = preg_replace('/[^a-zA-Z0-9\s]/', '', $customerName);
                $customerName = trim($customerName) ?: 'Customer';
            }

            // Fix empty or invalid phone number (MAIN CAUSE OF 500 ERROR)
            if (empty($customerPhone)) {
                $customerPhone = '081234567890';
            } else {
                // Clean phone number
                $customerPhone = preg_replace('/[^0-9]/', '', $customerPhone);
                if (strlen($customerPhone) < 10) {
                    $customerPhone = '081234567890';
                }
            }

            $customerDetails = [
                'first_name' => substr($customerName, 0, 20),
                'phone' => $customerPhone,
                'email' => '<EMAIL>'
            ];

            $params = [
                'transaction_details' => [
                    'order_id' => $orderId,
                    'gross_amount' => (int) $transaction->total_amount,
                ],
                'item_details' => $itemDetails,
                'customer_details' => $customerDetails,
                'enabled_payments' => [
                    'bca_va', 'bni_va', 'bri_va', 'mandiri_va',
                    'echannel', 'permata_va', 'other_va',
                    'qris', 'gopay', 'shopeepay',
                    'credit_card' // Move credit card to last as it might have issues
                ],
                'callbacks' => [
                    'finish' => config('app.url') . '/payment/finish',
                    'unfinish' => config('app.url') . '/payment/unfinish',
                    'error' => config('app.url') . '/payment/error'
                ],
                'expiry' => [
                    'start_time' => date('Y-m-d H:i:s O'),
                    'unit' => 'minutes',
                    'duration' => 60
                ],
                'page_expiry' => [
                    'duration' => 60,
                    'unit' => 'minutes'
                ]
            ];

            Log::info('Transaction params built successfully', [
                'order_id' => $orderId,
                'gross_amount' => $transaction->total_amount,
                'items_count' => count($itemDetails)
            ]);

            return $params;

        } catch (\Exception $e) {
            Log::error('Error building transaction params', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get Snap redirect URL
     */
    private function getSnapRedirectUrl($snapToken)
    {
        $baseUrl = config('midtrans.is_production') 
            ? 'https://app.midtrans.com/snap/v2/vtweb/' 
            : 'https://app.sandbox.midtrans.com/snap/v2/vtweb/';
            
        return $baseUrl . $snapToken;
    }

    /**
     * Handle notification from Midtrans
     */
    public function handleNotification($notification)
    {
        try {
            $orderId = $notification['order_id'];
            $statusCode = $notification['status_code'];
            $grossAmount = $notification['gross_amount'];
            $transactionStatus = $notification['transaction_status'];

            // Extract transaction ID from order ID
            $transactionId = explode('-', $orderId)[1];
            $transaction = Transaction::find($transactionId);

            if (!$transaction) {
                Log::error('Transaction not found for notification', ['order_id' => $orderId]);
                return false;
            }

            // Update transaction status based on Midtrans status
            $updateData = [
                'payment_gateway_transaction_id' => $notification['transaction_id'] ?? null,
                'payment_gateway_status' => $transactionStatus,
                'payment_gateway_response' => json_encode($notification)
            ];

            if ($transactionStatus == 'capture' || $transactionStatus == 'settlement') {
                $updateData = array_merge($updateData, [
                    'status' => 'completed',
                    'payment_status' => 'paid',
                    'amount_paid' => $transaction->total_amount,
                    'change_amount' => 0,
                    'payment_gateway_paid_at' => now()
                ]);
            } elseif ($transactionStatus == 'pending') {
                $updateData = array_merge($updateData, [
                    'status' => 'pending',
                    'payment_status' => 'pending'
                ]);
            } elseif ($transactionStatus == 'deny' || $transactionStatus == 'cancel') {
                $updateData = array_merge($updateData, [
                    'status' => 'cancelled',
                    'payment_status' => 'cancelled'
                ]);
            } elseif ($transactionStatus == 'expire') {
                $updateData = array_merge($updateData, [
                    'status' => 'cancelled',
                    'payment_status' => 'expired',
                    'payment_gateway_expired_at' => now()
                ]);
            } elseif ($transactionStatus == 'failure') {
                $updateData = array_merge($updateData, [
                    'status' => 'cancelled',
                    'payment_status' => 'failed'
                ]);
            }

            $transaction->update($updateData);

            Log::info('Notification processed successfully', [
                'order_id' => $orderId,
                'transaction_status' => $transactionStatus,
                'transaction_id' => $transaction->id
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to process notification', [
                'error' => $e->getMessage(),
                'notification' => $notification
            ]);
            return false;
        }
    }
}
